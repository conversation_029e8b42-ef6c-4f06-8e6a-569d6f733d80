import { useSearchContacts } from '@/apis/contact/contact.api';
import { Option } from '@/types/app.type';
import { useMemo } from 'react';

const useGetOptionPeopleContactId = (id: string) => {
    const { data } = useSearchContacts({
        CompanyId: id,
    });

    const contacts = useMemo<Option[]>(() => {
        if (Array.isArray(data?.items) && data?.items.length > 0) {
            const uniquePeople = data.items.reduce(
                (acc, current) => {
                    const existingPerson = acc.find(
                        (item) => item.name === current.name,
                    );
                    if (!existingPerson) {
                        acc.push(current);
                    }
                    return acc;
                },
                [] as typeof data.items,
            );

            return uniquePeople.map((parent) => ({
                label: parent.name,
                value: parent.name,
            }));
        }
        return [];
    }, [data]);

    return contacts;
};
export default useGetOptionPeopleContactId;
