'use client';
import { useParams } from 'next/navigation';
import FormProductGroups from '../../_components/FormProductGroups';
import { useDetailProductGroups } from '@/apis/product-group/product-group.api';
import { Spinner } from 'reactstrap';

const DetailProductGroups = () => {
    const params = useParams();
    const id = params.id as string;
    const { data: productGroups } = useDetailProductGroups(id);
    const initValue = productGroups?.data;
    if (!initValue) {
        return <Spinner />;
    }
    return <FormProductGroups page='chi-tiet' initValue={initValue} />;
};
export default DetailProductGroups;
