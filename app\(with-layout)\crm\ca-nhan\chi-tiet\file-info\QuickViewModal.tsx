import React from 'react';
import { Mo<PERSON>, <PERSON>dalBody, ModalHeader } from 'reactstrap';

interface QuickViewModalProps {
    isOpen: boolean;
    toggle: () => void;
    data: {
        name: string;
        companyName?: string;
        email: string;
        phone: string;
        employees?: string;
        website?: string;
        score?: string;
        stage?: string;
        status?: string;
    };
}

const QuickViewModal = ({ isOpen, toggle, data }: QuickViewModalProps) => {
    return (
        <Modal
            isOpen={isOpen}
            toggle={toggle}
            size='lg'
            style={{
                width: '25%',
                margin: '0',
                position: 'absolute',
                right: '0',
                top: '0',
                height: '500px',
            }}
        >
            <ModalHeader style={{ fontSize: '15px' }}>Khách hàng</ModalHeader>
            <ModalBody className='p-4' style={{ fontSize: '15px' }}>
                <div className='text-center mb-4'>
                    <div
                        className='rounded-pill mx-auto d-flex align-items-center justify-content-center mb-3'
                        style={{
                            width: '120px',
                            height: '60px',
                            backgroundColor: 'rgba(10, 179, 156, 0.18)',
                            color: '#0ab39c',
                            fontSize: '30px',
                        }}
                    >
                        {data.name?.charAt(0) || 'G'}
                    </div>
                    <h5 className='mb-1' style={{ fontSize: '18px' }}>
                        {data.companyName || 'Công Ty CP Garena Việt Nam'}
                    </h5>
                    <p className='text-muted' style={{ fontSize: '15px' }}>
                        {data.name || 'Garena'}
                    </p>
                </div>

                <div className='mb-4 d-flex flex-column gap-4'>
                    <div className='d-flex mb-3'>
                        <div
                            className='w-50 text-start text-muted'
                            style={{ fontSize: '15px' }}
                        >
                            Email
                        </div>
                        <div
                            className='w-50 text-start'
                            style={{ fontSize: '15px' }}
                        >
                            {data.email}
                        </div>
                    </div>

                    <div className='d-flex mb-3'>
                        <div
                            className='w-50 text-start text-muted'
                            style={{ fontSize: '15px' }}
                        >
                            Số điện thoại
                        </div>
                        <div
                            className='w-50 text-start'
                            style={{ fontSize: '15px' }}
                        >
                            {data.phone}
                        </div>
                    </div>
                    {data.employees && (
                        <div className='d-flex mb-3'>
                            <div
                                className='w-50 text-start text-muted'
                                style={{ fontSize: '15px' }}
                            >
                                Quy mô
                            </div>
                            <div
                                className='w-50 text-start'
                                style={{ fontSize: '15px' }}
                            >
                                {data.employees}
                            </div>
                        </div>
                    )}
                    {data.website && (
                        <div className='d-flex mb-3'>
                            <div
                                className='w-50 text-start text-muted'
                                style={{ fontSize: '15px' }}
                            >
                                Website
                            </div>
                            <div
                                className='w-50 text-start'
                                style={{ fontSize: '15px' }}
                            >
                                {data.website}
                            </div>
                        </div>
                    )}
                    {data.score && (
                        <div className='d-flex mb-3'>
                            <div
                                className='w-50 text-start text-muted'
                                style={{ fontSize: '15px' }}
                            >
                                Điểm tiềm năng
                            </div>
                            <div
                                className='w-50 text-start'
                                style={{ fontSize: '15px' }}
                            >
                                {data.score}
                            </div>
                        </div>
                    )}
                    {data.stage && (
                        <div className='d-flex mb-3'>
                            <div
                                className='w-50 text-start text-muted'
                                style={{ fontSize: '15px' }}
                            >
                                Giai đoạn
                            </div>
                            <div className='w-50 text-start'>
                                <span
                                    className='badge bg-soft-success text-success'
                                    style={{
                                        fontSize: '13px',
                                        backgroundColor: '#daf4f0',
                                    }}
                                >
                                    {data.stage}
                                </span>
                            </div>
                        </div>
                    )}
                    {data.status && (
                        <div className='d-flex mb-3'>
                            <div
                                className='w-50 text-start text-muted'
                                style={{ fontSize: '15px' }}
                            >
                                Trạng thái
                            </div>
                            <div className='w-50 text-start'>
                                <span
                                    className='badge bg-soft-success text-success'
                                    style={{
                                        fontSize: '13px',
                                        backgroundColor: '#daf4f0',
                                    }}
                                >
                                    {data.status}
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </ModalBody>
        </Modal>
    );
};

export default QuickViewModal;
