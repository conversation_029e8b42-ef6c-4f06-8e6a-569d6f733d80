'use client';

import FormController from '@/components/common/FormController';
import {
    Button,
    Col,
    Modal,
    ModalBody,
    Modal<PERSON>ooter,
    ModalHeader,
    Row,
} from 'reactstrap';

interface BankAccountModalProps {
    isOpen: boolean;
    toggle: () => void;
    handleAddBankAccount: () => void;
}

const BankAccountModal = ({
    isOpen,
    toggle,
    handleAddBankAccount,
}: BankAccountModalProps) => {
    return (
        <Modal isOpen={isOpen} toggle={toggle}>
            <ModalHeader toggle={toggle}>Thêm tài khoản ngân hàng</ModalHeader>
            <ModalBody>
                <Row>
                    <Col md={12}>
                        <FormController
                            controlType='textInput'
                            name='newBankAccount.bankName'
                            label='Tên ngân hàng'
                            placeholder='Nhập tên ngân hàng'
                        />
                    </Col>
                    <Col md={12}>
                        <FormController
                            controlType='textInput'
                            name='newBankAccount.branchName'
                            label='Chi nhánh'
                            placeholder='Nhập tên chi nhánh'
                        />
                    </Col>
                    <Col md={12}>
                        <FormController
                            controlType='textInput'
                            name='newBankAccount.accountNumber'
                            label='Số tài khoản'
                            placeholder='Nhập số tài khoản'
                        />
                    </Col>
                    <Col md={12}>
                        <FormController
                            controlType='textInput'
                            name='newBankAccount.accountHolder'
                            label='Tên chủ tài khoản'
                            placeholder='Nhập tên chủ tài khoản'
                        />
                    </Col>
                    <Col md={12}>
                        <FormController
                            controlType='textInput'
                            name='newBankAccount.swiftCode'
                            label='Mã Swift'
                            placeholder='Nhập mã Swift'
                        />
                    </Col>
                </Row>
            </ModalBody>
            <ModalFooter>
                <Button color='secondary' onClick={toggle}>
                    Hủy
                </Button>
                <Button color='info' onClick={handleAddBankAccount}>
                    Lưu
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default BankAccountModal;
