'use client';
import { useCreateContact } from '@/apis/contact/contact.api';
import { IContact } from '@/apis/contact/contact.type';
import { KEYS_TO_CONTACT } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import PersonalForm from '../_components/PersonalForm';

export default function CreatePersonal() {
    const router = useRouter();
    const { mutate: createContact } = useCreateContact({
        onSuccess: () => {
            toast.success('Tạo mới cá nhân thành công');
            router.push(ROUTES.CRM.PERSONAL.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleCreateContact = (data: IContact) => {
        const payload = convertFormValueToPayload(data, KEYS_TO_CONTACT);
        data.phoneNumber = String(data.phoneNumber);
        createContact(payload as IContact);
    };

    const handleCancel = () => {
        router.push(ROUTES.CRM.PERSONAL.INDEX);
    };
    return (
        <PersonalForm
            onSubmit={handleCreateContact}
            onCancel={handleCancel}
            mode='create'
        />
    );
}
