'use client';
import { IContact } from '@/apis/contact/contact.type';
import Avatar from '@/components/common/Avatar';
import FormController from '@/components/common/FormController';
import SelectDepartmentControl from '@/components/common/FormController/SelectDepartmentControl';
import SelectPositionControl from '@/components/common/FormController/SelectPositionControl';
import { honorifics } from '@/constants/sharedData/sharedData';
import { yupResolver } from '@hookform/resolvers/yup';
import { ComboboxData } from '@mantine/core';
import { useEffect } from 'react';
import { FormProvider, Resolver, useForm } from 'react-hook-form';
import { Button, Card, Col, FormGroup, Label, Row } from 'reactstrap';
import * as yup from 'yup';

const personalFormSchema = yup.object().shape({
    honorific: yup.string().nullable(),
    name: yup
        .string()
        .required('Họ và tên là trường bắt buộc')
        .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
        .max(100, 'Họ và tên không được quá 100 ký tự')
        .matches(
            /^[a-zA-ZÀ-ỹ\s]+$/,
            'Họ và tên chỉ được chứa chữ cái và khoảng trắng',
        ),
    email: yup
        .string()
        .email('Email không hợp lệ')
        .required('Email là trường bắt buộc')
        .max(255, 'Email không được quá 255 ký tự'),
    phoneNumber: yup
        .string()
        .required('Số điện thoại là trường bắt buộc')
        .matches(/^[0-9]+$/, 'Số điện thoại chỉ được chứa số')
        .min(10, 'Số điện thoại phải có ít nhất 10 số')
        .max(11, 'Số điện thoại không được quá 11 số')
        .matches(
            /^(0[3|5|7|8|9])[0-9]{8,9}$/,
            'Số điện thoại không đúng định dạng Việt Nam',
        ),
    departmentId: yup.string().required('Phòng ban là trường bắt buộc'),
    positionId: yup.string().required('Chức vụ là trường bắt buộc'),
});

interface PersonalFormProps {
    initValue?: IContact;
    onSubmit?: (data: IContact) => void;
    onEdit?: (data: IContact) => void;
    onCancel: () => void;
    mode?: 'create' | 'edit';
}
const PersonalForm = ({
    initValue,
    onSubmit,
    onEdit,
    onCancel,
    mode = 'create',
}: PersonalFormProps) => {
    const methods = useForm<IContact>({
        defaultValues: initValue || {},
        resolver: yupResolver(
            personalFormSchema,
        ) as unknown as Resolver<IContact>,
    });

    useEffect(() => {
        if (initValue) {
            methods.reset(initValue);
        }
    }, [initValue, methods]);

    const handleFormSubmit = (data: IContact) => {
        if (mode === 'edit' && onEdit) {
            onEdit(data);
        } else if (onSubmit) {
            onSubmit(data);
        }
    };

    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                <h5 className='mb-3'>
                    <strong>THÔNG TIN CHÍNH</strong>
                </h5>
                <Row style={{ padding: '20px 40px 20px 40px' }}>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='honorific'
                            label='Xưng hô'
                            placeholder='Chọn cách xưng hô'
                            data={honorifics as ComboboxData}
                        />
                    </Col>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Họ và tên'
                            placeholder='Nhập họ và tên cá nhân'
                            required={true}
                        />
                    </Col>

                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='email'
                            label='Email'
                            placeholder='Nhập email cá nhân'
                            required={true}
                        />
                    </Col>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='phoneNumber'
                            label='Số điện thoại'
                            placeholder='Nhập số điện thoại cá nhân'
                            required={true}
                        />
                    </Col>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <SelectDepartmentControl
                            name='departmentId'
                            label='Phòng ban'
                            placeholder='Chọn phòng ban'
                            required={true}
                        />
                    </Col>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <SelectPositionControl
                            name='positionId'
                            label='Chức vụ'
                            placeholder='Chọn chức vụ'
                            required={true}
                        />
                    </Col>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='roleName'
                            label='Vai trò'
                            placeholder='Nhập vai trò của cá nhân trong khách hàng'
                        />
                    </Col>
                    <Col
                        md='6'
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormGroup>
                            <Label>
                                <strong>Ảnh đại diện</strong>
                            </Label>
                            <div>
                                <Avatar />
                            </div>
                        </FormGroup>
                    </Col>
                    <Col md='5'></Col>
                    <Col md='5' className='mt-4 d-flex justify-content-end'>
                        <Button
                            color='danger'
                            className='me-2'
                            type='button'
                            onClick={onCancel}
                        >
                            Hủy
                        </Button>
                        {mode === 'create' && (
                            <Button
                                color='success'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Tạo mới
                            </Button>
                        )}
                        {mode === 'edit' && (
                            <Button
                                color='success'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Lưu
                            </Button>
                        )}
                    </Col>
                </Row>
            </Card>
        </FormProvider>
    );
};
export default PersonalForm;
