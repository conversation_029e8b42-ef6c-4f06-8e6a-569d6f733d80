import React from 'react';
import { Card, CardBody } from 'reactstrap';
import DataCardHeader from './DataCardHeader';
import HeaderRow from './HeaderRow';
import DataRow from './DataRow';
import ViewAllButton from './ViewAllButton';

interface OpportunityItemProps {
    title: string;
    revenue: string;
    deadline: string;
    prediction: string;
}

const OpportunityItem: React.FC<OpportunityItemProps> = ({
    title,
    revenue,
    deadline,
    prediction,
}) => (
    <>
        <div className='d-flex justify-content-between align-items-center mb-2'>
            <HeaderRow title={title} />
        </div>
        <DataRow label='Doanh thu:' value={revenue} />
        <DataRow label='Hạn hoàn thành:' value={deadline} />
        <DataRow label='Dự đoán:' value={prediction} />
    </>
);

const OpportunityCard: React.FC = () => (
    <div className='mb-4'>
        <Card className='bg-white'>
            <DataCardHeader title='Cơ hội' count={2} />
            <CardBody>
                <OpportunityItem
                    title='Cơ hội vận hành robot hàn'
                    revenue='30,000,000'
                    deadline='15/02/2025'
                    prediction='Khả năng sẽ chốt cao'
                />
                <div className='d-flex border-top pt-3 mb-2 justify-content-between align-items-center'>
                    <HeaderRow title='Cơ hội bảo hành robot hàn' />
                </div>
                <DataRow label='Doanh thu:' value='10,000,000' />
                <DataRow label='Hạn hoàn thành:' value='20/02/2025' />
                <DataRow label='Dự đoán:' value='Chưa chắc chắn' />
                <ViewAllButton />
            </CardBody>
        </Card>
    </div>
);

export default OpportunityCard;
