'use client';
import { <PERSON>, <PERSON>Header, Col, Row } from 'reactstrap';
import MenuIcon from './components/MenuIcon';
import SelectTime from './components/SelectTime';
import TableCRM from './components/TableCRM';
import BarChart from '@/components/common/Charts/Bar/BarChart';
import PieSimple from '@/components/common/Charts/Pie/PieSimple';

export default function DashBoardCRM() {
    return (
        <div>
            <Col lg={12}>
                <SelectTime />
            </Col>
            <Col lg={12}>
                <MenuIcon />
            </Col>
            <Col lg={12}>
                <Row>
                    <Col lg={6} xl={4}>
                        <Card>
                            <CardHeader>
                                Tình hình thực hiện mục tiêu doanh thu
                            </CardHeader>
                            <BarChart dataColors='["--vz-primary"]' />
                        </Card>
                    </Col>
                    <Col lg={6} xl={4}>
                        <Card>
                            <CardHeader>Thống kê KH theo loại hình</CardHeader>
                            <PieSimple dataColors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger", "--vz-info"]' />
                        </Card>
                    </Col>
                    <Col lg={6} xl={4}>
                        <Card>
                            <CardHeader>Doanh thu theo loại hình DN</CardHeader>
                            <BarChart dataColors='["--vz-primary"]' />
                        </Card>
                    </Col>
                    <Col lg={6} xl={4}>
                        <Card>
                            <CardHeader>Thống kê KH theo giai đoạn</CardHeader>
                            <PieSimple dataColors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger", "--vz-info"]' />
                        </Card>
                    </Col>
                    <Col lg={6} xl={4}>
                        <Card>
                            <CardHeader>
                                Tăng trưởng số lượng KH đã mua theo loại hình
                            </CardHeader>
                            <BarChart dataColors='["--vz-primary"]' />
                        </Card>
                    </Col>

                    <Col lg={12} xl={12}>
                        <TableCRM />
                    </Col>
                </Row>
            </Col>
        </div>
    );
}
