import { SearchCustomerResponse } from '@/apis/customer/customer.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import { LeadStatus } from '@/constants/sharedData/sharedData';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '../_types/action.type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: SearchCustomerResponse | undefined,
    ) => void;
    page: string;
    onRestore?: (data: SearchCustomerResponse | undefined) => void;
}

const useGetColumn = ({
    onSelectedAction,
    page,
    onRestore,
}: GetColumnProps) => {
    const actions: DropdownAction<SearchCustomerResponse>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const getLeadStatusLabel = (status: string) => {
        const found = LeadStatus.find(
            (item) => String(item.value) === String(status),
        );
        return found ? found.label : String(status);
    };

    const columns = useMemo<MRT_ColumnDef<SearchCustomerResponse>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên khách hàng',
                size: 200,
                minSize: 150,
                maxSize: 300,
            },
            {
                accessorKey: 'businessTypeName',
                header: 'Loại hình',
                size: 120,
                minSize: 100,
                maxSize: 180,
            },
            {
                accessorKey: 'industryName',
                header: 'Lĩnh vực',
                size: 120,
                minSize: 100,
                maxSize: 180,
            },
            {
                accessorKey: 'leadStatus',
                header: 'Giai đoạn',
                size: 120,
                minSize: 100,
                maxSize: 150,
                Cell: ({ cell }) => (
                    <span
                        className='badge me-1'
                        style={{
                            backgroundColor: '#daf4f0',
                            color: '#2fbeab',
                            display: 'inline-block',
                            textAlign: 'center',
                            padding: '4px 8px',
                            fontSize: '12px',
                            fontWeight: 500,
                            borderRadius: '4px',
                        }}
                    >
                        {getLeadStatusLabel(cell.getValue() as string)}
                    </span>
                ),
            },
            {
                accessorKey: 'salePersonName',
                header: 'Nhân viên kinh doanh',
                size: 180,
                minSize: 150,
                maxSize: 250,
            },
            {
                accessorKey: 'createdOn',
                header: 'Ngày tạo',
                size: 140,
                minSize: 120,
                maxSize: 180,
                Cell: ({ row }) => (
                    <FormattedDateTimeWithFormat
                        date={row.original.createdOn}
                    />
                ),
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<SearchCustomerResponse>),
                Cell: ({ row }) => {
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    if (page === 'list-Customer') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                },
            },
        ],
        [actions, onRestore, page],
    );

    return columns;
};

export default useGetColumn;
