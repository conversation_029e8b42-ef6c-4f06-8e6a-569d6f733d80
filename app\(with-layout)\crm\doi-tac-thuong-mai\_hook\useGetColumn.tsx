import { IPartnerResponse } from '@/apis/partners/partners.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import columnDateDefault from '@/components/common/MantineReactTable/columnDateDefault';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: IPartnerResponse | undefined,
    ) => void;
    onRestore?: (data: IPartnerResponse | undefined) => void;
    page: string;
}
const useGetColumn = ({
    onSelectedAction,
    onRestore,
    page,
}: GetColumnProps) => {
    const actions: DropdownAction<IPartnerResponse>[] = useMemo(
        () => [
            {
                icon: 'ri-function-line',
                label: 'Hoạt động',
                onClick: (data) => onSelectedAction?.(ACTIONS.ACTIVITY, data),
            },
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );
    const columns = useMemo<MRT_ColumnDef<IPartnerResponse>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên đối tác thương mại',
                enableSorting: false,
                size: 400,
            },
            { accessorKey: 'taxCode', header: 'Mã số thuế', size: 250 },
            {
                accessorKey: 'salePersonName',
                header: 'Nhân viên kinh doanh',
                size: 250,
            },
            page === 'restore'
                ? {
                      ...(columnDateDefault as MRT_ColumnDef<IPartnerResponse>),
                      accessorKey: 'deletedDate',
                      header: 'Ngày xóa',
                      Cell: ({ row }) => (
                          <FormattedDateTimeWithFormat
                              date={row.original.deletedDate ?? ''}
                          />
                      ),
                  }
                : {
                      ...(columnDateDefault as MRT_ColumnDef<IPartnerResponse>),
                      accessorKey: 'createdDate',
                      header: 'Ngày tạo',
                      Cell: ({ row }) => (
                          <FormattedDateTimeWithFormat
                              date={row.original.createdDate ?? ''}
                          />
                      ),
                  },
            {
                ...(columnActionsDefault as MRT_ColumnDef<IPartnerResponse>),
                Cell: ({ row }) => {
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    if (page === 'list-partners') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                },
            },
        ],
        [actions, onRestore, page],
    );

    return columns;
};

export default useGetColumn;
