// app/api/auth/login/route.ts
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // Gọi API backend
        const backendUrl =
            process.env.BACKEND_API_URL || 'https://tmivcms.digins.vn';
        const response = await fetch(`${backendUrl}/api/v1.0/Users/<USER>
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body),
        });

        if (!response.ok) {
            return NextResponse.json(
                { error: 'Login failed' },
                { status: response.status },
            );
        }

        const data = await response.json();

        // Lấy token từ response
        const { token, refresh } = data.data;

        // Set httpOnly cookies
        const cookieStore = await cookies();

        // Set access token cookie (1 hour)
        cookieStore.set('access_token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 60 * 60, // 1 hour
            path: '/',
        });

        // Set refresh token cookie (7 days)
        cookieStore.set('refresh_token', refresh, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 60 * 60 * 24 * 7, // 7 days
            path: '/',
        });

        // Trả về user info (không bao gồm token)
        return NextResponse.json({
            data: {
                user: data.data.user,
                accessTokenExpiredIn: data.data.accessTokenExpiredIn,
                tokenType: data.data.tokenType,
            },
            isError: false,
            errorMessage: null,
            status: 200,
        });
    } catch (error) {
        console.error('Login error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 },
        );
    }
}
