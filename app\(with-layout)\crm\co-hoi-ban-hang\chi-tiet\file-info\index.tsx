import { IDealDetail } from '@/apis/opportunity/opportunity.type';
import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Col } from 'reactstrap';

interface FileInfoProps {
    data?: IDealDetail;
}

const FileInfo = ({ data }: FileInfoProps) => {
    const [showAllDeals, setShowAllDeals] = useState(false);
    const [showAllTradePartners, setShowAllTradePartners] = useState(false);
    const [showAllContacts, setShowAllContacts] = useState(false);
    const [showAllQuotes, setShowAllQuotes] = useState(false);
    const [showAllContracts, setShowAllContracts] = useState(false);

    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đ<PERSON>h kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>
            {data?.deals && data.deals.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Cơ hội ({data?.deals?.length})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {data?.deals && data.deals.length > 0 ? (
                            (showAllDeals
                                ? data.deals
                                : data.deals.slice(0, 1)
                            ).map((deal, index) => (
                                <div className='mb-4' key={deal.id || index}>
                                    <div className='d-flex align-items-center mb-3'>
                                        <h6 className='mb-0 flex-grow-1 fs-16'>
                                            {deal.title}
                                        </h6>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon'
                                            title='Xem chi tiết'
                                        >
                                            <i className='ri-more-2-line'></i>
                                        </Button>
                                    </div>

                                    <div className='mb-2'>
                                        <span className='text-muted fs-14'>
                                            Doanh thu:{' '}
                                            <span className='fw-medium text-success fs-15'>
                                                {deal.amount?.toLocaleString(
                                                    'vi-VN',
                                                )}{' '}
                                                VNĐ
                                            </span>
                                        </span>
                                    </div>

                                    <div className='mb-2'>
                                        <span className='text-muted fs-14'>
                                            Hạn hoàn thành:{' '}
                                            <span className='fw-medium text-dark'>
                                                -
                                            </span>
                                        </span>
                                    </div>

                                    <div className='mb-2'>
                                        <span className='text-muted fs-14'>
                                            Xác suất thành:{' '}
                                            <span className='fw-medium text-info'>
                                                {deal.probability}%
                                            </span>
                                        </span>
                                    </div>

                                    {index <
                                        (showAllDeals
                                            ? data.deals
                                            : data.deals.slice(0, 1)
                                        ).length -
                                            1 && <hr className='my-3' />}
                                </div>
                            ))
                        ) : (
                            <div className='text-center py-3'>
                                <i className='ri-briefcase-line text-muted fs-24 mb-2 d-block'></i>
                                <p className='text-muted mb-0'>
                                    Chưa có cơ hội nào
                                </p>
                            </div>
                        )}

                        {data?.deals && data.deals.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                className='mt-2'
                                onClick={() => setShowAllDeals(!showAllDeals)}
                            >
                                <i className='ri-eye-line align-middle me-1'></i>
                                {showAllDeals
                                    ? 'Thu gọn'
                                    : 'Xem toàn bộ danh sách'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}
            {data?.tradePartners && data.tradePartners.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Đối tác thương mại (
                                {data?.tradePartners?.length || 0})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllTradePartners
                            ? data.tradePartners
                            : data.tradePartners.slice(0, 1)
                        ).map((partner, partnerIndex) => (
                            <div className='mb-4' key={partnerIndex}>
                                <div className='d-flex align-items-center mb-3'>
                                    <i className='ri-building-line text-primary fs-20 me-2'></i>
                                    <h6 className='mb-0 flex-grow-1 fs-16'>
                                        {partner.contacts &&
                                        partner.contacts.length > 0
                                            ? partner.contacts[0].fullName
                                            : 'FD Technologies'}
                                    </h6>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                <div className='mb-2'>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-mail-line me-2 fs-14'></i>
                                        <span className='fs-14'>
                                            Email:{' '}
                                            <span className='fw-medium text-dark'>
                                                {partner.email ||
                                                    '<EMAIL>'}
                                            </span>
                                        </span>
                                    </div>
                                </div>

                                <div className='mb-2'>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-phone-line me-2 fs-14'></i>
                                        <span className='fs-14'>
                                            Số điện thoại:{' '}
                                            <span className='fw-medium text-dark'>
                                                {partner.phoneNumber ||
                                                    '0981827526'}
                                            </span>
                                        </span>
                                    </div>
                                </div>

                                {partnerIndex <
                                    (showAllTradePartners
                                        ? data.tradePartners
                                        : data.tradePartners.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.tradePartners.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                className='mt-2'
                                onClick={() =>
                                    setShowAllTradePartners(
                                        !showAllTradePartners,
                                    )
                                }
                            >
                                <i className='ri-eye-line align-middle me-1'></i>
                                {showAllTradePartners
                                    ? 'Thu gọn'
                                    : 'Xem toàn bộ danh sách'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}
            {data?.contacts && data.contacts.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Cá nhân ({data?.contacts?.length || 0})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllContacts
                            ? data.contacts
                            : data.contacts.slice(0, 1)
                        ).map((contact, contactIndex) => (
                            <div
                                className='mb-4'
                                key={contact.id || contactIndex}
                            >
                                <div className='d-flex align-items-center mb-3'>
                                    <i className='ri-user-line text-primary fs-20 me-2'></i>
                                    <h6 className='mb-0 flex-grow-1 fs-16'>
                                        {contact.fullName}
                                    </h6>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                {contact.roleName && (
                                    <div className='mb-3'>
                                        <span className='badge bg-success-subtle text-success fs-12 px-2 py-1'>
                                            <i className='ri-user-star-line me-1'></i>
                                            {contact.roleName}
                                        </span>
                                    </div>
                                )}

                                <div className='mb-2'>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-mail-line me-2 fs-14'></i>
                                        <span className='fs-14'>
                                            Email:{' '}
                                            <span className='fw-medium text-dark'>
                                                {contact.email ||
                                                    '<EMAIL>'}
                                            </span>
                                            {contact.email && (
                                                <i className='ri-verified-badge-line text-success ms-1'></i>
                                            )}
                                        </span>
                                    </div>
                                </div>

                                <div className='mb-2'>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-phone-line me-2 fs-14'></i>
                                        <span className='fs-14'>
                                            Số điện thoại:{' '}
                                            <span className='fw-medium text-dark'>
                                                {contact.phoneNumber ||
                                                    '0979273526'}
                                            </span>
                                        </span>
                                    </div>
                                </div>

                                <div className='mb-2'>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-user-settings-line me-2 fs-14'></i>
                                        <span className='fs-14'>
                                            Vai trò:{' '}
                                            <span className='fw-medium text-dark'>
                                                {contact.roleName ||
                                                    'Người thẩm định kỹ thuật'}
                                            </span>
                                        </span>
                                    </div>
                                </div>

                                {contactIndex <
                                    (showAllContacts
                                        ? data.contacts
                                        : data.contacts.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.contacts.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                className='mt-2'
                                onClick={() =>
                                    setShowAllContacts(!showAllContacts)
                                }
                            >
                                <i className='ri-eye-line align-middle me-1'></i>
                                {showAllContacts
                                    ? 'Thu gọn'
                                    : 'Xem toàn bộ danh sách'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}

            {data?.process && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Quy trình liên kết
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                            <Button
                                color='light'
                                size='sm'
                                className='btn-icon ms-1'
                            >
                                <i className='ri-more-2-line'></i>
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        <div className='mb-4'>
                            <h6 className='mb-3 fs-16'>{data.process.name}</h6>
                            <div className='mb-2'>
                                <span className='text-muted fs-14'>
                                    Trạng thái:{' '}
                                    <span
                                        className={`badge ${
                                            data.process.status === 1
                                                ? 'bg-success-subtle text-success'
                                                : data.process.status === 2
                                                  ? 'bg-warning-subtle text-warning'
                                                  : 'bg-danger-subtle text-danger'
                                        }`}
                                    >
                                        {data.process.statusName}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            )}

            {data?.quotes && data.quotes.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Báo giá ({data.quotes.length})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                            <Button
                                color='light'
                                size='sm'
                                className='btn-icon ms-1'
                            >
                                <i className='ri-more-2-line'></i>
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllQuotes
                            ? data.quotes
                            : data.quotes.slice(0, 1)
                        ).map((quote, index) => (
                            <div className='mb-4' key={quote.id || index}>
                                <div className='d-flex align-items-center mb-3'>
                                    <h6 className='mb-0 flex-grow-1 fs-16'>
                                        {quote.name}
                                    </h6>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Giá trị:{' '}
                                        <span className='fw-medium text-success fs-15'>
                                            {quote.amount?.toLocaleString(
                                                'vi-VN',
                                            )}{' '}
                                            VNĐ
                                        </span>
                                    </span>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Ngày kết thúc:{' '}
                                        <span className='fw-medium text-dark'>
                                            {new Date(
                                                quote.closeDate,
                                            ).toLocaleDateString('vi-VN')}
                                        </span>
                                    </span>
                                </div>

                                {index <
                                    (showAllQuotes
                                        ? data.quotes
                                        : data.quotes.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.quotes.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                className='mt-2'
                                onClick={() => setShowAllQuotes(!showAllQuotes)}
                            >
                                <i className='ri-eye-line align-middle me-1'></i>
                                {showAllQuotes
                                    ? 'Thu gọn'
                                    : 'Xem toàn bộ danh sách'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}

            {data?.contracts && data.contracts.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Hợp đồng ({data.contracts.length})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllContracts
                            ? data.contracts
                            : data.contracts.slice(0, 1)
                        ).map((contract, index) => (
                            <div className='mb-4' key={contract.id || index}>
                                <div className='d-flex align-items-center mb-3'>
                                    <h6 className='mb-0 flex-grow-1 fs-16'>
                                        {contract.name}
                                    </h6>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Ngày tạo:{' '}
                                        <span className='fw-medium text-dark'>
                                            {new Date(
                                                contract.createdDate,
                                            ).toLocaleDateString('vi-VN')}{' '}
                                            {new Date(
                                                contract.createdDate,
                                            ).toLocaleTimeString('vi-VN', {
                                                hour: '2-digit',
                                                minute: '2-digit',
                                            })}
                                        </span>
                                    </span>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Người tạo:{' '}
                                        <span className='fw-medium text-dark'>
                                            {contract.creatorName}
                                        </span>
                                    </span>
                                </div>

                                {index <
                                    (showAllContracts
                                        ? data.contracts
                                        : data.contracts.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.contracts.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                className='mt-2'
                                onClick={() =>
                                    setShowAllContracts(!showAllContracts)
                                }
                            >
                                <i className='ri-eye-line align-middle me-1'></i>
                                {showAllContracts
                                    ? 'Thu gọn'
                                    : 'Xem toàn bộ danh sách'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}

            {data?.project && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>Dự án</h5>
                        </div>
                    </CardHeader>
                    <CardBody>
                        <div className='mb-4'>
                            <h6 className='mb-3 fs-16'>{data.project.name}</h6>

                            <div className='mb-2'>
                                <span className='text-muted fs-14'>
                                    Người quản lý:{' '}
                                    <span className='fw-medium text-dark'>
                                        {data.project.managerName}
                                    </span>
                                </span>
                            </div>

                            <div className='mb-2'>
                                <span className='text-muted fs-14'>
                                    Trạng thái dự án:{' '}
                                    <span
                                        className={`fw-medium ${
                                            data.project.status === 1
                                                ? 'text-success'
                                                : data.project.status === 2
                                                  ? 'text-warning'
                                                  : 'text-danger'
                                        }`}
                                    >
                                        {data.project.statusName}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            )}
        </Col>
    );
};

export default FileInfo;
