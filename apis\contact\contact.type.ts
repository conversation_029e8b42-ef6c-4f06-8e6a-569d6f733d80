import { Honorific } from '@/constants/sharedData/sharedData.enums';

export interface IContactItem {
    name: string;
    email: string;
    companyName: string | null;
    phoneNumber: string;
    departmentName: string;
    positionName: string;
    createdOn: string;
    roleName: string;
}

export interface IContactResponse {
    id: string;
    name: string;
    email: string;
    companyName: string | null;
    phoneNumber: string;
    departmentId: string;
    departmentName: string;
    positionId: string;
    positionName: string;
    createdOn: string;
    roleName: string;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    deletedTime: string;
}

export interface SearchContact {
    Name?: string;
    CompanyId?: string;
    DepartmentId?: string;
    FromDate?: string;
    ToDate?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
}

export interface DeleteContactResponse {
    data: boolean;
    isError: boolean;
    errorMessage: string;
    status: number;
}

export interface DeleteContactPayload {
    ids: string[];
}
export interface IContact {
    id?: string;
    honorific: Honorific | number | string;
    name: string;
    email: string;
    phoneNumber: string;
    departmentId: string;
    departmentName: string;
    positionId: string;
    positionName: string;
    roleName: string;
    avatar: string;
    extraInfo: {
        code: string;
        companyId: string | null;
        companyName: string | null;
        companyEmail: string | null;
        companyPhoneNumber: string | null;
        tradePartnerId: string | null;
        tradePartnerName: string | null;
        tradePartnerEmail: string | null;
        tradePartnerPhoneNumber: string | null;
        salePerson: string;
        createdOn: string;
    };
    totalItems?: number;
    totalPages?: number;
    currentPage?: number;
}
export interface ContactDetail {
    id: string;
    name: string;
    email: string;
    departmentId: string;
    departmentName: string;
    positionId: string;
    positionName: string;
    phoneNumber: string;
    role: number;
    companyContactRoleName: string;
    roleName: string;
    fullName: string;
}
