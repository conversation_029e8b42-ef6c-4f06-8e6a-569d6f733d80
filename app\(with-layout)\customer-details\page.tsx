'use client';
import React, { useState } from 'react';
import AddressTab from '@/app/(with-layout)/customer-details/_components/address/AddressTab';
import PaymentInfoTab from '@/app/(with-layout)/customer-details/_components/payment/PaymentInfoTab';
import SalesPurchasingTab from '@/app/(with-layout)/customer-details/_components/payment/SalesPurchasingTab';
import GeneralInfoTab from '@/app/(with-layout)/customer-details/_components/profile/GeneralInfoTab';
import DropdownOptions from '@/app/(with-layout)/customer-details/_components/tabs/DropdownOptions';
import NavTabs from '@/app/(with-layout)/customer-details/_components/tabs/NavTabs';
import {
    Col,
    Card,
    CardHeader,
    CardBody,
    TabContent,
    TabPane,
    Row,
} from 'reactstrap';
import EnterpriseCard from '@/app/(with-layout)/customer-details/_components/cards/EnterpriseCard';
import PersonalCard from '@/app/(with-layout)/customer-details/_components/cards/PersonalCard';
import OpportunityCard from '@/app/(with-layout)/customer-details/_components/cards/OpportunityCard';
import QuotationCard from '@/app/(with-layout)/customer-details/_components/cards/QuotationCard';
import OrderCard from '@/app/(with-layout)/customer-details/_components/cards/OrderCard';

const Test: React.FC = () => {
    const [activeTab, setActiveTab] = useState<string>('1');

    const toggleTab = (tab: string) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };

    return (
        <>
            <Row>
                <Col xxl={8}>
                    <Card className='card-height-100'>
                        <CardHeader className='border-0 d-flex justify-between justify-content-between items-center'>
                            <NavTabs
                                activeTab={activeTab}
                                toggleTab={toggleTab}
                            />
                            <DropdownOptions />
                        </CardHeader>
                        <CardBody>
                            <TabContent activeTab={activeTab} className='p-3'>
                                <TabPane tabId='1'>
                                    <GeneralInfoTab />
                                </TabPane>
                                <TabPane tabId='2'>
                                    <AddressTab />
                                </TabPane>
                                <TabPane tabId='3'>
                                    <SalesPurchasingTab />
                                </TabPane>
                                <TabPane tabId='4'>
                                    <PaymentInfoTab />
                                </TabPane>
                            </TabContent>
                        </CardBody>
                    </Card>
                </Col>
                <Col xxl={4}>
                    <EnterpriseCard />
                    <PersonalCard />
                    <OpportunityCard />
                    <QuotationCard />
                    <OrderCard />
                </Col>
            </Row>
        </>
    );
};

export default Test;
