import { IQuoteReponse } from '@/apis/quotes/quotes.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import columnDateDefault from '@/components/common/MantineReactTable/columnDateDefault';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { TabValues } from '../type';

interface GetColumnProps {
    onSelectedAction?: (action: ACTIONS, data: IQuoteReponse) => void;
    onRestore?: (data: IQuoteReponse) => void;
    page: string;
    tab?: TabValues;
}
const useGetColumn = ({
    onSelectedAction,
    onRestore,
    page,
    tab,
}: GetColumnProps) => {
    const actions: DropdownAction<IQuoteReponse>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(
                        ACTIONS.VIEW_DETAIL,
                        data as IQuoteReponse,
                    ),
            },
            {
                icon: 'ri-edit-line',
                label: 'Sửa báo giá',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.EDIT, data as IQuoteReponse),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa báo giá',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.DELETE, data as IQuoteReponse),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );
    const allColumns = useMemo<MRT_ColumnDef<IQuoteReponse>[]>(
        () => [
            {
                accessorKey: 'quoteName',
                header: 'Tên báo giá',
                enableSorting: false,
            },
            {
                accessorKey: 'name',
                header: 'Tên báo giá',
                enableSorting: false,
                size: 600,
            },
            { accessorKey: 'dealName', header: 'Tên cơ hội' },

            {
                accessorKey: 'finalTotalPrice',
                header: 'Thành tiền',
                size: 150,
                Cell: ({ row }) => row.original.finalTotalPrice,
            },

            {
                accessorKey: 'userNameCreated',
                header:
                    tab === TabValues.QUOTATION_FORM
                        ? 'Người tạo'
                        : 'Nhân viên kinh doanh',
                size: 180,
            },
            {
                accessorKey: 'statusName',
                header: 'Trạng thái',
                enableResizing: false,
                enableGrouping: false,
                enableSorting: false,
                Cell: ({ row }) => row.original.statusName,
                size: 100,
            },
            {
                ...(columnDateDefault as MRT_ColumnDef<IQuoteReponse>),
                accessorKey: 'createdDateTime',
                header: 'Ngày tạo',
                Cell: ({ row }) => (
                    <FormattedDateTimeWithFormat
                        date={row.original.createdDateTime}
                    />
                ),
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<IQuoteReponse>),
                Cell: ({ row }) => {
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    if (page === 'list-quote') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                },
            },
        ],
        [actions, onRestore, page, tab],
    );

    // Bảng tab Mẫu báo giá
    const columnsTabQuotationForm = useMemo(
        () => ['name', 'createdDateTime', 'userNameCreated'],
        [],
    );
    // Bảng tab Báo giá liên kết
    const columnsTabLinkQuote = useMemo(
        () => [
            'quoteName',
            'dealName',
            'statusName',
            'finalTotalPrice',
            'createdDateTime',
            'userNameCreated',
        ],
        [],
    );

    const columns = useMemo(() => {
        if (tab === TabValues.QUOTATION_FORM) {
            return allColumns.filter(
                (column) =>
                    // Luôn hiển thị cột "Hành động" (không có accessorKey)
                    !column?.accessorKey ||
                    columnsTabQuotationForm.includes(
                        column?.accessorKey as string,
                    ),
            );
        }

        if (tab === TabValues.LINK_QUOTE) {
            return allColumns.filter(
                (column) =>
                    // Luôn hiển thị cột "Hành động" (không có accessorKey)
                    !column?.accessorKey ||
                    columnsTabLinkQuote.includes(column?.accessorKey as string),
            );
        }

        return allColumns;
    }, [tab, allColumns, columnsTabQuotationForm, columnsTabLinkQuote]);

    return columns;
};

export default useGetColumn;
