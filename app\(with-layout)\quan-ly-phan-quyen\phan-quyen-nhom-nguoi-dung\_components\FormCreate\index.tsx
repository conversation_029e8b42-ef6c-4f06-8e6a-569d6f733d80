import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
    <PERSON>ton,
    Card,
    CardBody,
    Nav,
    NavItem,
    NavLink,
    TabContent,
    TabPane,
} from 'reactstrap';
import TabInformation from './TabInformation';
import TabPermission from './TabPermission';

interface Permission {
    all: boolean;
    view: boolean;
    update: boolean;
    delete: boolean;
    other: string;
}

interface FormData {
    name: string;
    moTa: string;
    loaiPhanMem: string;
    nhanBanTuNhom: string;
    permissions: Permission[];
}

const FormCreate = () => {
    const [activeTab, setActiveTab] = useState('1');
    const methods = useForm<FormData>();

    const toggleTab = (tab: string) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };

    return (
        <FormProvider {...methods}>
            <Card>
                <Nav className='d-flex justify-content-start p-3'>
                    <NavItem>
                        <NavLink
                            className={activeTab === '1' ? 'active' : ''}
                            onClick={() => toggleTab('1')}
                            style={{
                                cursor: 'pointer',
                                ...(activeTab === '1' && {
                                    color: '#0ab39c',
                                    borderBottom: '2px solid #0ab39c',
                                    height: '100%',
                                }),
                            }}
                        >
                            Thông tin nhóm người dùng
                        </NavLink>
                    </NavItem>
                    <NavItem>
                        <NavLink
                            className={activeTab === '2' ? 'active' : ''}
                            onClick={() => toggleTab('2')}
                            style={{
                                cursor: 'pointer',
                                ...(activeTab === '2' && {
                                    color: '#0ab39c',
                                    borderBottom: '2px solid #0ab39c',
                                }),
                            }}
                        >
                            Phân quyền nhóm người dùng
                        </NavLink>
                    </NavItem>
                    <NavItem>
                        <NavLink
                            className={activeTab === '3' ? 'active' : ''}
                            onClick={() => toggleTab('3')}
                            style={{
                                cursor: 'pointer',
                                ...(activeTab === '3' && {
                                    color: '#0ab39c',
                                    borderBottom: '2px solid #0ab39c',
                                }),
                            }}
                        >
                            Phân quyền dữ liệu
                        </NavLink>
                    </NavItem>
                </Nav>

                <CardBody>
                    <TabContent activeTab={activeTab}>
                        <TabPane tabId='1'>
                            <TabInformation />
                        </TabPane>
                        <TabPane tabId='2'>
                            <TabPermission />
                        </TabPane>
                        <TabPane tabId='3'></TabPane>
                    </TabContent>
                </CardBody>
                <div className='d-flex justify-content-end p-3 gap-2'>
                    <Button
                        color='secondary'
                        outline
                        style={{
                            minWidth: '100px',
                            border: '1px solid #f17055',
                            color: '#f17055',
                        }}
                    >
                        Hủy
                    </Button>
                    <Button
                        color='success'
                        style={{
                            minWidth: '100px',
                            backgroundColor: '#0ab39c',
                            border: 'none',
                        }}
                    >
                        Tạo mới
                    </Button>
                </div>
            </Card>
        </FormProvider>
    );
};

export default FormCreate;
