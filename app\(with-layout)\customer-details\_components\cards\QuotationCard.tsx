import React from 'react';
import { Card, CardBody } from 'reactstrap';
import DataCardHeader from './DataCardHeader';
import HeaderRow from './HeaderRow';
import DataRow from './DataRow';
import ViewAllButton from './ViewAllButton';

const QuotationCard: React.FC = () => (
    <div className='mb-4'>
        <Card className='bg-white'>
            <DataCardHeader title='Báo giá' count={1} />
            <CardBody>
                <div className='d-flex justify-content-between align-items-center mb-2'>
                    <HeaderRow title='Gửi đơn báo giá máy móc sản xuất' />
                </div>
                <div className='mb-2'>
                    <div className='text-muted'>Trạng thái:</div>
                    <div>
                        <span className='badge bg-success-subtle text-success'>
                            <PERSON><PERSON> gửi
                        </span>
                    </div>
                </div>
                <DataRow label='<PERSON><PERSON><PERSON> kết thúc:' value='12/02/2025' />
                <ViewAllButton />
            </CardBody>
        </Card>
    </div>
);

export default QuotationCard;
