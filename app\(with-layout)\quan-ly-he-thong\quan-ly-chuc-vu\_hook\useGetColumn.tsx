import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

interface GetColumnProps {
    onSelectedAction: (
        action: ACTIONS,
        data: IPositionManagement | undefined,
    ) => void;
}
export interface IPositionManagement {
    name: string;
    MoTaChucVu: string;
    status: string;
}
const useGetColumn = ({ onSelectedAction }: GetColumnProps) => {
    const actions: DropdownAction<IPositionManagement>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) => onSelectedAction(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<IPositionManagement>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên nhóm người dùng',
                size: 150,
                // Header: () => (
                //     <div style={{ paddingLeft: '20px' }}>
                //         Tên nhóm người dùng
                //     </div>
                // ),
                // Cell: ({ cell }) => (
                //     <div style={{ paddingLeft: '50px' }}>
                //         {cell.getValue<string>()}
                //     </div>
                // ),
            },
            {
                accessorKey: 'MoTaChucVu',
                header: 'Mô tả chức vụ',
                size: 350,

                Cell: ({ cell }) => <div>{cell.getValue<string>()}</div>,
            },
            {
                accessorKey: 'status',
                header: 'Trạng thái',
                size: 100,
                Cell: ({ cell }) => (
                    <div className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800'>
                        {cell.getValue<string>()}
                    </div>
                ),
            },
            {
                header: 'Hành động',
                enableResizing: false,
                size: 70,
                Header: () => (
                    <div style={{ textAlign: 'center' }}>Hành động</div>
                ),
                Cell: ({ row }) => (
                    <div
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                        style={{ textAlign: 'center' }}
                    >
                        <DropdownActionMenu
                            actions={actions}
                            data={row.original}
                            direction='down'
                            end={false}
                        />
                    </div>
                ),
            },
        ],
        [actions],
    );

    return columns;
};

export default useGetColumn;
