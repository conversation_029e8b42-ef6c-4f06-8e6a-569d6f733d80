import { IDealDetail, IDealContact } from '@/apis/opportunity/opportunity.type';

interface Tab2Props {
    data?: IDealDetail;
}

const Tab2 = ({ data }: Tab2Props) => {
    const purchasingContacts =
        data?.contactsDeal?.filter((contact) => contact.roleType === 0) || [];
    const usageContacts =
        data?.contactsDeal?.filter((contact) => contact.roleType === 1) || [];

    const renderContactTable = (contacts: IDealContact[], title: string) => {
        if (contacts.length === 0) {
            return (
                <div className='mb-4'>
                    <h5 className='mb-3'>{title}</h5>
                    <p className='text-muted'>Không có dữ liệu</p>
                </div>
            );
        }

        return (
            <div className='mb-4'>
                <h5 className='mb-3'>{title}</h5>
                <div className='table-responsive'>
                    <table className='table table-bordered'>
                        <thead className='table-light'>
                            <tr>
                                <th style={{ width: '40%' }}>Họ và tên</th>
                                <th style={{ width: '30%' }}>Phòng ban</th>
                                <th style={{ width: '30%' }}>
                                    Chức danh nghề nghiệp
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {contacts.map((contact, index) => (
                                <tr key={contact.id || index}>
                                    <td>
                                        <div className='d-flex align-items-center'>
                                            <div
                                                className='d-flex justify-content-center align-items-center rounded-circle me-2'
                                                style={{
                                                    width: '30px',
                                                    height: '30px',
                                                    backgroundColor: '#daf4f0',
                                                    color: '#0ab39c',
                                                    fontSize: '14px',
                                                    fontWeight: 'bold',
                                                }}
                                            >
                                                {contact.fullName
                                                    ?.charAt(0)
                                                    ?.toUpperCase() || 'A'}
                                            </div>
                                            {contact?.fullName}
                                        </div>
                                    </td>
                                    <td>{contact?.departmentName}</td>
                                    <td>{contact?.positionTitle}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        );
    };

    return (
        <div>
            {renderContactTable(purchasingContacts, 'Phòng mua hàng')}
            {renderContactTable(usageContacts, 'Phòng sử dụng')}
        </div>
    );
};

export default Tab2;
