'use client';

import FormController from '@/components/common/FormController';
import { FormProvider, useForm } from 'react-hook-form';
import { Button, Card, CardBody, Col, Row } from 'reactstrap';

interface StatusCardProps {
    icon: string;
    count: number;
    label: string;
    bgColor: string;
    textColor: string;
}

const StatusCard = ({
    icon,
    count,
    label,
    bgColor,
    textColor,
}: StatusCardProps) => (
    <Card className='border-0 rounded-1'>
        <CardBody
            className='d-flex align-items-center gap-3 p-3'
            style={{ border: '1px solid #ecedee' }}
        >
            <div
                className='rounded-circle d-flex align-items-center justify-content-center'
                style={{
                    width: '42px',
                    height: '42px',
                    backgroundColor: bgColor,
                }}
            >
                <i className={`${icon} fs-5`} style={{ color: textColor }}></i>
            </div>
            <div>
                <p className='mb-0 text-muted'>{label}</p>
                <h4 className='mb-1 fs-4 fw-semibold'>{count}</h4>
            </div>
        </CardBody>
    </Card>
);

export default function SelectWord() {
    const statuses = [
        {
            icon: 'ri-task-line',
            count: 181,
            label: 'Tổng số công việc',
            bgColor: '#e0f2fe',
            textColor: '#0ea5e9',
        },
        {
            icon: 'ri-time-line',
            count: 16,
            label: 'Đang thực hiện',
            bgColor: '#fef3c7',
            textColor: '#d97706',
        },
        {
            icon: 'ri-check-line',
            count: 15,
            label: 'Hoàn thành',
            bgColor: '#dcfce7',
            textColor: '#16a34a',
        },
        {
            icon: 'ri-error-warning-line',
            count: 9,
            label: 'Quá hạn',
            bgColor: '#fee2e2',
            textColor: '#dc2626',
        },
        {
            icon: 'ri-file-list-line',
            count: 6,
            label: 'Sắp quá hạn',
            bgColor: '#f3f4f6',
            textColor: '#4b5563',
        },
    ];

    const methods = useForm();

    return (
        <Card className='mb-4 p-4'>
            <FormProvider {...methods}>
                <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                    <div className='d-flex gap-2 mb-4'>
                        <FormController
                            controlType='nativeSelect'
                            name='company'
                            data={['Công ty AISEC CyberSecurity']}
                            style={{ width: '300px' }}
                        />
                        <FormController
                            controlType='nativeSelect'
                            name='department'
                            data={['Tất cả phòng ban']}
                            style={{ maxWidth: '200px' }}
                        />
                        <FormController
                            controlType='nativeSelect'
                            name='employee'
                            data={['Tất cả nhân viên']}
                            style={{ maxWidth: '200px' }}
                        />
                        <FormController
                            controlType='nativeSelect'
                            name='time'
                            data={['Tháng này']}
                            style={{ maxWidth: '150px' }}
                        />
                    </div>
                    <div style={{ display: 'flex', gap: '7px' }}>
                        <div>
                            <Button
                                style={{
                                    backgroundColor: '#0ab39c',
                                    color: '#ffffff',
                                    border: 'none',
                                }}
                            >
                                + Tạo mới báo cáo
                            </Button>
                        </div>
                        <div>
                            <Button
                                style={{
                                    backgroundColor: '#e7f8f6',
                                    color: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className=' ri-settings-4-line'></i>
                            </Button>
                        </div>
                    </div>
                </div>
            </FormProvider>

            <Row className='g-3'>
                {statuses.map((status, index) => (
                    <Col
                        key={index}
                        xs={12}
                        sm={6}
                        lg
                        style={{ minWidth: '200px' }}
                    >
                        <StatusCard {...status} />
                    </Col>
                ))}
            </Row>
        </Card>
    );
}
