'use client';
import {
    useRestoreCustomer,
    useSearchRestoreCustomer,
} from '@/apis/customer/customer.api';
import {
    SearchCustomerResponse,
    SearchRestores,
} from '@/apis/customer/customer.type';
import dynamic from 'next/dynamic';

import ButtonHeader from '@/components/common/ButtonHeader';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { ROUTES } from '@/lib/routes';
import { getOneMonthAgo, getToday } from '@/utils/time';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import { Button, Card, CardHeader, Col, Container, Row } from 'reactstrap';
import useGetColumn from '../_hook/useGetColumn';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then(
            (mod) => mod.default,
        ),
    {
        ssr: false,
    },
) as typeof import('@/components/common/MantineReactTable').default;
const RestoreCustomer = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const methods = useForm<SearchRestores>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control } = methods;

    const [
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { mutate: restoreCustomers } = useRestoreCustomer({
        onSuccess: () => {
            toast.success('Khôi phục khách hàng thành công');
            refetch();
            setSelectedIds([]);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleRestoreCustomer = (
        customer: SearchCustomerResponse | undefined,
    ) => {
        if (customer) {
            restoreCustomers({ ids: [customer.id] });
        }
    };

    const handleRestoreSelected = () => {
        if (selectedIds.length > 0) {
            restoreCustomers({ ids: selectedIds });
        } else {
            toast.warning('Vui lòng chọn ít nhất một khách hàng để khôi phục');
        }
    };

    const columns = useGetColumn({
        page: 'restore',
        onRestore: handleRestoreCustomer,
    });

    const { data, refetch, isLoading } = useSearchRestoreCustomer({
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    useEffect(() => {}, [
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ]);

    useEffect(() => {
        if (PageNumber && PageSize) {
            refetch();
        }
    }, [PageNumber, PageSize, refetch]);

    const { items: listRestoreCustomer = [], totalItems } = data ?? {};

    const handlePageChange = (page: number) => {
        methods.setValue('PageNumber', page);
    };

    const handlePageSizeChange = (size: number) => {
        methods.setValue('PageSize', size);
        methods.setValue('PageNumber', 1);

        setTimeout(() => {
            refetch();
        }, 0);
    };

    useEffect(() => {
        if (PageSize) {
            refetch();
        }
    }, [PageSize, refetch]);

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showCreateButton={false}
                        showImportButton={false}
                        showExportButton={false}
                        showDateFilters={true}
                    />
                </Col>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex '>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên khách hàng...'
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        <Button
                                            color='success'
                                            onClick={handleRestoreSelected}
                                            disabled={selectedIds.length === 0}
                                        >
                                            {selectedIds.length > 0
                                                ? `Khôi phục (${selectedIds.length})`
                                                : 'Khôi phục'}
                                        </Button>
                                        <Button
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                            }}
                                            onClick={() => {
                                                router.push(
                                                    ROUTES.CRM.CUSTOMERS.INDEX,
                                                );
                                            }}
                                        >
                                            <i className=' ri-arrow-go-back-line'></i>
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listRestoreCustomer}
                            isLoading={isLoading}
                            totalItems={totalItems ?? 0}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                            tableProps={{
                                enableRowSelection: true,
                                enableMultiRowSelection: true,
                                selectAllMode: 'page',
                                initialState: {
                                    pagination: {
                                        pageIndex: (PageNumber || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                },
                                state: {
                                    pagination: {
                                        pageIndex: (PageNumber || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listRestoreCustomer.findIndex(
                                                    (customer) =>
                                                        customer.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                },
                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listRestoreCustomer.findIndex(
                                                            (customer) =>
                                                                customer.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds = Object.keys(
                                        selectedRows,
                                    )
                                        .filter((key) => selectedRows[key])
                                        .map(
                                            (key) =>
                                                listRestoreCustomer[
                                                    parseInt(key)
                                                ].id,
                                        );

                                    setSelectedIds(newSelectedIds);
                                },
                                mantineTableBodyRowProps: ({ row }) => ({
                                    onClick: (e) => {
                                        if (
                                            (e.target as HTMLElement).closest(
                                                '.mantine-Checkbox-root',
                                            ) ||
                                            (e.target as HTMLElement)
                                                .tagName === 'INPUT'
                                        ) {
                                            e.stopPropagation();
                                            return;
                                        }
                                        router.push(
                                            ROUTES.CRM.PERSONAL.DETAIL.replace(
                                                ':id',
                                                row.original.id,
                                            ),
                                        );
                                    },
                                    sx: {
                                        cursor: 'pointer',
                                    },
                                }),
                                onPaginationChange: (updater) => {
                                    let newPagination;
                                    if (typeof updater === 'function') {
                                        newPagination = updater({
                                            pageIndex: (PageNumber || 1) - 1,
                                            pageSize: PageSize || 10,
                                        });
                                    } else {
                                        newPagination = updater;
                                    }

                                    if (
                                        newPagination.pageIndex !==
                                        (PageNumber || 1) - 1
                                    ) {
                                        handlePageChange(
                                            newPagination.pageIndex + 1,
                                        );
                                    }

                                    if (
                                        newPagination.pageSize !==
                                        (PageSize || 10)
                                    ) {
                                        handlePageSizeChange(
                                            newPagination.pageSize,
                                        );
                                    }
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};
export default RestoreCustomer;
