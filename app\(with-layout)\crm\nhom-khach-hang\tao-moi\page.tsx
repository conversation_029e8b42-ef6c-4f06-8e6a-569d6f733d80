'use client';

import { ICustomerGroups } from '@/apis/customer-groups/customer-groups.type';
import CustomerGroupsForm from '../_components/CustomerGroupsForm';
import { useCreateCustomerGroups } from '@/apis/customer-groups/customer-groups.api';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'react-toastify';
import { convertFormValueToPayload } from '@/utils/convert-data';
// import { yupResolver } from '@hookform/resolvers/yup';
// import * as yup from 'yup';

// const schema = yup.object().shape({
//     name: yup.string().required('Vui lòng nhập tên doanh nghiệp'),
//     description: yup.string(),
//     website: yup.string(),
//     facebook: yup.string(),
//     youtube: yup.string(),
//     linkedInPage: yup.string(),
//     taxCode: yup.string(),
//     annualRevenue: yup.number().default(0),
//     numberOfEmployees: yup.number().default(0),
//     industryId: yup.string(),
//     companyType: yup.number().default(1),
//     dateOfEstablishment: yup.string(),
//     ownerId: yup.string(),
//     // Thông tin liên kết tạm thời
//     linkedPerson1: yup.string(),
//     linkedPerson2: yup.string(),
//     linkedRole1: yup.string(),
//     linkedRole2: yup.string(),
//     // Thông tin địa chỉ tạm thời
//     country: yup.string(),
//     district: yup.string(),
//     city: yup.string(),
//     ward: yup.string(),
//     address: yup.string(),
// });

export default function CreateCustomerGroups() {
    const router = useRouter();
    const { mutate: createCustomerGroups } = useCreateCustomerGroups({
        onSuccess: () => {
            toast.success('Tạo mới nhóm khách hàng thành công');
            router.push(ROUTES.CRM.CUSTOMER_GROUPS.INDEX);
        },
        onError: () => {
            toast.error('Tạo mới nhóm khách hàng thất bại');
        },
    });

    const handleCreateCustomerGroups = (data: ICustomerGroups) => {
        const payload = convertFormValueToPayload(data, []);

        createCustomerGroups(payload as ICustomerGroups);
    };

    const handleCancel = () => {
        router.push(ROUTES.CRM.CUSTOMER_GROUPS.INDEX);
    };

    return (
        <CustomerGroupsForm
            onSubmit={handleCreateCustomerGroups}
            onCancel={handleCancel}
        />
    );
}
