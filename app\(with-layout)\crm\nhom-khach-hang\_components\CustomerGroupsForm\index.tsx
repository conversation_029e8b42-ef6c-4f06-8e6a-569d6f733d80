'use client';

import { ICustomerGroups } from '@/apis/customer-groups/customer-groups.type';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { FormProvider, useForm } from 'react-hook-form';
import { Button, Card, Col, FormGroup, Label, Row } from 'reactstrap';
import ListCustomer from './ListCustomer';

import Avatar from '@/components/common/Avatar';

interface CustomerGroupsFormProps {
    initValue?: ICustomerGroups;
    onSubmit?: (data: ICustomerGroups) => void;
    onEdit?: (data: ICustomerGroups) => void;
    onCancel: () => void;
    mode?: 'create' | 'edit';
}

const CustomerGroupsForm = ({
    initValue,
    onSubmit,
    // onEdit,
    onCancel,
    mode = 'create',
}: CustomerGroupsFormProps) => {
    const handleFormSubmit = (data: ICustomerGroups) => {
        if (onSubmit) {
            onSubmit(data);
        }
    };

    const methods = useForm<ICustomerGroups>({
        defaultValues: initValue || {},
    });

    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                <CollapseApp title='THÔNG TIN CHÍNH'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='11'>
                            <FormController
                                controlType='textInput'
                                name='name'
                                label='Tên nhóm khách hàng'
                                placeholder='Nhập tên nhóm khách hàng'
                                required={true}
                            />
                        </Col>
                        <Col md='11'>
                            <FormController
                                controlType='textarea'
                                name='description'
                                label='Mô tả'
                                placeholder='Nhập mô tả nhóm khách hàng'
                            />
                        </Col>
                        <Col md='11'>
                            <FormGroup>
                                <Label>
                                    <strong>Ảnh đại diện</strong>
                                </Label>
                                <div style={{ height: '50px' }}>
                                    <Avatar />
                                </div>
                            </FormGroup>
                        </Col>
                    </Row>
                </CollapseApp>
                <CollapseApp title='THÔNG TIN LIÊN KẾT'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='11'>
                            <ListCustomer title='Khách hàng' />
                        </Col>
                    </Row>
                </CollapseApp>

                <Row className='g-3 justify-content-around'>
                    <Col md='11' className='mt-4 d-flex justify-content-end'>
                        <Button
                            color='danger'
                            className='me-2'
                            type='button'
                            onClick={onCancel}
                        >
                            Hủy
                        </Button>
                        {mode === 'create' && (
                            <Button
                                color='success'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Tạo mới
                            </Button>
                        )}
                        {mode === 'edit' && (
                            <Button color='success'>Chỉnh sửa</Button>
                        )}
                    </Col>
                </Row>
            </Card>
        </FormProvider>
    );
};

export default CustomerGroupsForm;
