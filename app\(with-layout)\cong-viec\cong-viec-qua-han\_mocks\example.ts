export interface ITask {
    id: string;
    type?: string; // <PERSON><PERSON>i nhóm công việc chính
    name?: string;
    group?: string;
    overdueTime?: string;
    deadline?: string;
    createdBy?: string;
    subRows?: ITask[];
}

export const listTask: ITask[] = [
    {
        id: '1',
        type: 'Triển khai TCBS',
        subRows: [
            {
                id: '1-0',
                name: 'POC với khách hàng',
                group: 'Giới thiệu sản phẩm',
                overdueTime: '1 ngày',
                deadline: '09-02-2025',
                createdBy: 'ĐH',
            },
        ],
    },
    {
        id: '2',
        type: 'Triển khai SHB',
        subRows: [
            {
                id: '2-0',
                name: 'POC với khách hàng',
                group: 'Giới thiệu sản phẩm',
                overdueTime: '1 ngày',
                deadline: '10-08-2025',
                createdBy: 'ĐH',
            },
        ],
    },
];
