import {
    Opportunity,
    KanbanColumn,
    AdvancedFilter,
    SalesOpportunity,
} from './types';

export const mockOpportunities: Opportunity[] = [
    {
        id: '1',
        title: '<PERSON><PERSON> hội mua máy móc',
        revenue: 572678,
        dueDate: '07/01/2025',
        priority: 'medium',
        stage: 'new',
        assignees: [
            { avatar: '/assets/images/users/avatar-1.jpg', name: 'User 1' },
            { avatar: '/assets/images/users/avatar-2.jpg', name: 'User 2' },
        ],
    },
    {
        id: '2',
        title: '<PERSON><PERSON><PERSON><PERSON> đổi số sản xuất',
        revenue: 1572678,
        dueDate: '07/01/2025',
        priority: 'low',
        stage: 'new',
        assignees: [
            { avatar: '/assets/images/users/avatar-3.jpg', name: 'User 3' },
            { avatar: '/assets/images/users/avatar-4.jpg', name: 'User 4' },
        ],
    },
    {
        id: '3',
        title: '<PERSON><PERSON><PERSON> đồng nguyên vật liệu',
        revenue: 572678,
        dueDate: '07/01/2025',
        priority: 'medium',
        stage: 'evaluated',
        assignees: [],
    },
    {
        id: '4',
        title: 'Lắp đặt băng chuyền',
        revenue: 1572678,
        dueDate: '07/01/2025',
        priority: 'low',
        stage: 'negotiating',
        assignees: [],
    },
    {
        id: '5',
        title: 'Cung cấp robot hàn',
        revenue: 972678,
        dueDate: '07/01/2025',
        priority: 'high',
        stage: 'success',
        assignees: [],
    },
    {
        id: '6',
        title: 'Cung cấp dịch vụ Cloud',
        revenue: 972678,
        dueDate: '07/01/2025',
        priority: 'low',
        stage: 'failed',
        assignees: [],
    },
];

export const mockKanbanColumns: KanbanColumn[] = [
    {
        id: 'new',
        title: 'CƠ HỘI MỚI',
        count: 2,
        opportunities: mockOpportunities.filter((opp) => opp.stage === 'new'),
        color: 'success',
    },
    {
        id: 'evaluated',
        title: 'ĐÃ ĐÁNH GIÁ CHẤT LƯỢNG',
        count: 3,
        opportunities: mockOpportunities.filter(
            (opp) => opp.stage === 'evaluated',
        ),
        color: 'info',
    },
    {
        id: 'negotiating',
        title: 'ĐÀM PHÁN',
        count: 2,
        opportunities: mockOpportunities.filter(
            (opp) => opp.stage === 'negotiating',
        ),
        color: 'warning',
    },
    {
        id: 'success',
        title: 'CƠ HỘI THÀNH CÔNG',
        count: 1,
        opportunities: mockOpportunities.filter(
            (opp) => opp.stage === 'success',
        ),
        color: 'primary',
    },
    {
        id: 'failed',
        title: 'CƠ HỘI THẤT BẠI',
        count: 1,
        opportunities: mockOpportunities.filter(
            (opp) => opp.stage === 'failed',
        ),
        color: 'danger',
    },
];

export const mockAdvancedFilters: AdvancedFilter[] = [
    {
        id: '1',
        name: 'Bộ lọc 1',
        conditions: [
            {
                field: 'stage',
                operator: 'contains',
                value: 'Hoạt động',
            },
        ],
    },
    {
        id: '2',
        name: 'Bộ lọc 2',
        conditions: [
            {
                field: 'createdDate',
                operator: 'before',
                value: '19/02/2025',
            },
        ],
    },
];

export const mockData: SalesOpportunity[] = [
    {
        name: 'Nguyễn Văn Ánh',
        email: '<EMAIL>',
        phone: '0354629272',
        business: 'Garena',
        stage: 'tiềm năng',
        status: 'abc',
    },
    {
        name: 'Lê Văn Cường',
        email: '<EMAIL>',
        phone: '0543245543',
        business: 'Nashtech',
        stage: 'Khách hàng',
        status: 'abc',
    },
];
