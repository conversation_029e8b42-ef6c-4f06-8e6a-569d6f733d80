import {
    IDealResponse,
    IPipelineStage,
} from '@/apis/opportunity/opportunity.type';
import { DropResult } from '@hello-pangea/dnd';
import React, { createContext, ReactNode, useContext, useState } from 'react';

interface KanbanContextType {
    columns: {
        [key: string]: {
            id: string;
            title: string;
            items: IDealResponse[];
        };
    };
    setColumns: React.Dispatch<
        React.SetStateAction<{
            [key: string]: {
                id: string;
                title: string;
                items: IDealResponse[];
            };
        }>
    >;
    onDragEnd: (result: DropResult) => void;
}

const KanbanContext = createContext<KanbanContextType | undefined>(undefined);

interface KanbanProviderProps {
    children: ReactNode;
    pipelineStages: IPipelineStage[];
    opportunities: { [key: string]: IDealResponse[] };
}

export const KanbanProvider: React.FC<KanbanProviderProps> = ({
    children,
    pipelineStages,
    opportunities,
}) => {
    // Khởi tạo columns từ pipelineStages và opportunities
    const initialColumns = pipelineStages.reduce(
        (acc, stage) => {
            acc[stage.id] = {
                id: stage.id,
                title: stage.name,
                items: opportunities[stage.id] || [],
            };
            return acc;
        },
        {} as {
            [key: string]: {
                id: string;
                title: string;
                items: IDealResponse[];
            };
        },
    );

    const [columns, setColumns] = useState(initialColumns);

    const onDragEnd = (result: DropResult) => {
        const { source, destination } = result;

        // Nếu không có điểm đến hoặc điểm đến giống điểm đi, không làm gì cả
        if (
            !destination ||
            (source.droppableId === destination.droppableId &&
                source.index === destination.index)
        ) {
            return;
        }

        // Lấy cột nguồn và cột đích
        const sourceColumn = columns[source.droppableId];
        const destColumn = columns[destination.droppableId];

        // Tạo bản sao của các mảng items
        const sourceItems = [...sourceColumn.items];
        const destItems =
            source.droppableId === destination.droppableId
                ? sourceItems
                : [...destColumn.items];

        // Xóa item từ mảng nguồn
        const [removed] = sourceItems.splice(source.index, 1);

        // Thêm item vào mảng đích
        destItems.splice(destination.index, 0, removed);

        // Cập nhật state
        setColumns({
            ...columns,
            [source.droppableId]: {
                ...sourceColumn,
                items: sourceItems,
            },
            [destination.droppableId]: {
                ...destColumn,
                items: destItems,
            },
        });

        // Ở đây bạn có thể gọi API để cập nhật trạng thái của item trên server
        // Ví dụ: updateItemStatus(removed.id, destination.droppableId);
    };

    return (
        <KanbanContext.Provider value={{ columns, setColumns, onDragEnd }}>
            {children}
        </KanbanContext.Provider>
    );
};

export const useKanban = () => {
    const context = useContext(KanbanContext);
    if (context === undefined) {
        throw new Error('useKanban must be used within a KanbanProvider');
    }
    return context;
};
