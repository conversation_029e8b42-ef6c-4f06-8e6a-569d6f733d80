'use client';

import { useParams } from 'next/navigation';
import FormProducts from '../../_components/FormProducts';
import { useDetailProduct } from '@/apis/product/product.api';
import { Spinner } from 'reactstrap';

const DetailProducts = () => {
    const params = useParams();
    const id = params.id as string;
    const { data: product } = useDetailProduct(id);
    const initValue = product?.data;
    if (!initValue) {
        return <Spinner />;
    }
    return <FormProducts page='chi-tiet' initValue={initValue} />;
};
export default DetailProducts;
