'use client';

import { useGetCurrentUser } from '@/apis/auth/auth.api';
import BreadCrumb from '@/components/common/BreadCrumb';
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { LAYOUT_MODE_TYPES } from '@/constants/layout';
import { changeInfoUserAuth } from '@/slices/auth/reducer';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Container } from 'reactstrap';
import { createSelector } from 'reselect';

import {
    changeLayout,
    changeLayoutMode,
    changeLayoutPosition,
    changeLayoutWidth,
    changeLeftsidebarSizeType,
    changeLeftsidebarViewType,
    changeSidebarImageType,
    changeSidebarTheme,
    changeSidebarVisibility,
    changeTopbarTheme,
} from '../../slices/thunk';
import type { AppDispatch, RootState } from '../../store/store';

function MainLayout({ children }: { children: React.ReactNode }) {
    const dispatch = useDispatch<AppDispatch>();
    const selectLayoutState = (state: RootState) => state.Layout;
    const selectLayoutProperties = createSelector(
        selectLayoutState,
        (layout) => ({
            layoutType: layout.layoutType,
            leftSidebarType: layout.leftSidebarType,
            layoutModeType: layout.layoutModeType,
            layoutWidthType: layout.layoutWidthType,
            layoutPositionType: layout.layoutPositionType,
            topbarThemeType: layout.topbarThemeType,
            leftsidbarSizeType: layout.leftsidbarSizeType,
            leftSidebarViewType: layout.leftSidebarViewType,
            leftSidebarImageType: layout.leftSidebarImageType,
            sidebarVisibilitytype: layout.sidebarVisibilitytype,
        }),
    );
    const {
        layoutType,
        leftSidebarType,
        layoutModeType,
        layoutWidthType,
        layoutPositionType,
        topbarThemeType,
        leftsidbarSizeType,
        leftSidebarViewType,
        leftSidebarImageType,
        sidebarVisibilitytype,
    } = useSelector(selectLayoutProperties);

    const user = useSelector((state: RootState) => state.Auth.user);

    // Gọi API getCurrentUser và tự động update Redux khi thành công
    const { data: userData } = useGetCurrentUser({
        enabled: !user?.id,
    });

    useEffect(() => {
        if (userData && !user) {
            dispatch(
                changeInfoUserAuth({
                    user: userData,
                }),
            );
        }
    }, [userData, dispatch, user]);

    // Cập nhật các thiết lập layout
    useEffect(() => {
        if (
            layoutType ||
            leftSidebarType ||
            layoutModeType ||
            layoutWidthType ||
            layoutPositionType ||
            topbarThemeType ||
            leftsidbarSizeType ||
            leftSidebarViewType ||
            leftSidebarImageType ||
            sidebarVisibilitytype
        ) {
            window.dispatchEvent(new Event('resize'));
            dispatch(changeLeftsidebarViewType(leftSidebarViewType));
            dispatch(changeLeftsidebarSizeType(leftsidbarSizeType));
            dispatch(changeSidebarTheme(leftSidebarType));
            dispatch(changeLayoutMode(layoutModeType));
            dispatch(changeLayoutWidth(layoutWidthType));
            dispatch(changeLayoutPosition(layoutPositionType));
            dispatch(changeTopbarTheme(topbarThemeType));
            dispatch(changeLayout(layoutType));
            dispatch(changeSidebarImageType(leftSidebarImageType));
            dispatch(changeSidebarVisibility(sidebarVisibilitytype));
        }
    }, [
        layoutType,
        leftSidebarType,
        layoutModeType,
        layoutWidthType,
        layoutPositionType,
        topbarThemeType,
        leftsidbarSizeType,
        leftSidebarViewType,
        leftSidebarImageType,
        sidebarVisibilitytype,
        dispatch,
    ]);

    // Hàm thay đổi layout mode
    const onChangeLayoutMode = (value: LAYOUT_MODE_TYPES) => {
        dispatch(changeLayoutMode(value));
    };

    const [headerClass, setHeaderClass] = useState('');
    // Cập nhật class cho header khi cuộn trang
    useEffect(() => {
        window.addEventListener('scroll', scrollNavigation, true);
        return () => {
            window.removeEventListener('scroll', scrollNavigation, true);
        };
    }, []);

    function scrollNavigation() {
        const scrollup = document.documentElement.scrollTop;
        if (scrollup > 50) {
            setHeaderClass('topbar-shadow');
        } else {
            setHeaderClass('');
        }
    }

    useEffect(() => {
        const humberIcon = document.querySelector(
            '.hamburger-icon',
        ) as HTMLElement;
        if (
            sidebarVisibilitytype === 'show' ||
            layoutType === 'vertical' ||
            layoutType === 'twocolumn'
        ) {
            humberIcon.classList.remove('open');
        } else {
            if (humberIcon) {
                humberIcon.classList.add('open');
            }
        }
    }, [sidebarVisibilitytype, layoutType]);

    return (
        <>
            <div id='layout-wrapper'>
                <Header
                    headerClass={headerClass}
                    layoutModeType={layoutModeType}
                    onChangeLayoutMode={onChangeLayoutMode}
                />
                <Sidebar layoutType={layoutType} />
                <div className='main-content'>
                    <div className='page-content'>
                        <Container fluid>
                            <BreadCrumb />
                            {children}
                        </Container>
                    </div>
                </div>
            </div>
        </>
    );
}
export default function WithLayoutWrapper({
    children,
}: {
    children: React.ReactNode;
}) {
    return <MainLayout>{children}</MainLayout>;
}
