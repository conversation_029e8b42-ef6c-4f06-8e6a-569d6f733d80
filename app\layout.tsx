import './globals.css';
import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import 'mantine-react-table/styles.css'; // MRT styles - QUAN TRỌNG!
import '../assets/scss/themes.scss';
import { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ColorSchemeScript } from '@mantine/core';
import ClientProviders from '../components/providers/ClientProviders';
import { ToastContainer } from 'react-toastify';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'CRM System',
    description: 'Customer Relationship Management System',
};

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang='en' suppressHydrationWarning>
            <head>
                <ColorSchemeScript />
            </head>
            <body className={inter.className} suppressHydrationWarning>
                <ClientProviders>
                    {children}
                    <ToastContainer />
                </ClientProviders>
            </body>
        </html>
    );
}
