import { useFormContext } from 'react-hook-form';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { Row, Col } from 'reactstrap';
import useGetOptionCustomer from '@/hooks/useGetOptionCustomer';
import useGetOptionPositionContactId from '@/hooks/useGetOptionPositionContactId';
import useGetOptionPeopleContactId from '@/hooks/useGetOptionPeopleContactId';

const Buyer = () => {
    const { watch } = useFormContext();
    const customers = useGetOptionCustomer();
    const customerId = watch('buyer.customerId');
    const position = useGetOptionPositionContactId(customerId);
    const people = useGetOptionPeopleContactId(customerId);

    return (
        <CollapseApp title='BÊN MUA'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='buyer.customerId'
                            label='Khách hàng'
                            placeholder='Chọn khách hàng'
                            data={customers}
                            required={true}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='buyer.representative'
                            label='Người đại diện'
                            placeholder='Chọn đại diện'
                            data={people}
                            required={true}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='buyer.position'
                            label='Chức vụ'
                            data={position}
                            placeholder='Chọn chức vụ'
                            required={true}
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default Buyer;
