import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, CardHeader, Col } from 'reactstrap';
import DropdownActionMenu from '@/components/common/DropdownActionMenu';
import QuickViewModal from './QuickViewModal';

const FileInfo = () => {
    const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<
        'customer' | 'partner' | null
    >(null);

    const toggleQuickView = (item: 'customer' | 'partner' | null = null) => {
        setIsQuickViewOpen(!isQuickViewOpen);
        setSelectedItem(item);
    };

    const customerData = {
        name: 'Garena',
        companyName: 'Công Ty CP Garena Việt Nam',
        email: '<EMAIL>',
        phone: '*********',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: '<PERSON>i<PERSON><PERSON> năng',
        status: '<PERSON><PERSON><PERSON>',
    };

    const partnerData = {
        name: '<PERSON>are<PERSON>',
        companyName: 'Garena',
        email: '<EMAIL>',
        phone: '0354629272',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: 'Tiềm năng',
        status: 'Mới',
    };

    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {/* File items */}
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <Card className='mb-3'>
                <CardHeader className='d-flex align-items-center'>
                    <h5 className='mb-0 flex-grow-1'>Khách hàng</h5>
                    <DropdownActionMenu
                        actions={[
                            {
                                icon: 'ri-eye-line',
                                label: 'Xem nhanh',
                                onClick: () => toggleQuickView('customer'),
                            },
                            {
                                icon: 'ri-eye-fill',
                                label: 'Xem chi tiết',
                                onClick: () => ({}),
                            },
                            {
                                icon: 'ri-delete-bin-line',
                                label: 'Xóa liên kết',
                                onClick: () => ({}),
                                className: 'text-danger',
                            },
                        ]}
                        toggleIcon='ri-more-2-fill'
                    />
                </CardHeader>
                <CardBody>
                    <h6 className='mb-3'>Công ty CP Garena Việt Nam</h6>
                    <div className='mb-2'>
                        <small className='text-muted d-block'>Email:</small>
                        <div><EMAIL></div>
                    </div>
                    <div className='mb-2'>
                        <small className='text-muted d-block'>
                            Số điện thoại:
                        </small>
                        <div>0354629272</div>
                    </div>
                </CardBody>
            </Card>

            <Card>
                <CardHeader className='d-flex align-items-center'>
                    <h5 className='mb-0 flex-grow-1'>Đối tác thương mại</h5>
                    <DropdownActionMenu
                        actions={[
                            {
                                icon: 'ri-eye-line',
                                label: 'Xem nhanh',
                                onClick: () => toggleQuickView('partner'),
                            },
                            {
                                icon: 'ri-eye-fill',
                                label: 'Xem chi tiết',
                                onClick: () => ({}),
                            },
                            {
                                icon: 'ri-delete-bin-line',
                                label: 'Xóa liên kết',
                                onClick: () => ({}),
                                className: 'text-danger',
                            },
                        ]}
                        toggleIcon='ri-more-2-fill'
                    />
                </CardHeader>
                <CardBody>
                    <h6>Garena</h6>
                    <div className='mb-2'>
                        <small className='text-muted d-block'>Email:</small>
                        <div><EMAIL></div>
                    </div>
                    <div className='mb-2'>
                        <small className='text-muted d-block'>
                            Số điện thoại:
                        </small>
                        <div>0354629272</div>
                    </div>
                </CardBody>
            </Card>

            {/* Quick View Modal */}
            <QuickViewModal
                isOpen={isQuickViewOpen}
                toggle={() => toggleQuickView()}
                data={selectedItem === 'customer' ? customerData : partnerData}
            />
        </Col>
    );
};

export default FileInfo;
