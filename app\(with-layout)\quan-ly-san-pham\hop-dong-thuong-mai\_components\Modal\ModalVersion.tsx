import { ContractNumber } from '@/apis/contracts/contracts.type';
import {
    <PERSON>dal,
    ModalHeader,
    ModalBody,
    Table,
    Button,
    ModalFooter,
} from 'reactstrap';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

interface ModalVersionProps {
    isOpen: boolean;
    toggle: () => void;
    versions?: ContractNumber[];
    title: string;
}

const ModalVersion = ({
    isOpen,
    toggle,
    versions = [],
    title,
}: ModalVersionProps) => {
    const router = useRouter();
    const displayVersions = versions.length > 0 ? versions : [];
    const formatDateTime = (dateTimeString: string): string => {
        if (!dateTimeString) {
            return '';
        }

        const date = dayjs(dateTimeString);
        const time = date.format('HH[h]mm');
        const dateFormatted = date.format('DD/MM/YYYY');

        return `${time} ${dateFormatted}`;
    };
    const handleRouter = (id: string) => {
        if (title === 'Chỉnh sửa') {
            router.push(
                ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.UPDATE.replace(':id', id),
            );
            return;
        }
        // if (title === 'Xem chi tiết') {
        //     router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.DETAIL);
        //     return;
        // }
        return;
    };
    return (
        <Modal isOpen={isOpen} toggle={toggle} size='lg' centered>
            <ModalHeader>
                <h5 className='mb-0 fw-bold'>{title} phiên bản</h5>
            </ModalHeader>
            <ModalBody className='pt-3'>
                <Table responsive borderless className='mb-0'>
                    <thead style={{ backgroundColor: '#f3f6f9' }}>
                        <tr className='border-bottom'>
                            <th className=' border-0 pb-3'>Phiên bản</th>
                            <th className=' border-0 pb-3'>Thời gian</th>
                            <th className=' border-0 pb-3'>Người cập nhật</th>
                            <th className=' border-0 pb-3 text-center'>Xem</th>
                        </tr>
                    </thead>
                    <tbody>
                        {displayVersions.map((version, index) => (
                            <tr
                                key={version.id || index}
                                className='border-bottom'
                            >
                                <td className='py-3 border-0'>
                                    {version.versionNumber}
                                </td>
                                <td className='py-3 border-0 '>
                                    {formatDateTime(version.updateTime)}
                                </td>
                                <td className='py-3 border-0'>
                                    {version.updatedBy}
                                </td>
                                <td className='py-3 border-0 text-center'>
                                    <Button
                                        color='link'
                                        size='lg'
                                        className='p-1 '
                                        style={{
                                            color: 'black',
                                            border: 'none',
                                        }}
                                        onClick={() => handleRouter(version.id)}
                                    >
                                        <i className='ri-eye-line'></i>
                                    </Button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </Table>
            </ModalBody>
            <ModalFooter>
                <Button
                    onClick={toggle}
                    style={{
                        backgroundColor: '#ffffff',
                        color: '#0ab39c',
                        borderColor: '#0ab39c',
                    }}
                >
                    Đóng
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default ModalVersion;
