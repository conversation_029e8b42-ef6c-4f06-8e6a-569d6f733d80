import React, { useState } from 'react';
import { Button } from 'reactstrap';
import AddCompanyModal from '../modals/AddCompanyModal';

export interface DataCardHeaderProps {
    title: string;
    count: number;
}

const DataCardHeader: React.FC<DataCardHeaderProps> = ({ title, count }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const toggleModal = () => setIsModalOpen(!isModalOpen);

    return (
        <>
            <div className='d-flex align-items-center justify-content-between bg-white p-3 border-bottom'>
                <h6 className='mb-0 d-flex align-items-center'>
                    <i className='bx bx-chevron-down me-1'></i>
                    {title} ({count})
                </h6>
                <div>
                    <Button
                        color='success'
                        outline
                        className='me-2'
                        onClick={toggleModal}
                    >
                        <i className='bx bx-plus me-1'></i>
                        Thêm
                    </Button>
                    <Button color='light' className='btn-icon'>
                        <i className='bx bx-chevron-down'></i>
                    </Button>
                </div>
            </div>

            <AddCompanyModal isOpen={isModalOpen} toggle={toggleModal} />
        </>
    );
};

export default DataCardHeader;
