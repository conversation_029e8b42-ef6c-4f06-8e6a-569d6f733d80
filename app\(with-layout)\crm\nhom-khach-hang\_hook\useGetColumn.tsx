import { ICustomerGroupsResponse } from '@/apis/customer-groups/customer-groups.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import columnDateDefault from '@/components/common/MantineReactTable/columnDateDefault';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '../_types/action.type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: ICustomerGroupsResponse | undefined,
    ) => void;
}

const useGetColumn = ({ onSelectedAction }: GetColumnProps) => {
    const actions: DropdownAction<ICustomerGroupsResponse>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<ICustomerGroupsResponse>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên nhóm khách hàng',
                enableHiding: false,
                size: 350,
            },
            {
                accessorKey: 'description',
                header: 'Mô tả',
                size: 450,
            },
            {
                accessorKey: 'salePerson',
                header: 'Nhân viên kinh doanh',
                size: 230,
            },
            {
                ...(columnDateDefault as MRT_ColumnDef<ICustomerGroupsResponse>),
                accessorKey: 'createdOn',
                header: 'Ngày tạo',
                Cell: ({ row }) => (
                    <FormattedDateTimeWithFormat
                        date={row.original.createdOn}
                    />
                ),
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<ICustomerGroupsResponse>),
                Cell: ({
                    row,
                }: {
                    row: {
                        original: ICustomerGroupsResponse;
                    };
                }) => {
                    return (
                        <div
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        >
                            <DropdownActionMenu
                                actions={actions}
                                data={row.original as ICustomerGroupsResponse}
                                direction='down'
                                end={false}
                            />
                        </div>
                    );
                },
            },
        ],
        [actions],
    );

    return columns;
};

export default useGetColumn;
