'use client';
import ButtonHeader from '@/components/common/ButtonHeader';
import FormController from '@/components/common/FormController';
import MantineTable from '@/components/common/MantineReactTable';
import { ROUTES } from '@/lib/routes';
import { ACTIONS } from '@/types/actions.type';
import { useRouter } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { Card, CardHeader, Col, Container } from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
interface IUserGroupAuthorization {
    name: string;
    MoTaNhomNguoiDung: string;
}

const data = [
    {
        name: 'Nhân viên',
        MoTaNhomNguoiDung: 'Vai trò có đầy đủ các quyền của nhân viên',
    },
    {
        name: 'Quản lý',
        MoTaNhomNguoiDung:
            'Vai trò có đầy đủ các quyền trong hệ thống trừ vai trò đặc biệt của giám đốc',
    },
    {
        name: '<PERSON><PERSON><PERSON><PERSON> đốc',
        MoTaNhomNguoiDung: 'Vai trò có đầy đủ các quyền trong',
    },
];

const UserGroupAuthorization = () => {
    const router = useRouter();
    const handleSelect = (
        action: ACTIONS,
        selectedData: IUserGroupAuthorization | undefined,
    ) => {
        if (!selectedData) {
            return;
        }

        switch (action) {
            case ACTIONS.EDIT: {
                // Handle edit action for selectedData
                break;
            }
            case ACTIONS.DELETE: {
                // Handle delete action for selectedData
                break;
            }
            case ACTIONS.VIEW_DETAIL: {
                // Handle view action for selectedData
                break;
            }
        }
    };

    const column = useGetColumn({ onSelectedAction: handleSelect });

    const methods = useForm({
        defaultValues: {
            pageNumber: 1,
            pageSize: 10,
        },
    });

    const handleCreate = () => {
        router.push(
            ROUTES.DECENTRALIZED_MANAGEMENT.USER_GROUP_AUTHORIZATION.CREATE,
        );
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        showImportButton={false}
                        showExportButton={false}
                        onCreateNew={handleCreate}
                    />
                </Col>
                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex justify-content-between align-items-center'>
                                <div style={{ flex: 1 }}>
                                    <FormController
                                        controlType='textInput'
                                        name='name'
                                        placeholder='Tìm kiếm...'
                                        style={{ width: '360px' }}
                                    />
                                </div>

                                <div>
                                    <FormController
                                        controlType='select'
                                        name='ungDung'
                                        placeholder='Tất cả'
                                        data={[
                                            {
                                                label: 'CRM',
                                                value: '1',
                                            },
                                            {
                                                label: 'Quy trình công việc',
                                                value: '2',
                                            },
                                            {
                                                label: 'Quản lý công việc',
                                                value: '3',
                                            },
                                        ]}
                                        clearable={true}
                                        style={{ width: '200px' }}
                                    />
                                </div>
                            </div>
                        </CardHeader>
                        {/* {isLoading ? (
                            <div className='p-4'>
                                <TableSkeleton
                                    rows={10}
                                    columns={columns.length}
                                />
                            </div>
                        ) : ( */}
                        <MantineTable
                            columns={column}
                            data={data}
                            totalItems={1}
                            onPageChange={(page: number) => {
                                console.error(page);
                            }}
                            onPageSizeChange={(size: number) => {
                                console.error(size);
                            }}
                        />
                        {/* )} */}
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};

export default UserGroupAuthorization;
