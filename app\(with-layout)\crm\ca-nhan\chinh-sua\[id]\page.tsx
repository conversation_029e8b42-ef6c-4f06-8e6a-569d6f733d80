'use client';
import {
    useGetContactDetail,
    useUpdateContact,
} from '@/apis/contact/contact.api';
import { IContact } from '@/apis/contact/contact.type';
import { KEYS_TO_CONTACT } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';
import PersonalForm from '../../_components/PersonalForm';

const EditPersonal = () => {
    const router = useRouter();

    const params = useParams();
    const id = params.id as string;
    const { data: contactDetail, isLoading } = useGetContactDetail(id, {
        isUpdate: true,
    });
    const { mutate: updatePersonal } = useUpdateContact({
        onSuccess: () => {
            toast.success('Chỉnh sửa thông tin cá nhân thành công');
            router.push(ROUTES.CRM.PERSONAL.INDEX);
        },
        onError: () => {
            toast.error('Chỉnh sửa thông tin cá nhân thất bại');
        },
    });

    const handleUpdatePersonal = (data: IContact) => {
        data.id = id;

        const payload = convertFormValueToPayload(data, KEYS_TO_CONTACT);

        updatePersonal(payload as IContact);
    };

    const handleCancel = () => {
        router.push(ROUTES.CRM.PERSONAL.INDEX);
    };
    if (isLoading) {
        return <Spinner />;
    }
    return (
        <PersonalForm
            initValue={convertPayloadToFormValue(contactDetail) as IContact}
            onEdit={handleUpdatePersonal}
            onCancel={handleCancel}
            mode='edit'
        />
    );
};

export default EditPersonal;
