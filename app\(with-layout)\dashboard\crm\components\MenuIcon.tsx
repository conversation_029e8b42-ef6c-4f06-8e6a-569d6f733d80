import { Card, Col, Row } from 'reactstrap';

const MenuIcon = () => {
    const menuItems = [
        {
            title: 'SỐ LƯỢNG KHÁCH HÀNG TẠO MỚI',
            icon: 'ri-contacts-book-line',
            number: 30,
            percent: 7.25,
            isIncrease: true,
        },
        {
            title: 'SỐ LƯỢNG KHÁCH HÀNG ĐÃ MUA',
            icon: 'ri-bell-line',
            number: 9,
            percent: 7.25,
            isIncrease: true,
        },
        {
            title: 'SỐ LƯỢNG KHÁCH HÀNG TIỀM NĂNG',
            icon: 'ri-user-star-line',
            number: 12,
            percent: 20.4,
            isIncrease: false,
        },
        {
            title: 'SỐ LƯỢNG KHÁCH HÀNG MUA LẠI',
            icon: 'ri-refresh-line',
            number: 6,
            percent: 7.25,
            isIncrease: false,
        },
    ];

    return (
        <Card className='p-3'>
            <h1 className='mb-4'>Tổng quan khách hàng</h1>
            <Row className='g-3'>
                {menuItems.map((item, index) => (
                    <Col xs={12} sm={6} md={6} lg={6} xl={3} key={index}>
                        <Card className='h-100 border'>
                            <div className='card-body d-flex flex-column justify-content-between'>
                                <p className='text-muted fs-11 mb-3'>
                                    {item.title}
                                </p>
                                <Row className='align-items-center'>
                                    <Col
                                        lg={3}
                                        sm={3}
                                        xs={3}
                                        className='text-center'
                                    >
                                        <i
                                            className={`${item.icon}`}
                                            style={{ fontSize: '30px' }}
                                        ></i>
                                    </Col>
                                    <Col lg={4} sm={4} xs={4}>
                                        <h4
                                            className='mb-0 text-nowrap'
                                            style={{ fontSize: '22px' }}
                                        >
                                            {item.number}
                                        </h4>
                                    </Col>
                                    <Col
                                        lg={5}
                                        sm={5}
                                        xs={5}
                                        className='text-center'
                                    >
                                        <span
                                            className={`badge ${item.isIncrease ? 'text-success' : 'text-danger'} fs-12`}
                                        >
                                            <i
                                                className={`${item.isIncrease ? 'ri-arrow-right-up-line' : 'ri-arrow-right-down-line'} fs-12 align-middle me-1`}
                                            ></i>
                                            {item.percent}%
                                        </span>
                                    </Col>
                                </Row>
                            </div>
                        </Card>
                    </Col>
                ))}
            </Row>
        </Card>
    );
};

export default MenuIcon;
