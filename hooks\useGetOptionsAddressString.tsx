import {
    useGetDistricts,
    useGetProvinces,
    useGetWards,
} from '@/apis/setting/setting.api';
import { Option } from '@/types/app.type';
import { useMemo } from 'react';

interface GetOptionsAddressProps {
    provinceName?: string;
    districtName?: string;
}

const useGetOptionsAddressString = ({
    provinceName = '',
    districtName = '',
}: GetOptionsAddressProps) => {
    const { data: dataProvinces } = useGetProvinces();
    const { data: dataDistricts } = useGetDistricts({
        provinceName,
    });
    const { data: dataWards } = useGetWards({
        districtName,
    });

    const province = useMemo<Option[]>(() => {
        if (Array.isArray(dataProvinces) && dataProvinces.length > 0) {
            return dataProvinces.map((province) => ({
                label: province.name,
                value: province.name,
            }));
        }
        return [];
    }, [dataProvinces]);

    const district = useMemo<Option[]>(() => {
        if (Array.isArray(dataDistricts) && dataDistricts.length > 0) {
            return dataDistricts.map((district) => ({
                label: district.name,
                value: district.name,
            }));
        }
        return [];
    }, [dataDistricts]);

    const ward = useMemo<Option[]>(() => {
        if (Array.isArray(dataWards) && dataWards.length > 0) {
            return dataWards.map((ward) => ({
                label: ward.name,
                value: ward.name,
            }));
        }
        return [];
    }, [dataWards]);

    return { province, district, ward };
};

export default useGetOptionsAddressString;
