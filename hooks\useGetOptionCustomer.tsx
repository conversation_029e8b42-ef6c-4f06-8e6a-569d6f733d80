import { useSearchCustomer } from '@/apis/customer/customer.api';
import { Option } from '@/types/app.type';
import { useMemo } from 'react';

const useGetOptionCustomer = () => {
    const { data } = useSearchCustomer({});
    const customers = useMemo<Option[]>(() => {
        if (Array.isArray(data?.items) && data?.items.length > 0) {
            return data?.items.map((parent) => ({
                label: parent.name,
                value: parent.id,
            }));
        }
        return [];
    }, [data]);
    return customers;
};
export default useGetOptionCustomer;
