'use client';

import MantineTable from '@/components/common/MantineReactTable';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo, useState } from 'react';
import { Input } from 'reactstrap';

type DeletedOpportunity = Record<string, string> & {
    id: string;
    name: string;
    date: string;
    assignee: string;
    stage: string;
};

const RestoreOpportunity = () => {
    const [selectedItems] = useState<string[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [startDate, setStartDate] = useState('12/02/2025');
    const [endDate, setEndDate] = useState('12/02/2025');

    // Mock data - thay thế bằng API call thực tế sau
    const deletedOpportunities: DeletedOpportunity[] = useMemo(
        () => [
            {
                id: '1',
                name: '<PERSON><PERSON> hội cung cấp robot hàn',
                date: '16/10/2024 23:00',
                assignee: '<PERSON><PERSON><PERSON>',
                stage: '<PERSON><PERSON> đánh giá chất lượng',
            },
            {
                id: '2',
                name: '<PERSON><PERSON> hội cung cấp robot hàn',
                date: '16/10/2024 23:00',
                assignee: 'Hiền Lương',
                stage: 'Đã đánh giá chất lượng',
            },
            {
                id: '3',
                name: 'Cơ hội cung cấp robot hàn',
                date: '16/10/2024 23:00',
                assignee: 'Hiền Lương',
                stage: 'Đã đánh giá chất lượng',
            },
            {
                id: '4',
                name: 'Cơ hội cung cấp robot hàn',
                date: '16/10/2024 23:00',
                assignee: 'Hiền Lương',
                stage: 'Đã đánh giá chất lượng',
            },
        ],
        [],
    );

    const handleRestore = (selectedIds: string[]) => {
        // Xử lý khôi phục các items đã chọn
        console.error('Restoring items:', selectedIds);
    };

    const columns = useMemo<MRT_ColumnDef<DeletedOpportunity>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên cơ hội',
                size: 200,
                enableSorting: true,
            },
            {
                accessorKey: 'date',
                header: 'Thời gian',
                size: 150,
                enableSorting: true,
            },
            {
                accessorKey: 'assignee',
                header: 'Người thực hiện',
                size: 150,
                enableSorting: false,
            },
            {
                accessorKey: 'stage',
                header: 'Giai đoạn',
                size: 150,
                Cell: ({ cell }) => (
                    <span
                        className='badge me-1'
                        style={{
                            backgroundColor: '#daf4f0',
                            color: '#2fbeab',
                            display: 'inline-block',
                            textAlign: 'center',
                            padding: '4px 8px',
                            fontSize: '12px',
                            fontWeight: 500,
                            borderRadius: '4px',
                        }}
                    >
                        {String(cell.getValue())}
                    </span>
                ),
                enableSorting: true,
            },
            {
                accessorKey: 'actions',
                header: 'Hành động',
                size: 100,
                Cell: ({ row }) => (
                    <button
                        className='btn btn-link p-0 text-primary'
                        style={{ fontSize: '13px' }}
                        onClick={() => handleRestore([row.original.id])}
                    >
                        <i className='ri-refresh-line me-1'></i>
                        Khôi phục
                    </button>
                ),
            },
        ],
        [],
    );

    // Filter data based on search query
    const filteredData = useMemo(() => {
        return deletedOpportunities.filter(
            (item) =>
                item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.assignee.toLowerCase().includes(searchQuery.toLowerCase()),
        );
    }, [deletedOpportunities, searchQuery]);

    return (
        <div className='bg-white rounded p-3'>
            <div className='d-flex align-items-center gap-2 mb-3'>
                <div className='search-box' style={{ width: '350px' }}>
                    <Input
                        type='text'
                        placeholder='Tìm kiếm tên cơ hội hoặc tên liên hệ'
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        style={{
                            backgroundColor: 'white',
                            border: '1px solid #e9ebec',
                            height: '36px',
                            borderRadius: '4px',
                        }}
                    />
                    <i className='ri-search-line search-icon'></i>
                </div>

                <Input
                    type='date'
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    style={{
                        width: '150px',
                        backgroundColor: 'white',
                        border: '1px solid #e9ebec',
                        height: '36px',
                        borderRadius: '4px',
                    }}
                />
                <span>—</span>
                <Input
                    type='date'
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    style={{
                        width: '150px',
                        backgroundColor: 'white',
                        border: '1px solid #e9ebec',
                        height: '36px',
                        borderRadius: '4px',
                    }}
                />

                <button
                    className='btn'
                    style={{
                        backgroundColor: '#0ab39c',
                        color: 'white',
                        height: '36px',
                        borderRadius: '4px',
                    }}
                    onClick={() => handleRestore(selectedItems)}
                >
                    Khôi phục
                </button>
            </div>

            <MantineTable
                columns={columns}
                data={filteredData}
                totalItems={filteredData.length}
            />
        </div>
    );
};

export default RestoreOpportunity;
