import React from 'react';
import { Table } from 'reactstrap';

interface DayInfo {
    day: number;
    month: number;
    year: number;
    isCurrentMonth: boolean;
}

interface Event {
    id: number;
    date: string;
    title: string;
    type: string;
    time?: string;
}

interface MonthViewProps {
    calendarDays: DayInfo[];
    events: Event[];
    onDayClick?: (day: DayInfo) => void;
}

const MonthView: React.FC<MonthViewProps> = ({
    calendarDays,
    events,
    onDayClick,
}) => {
    const getEventsForDay = (day: number, month: number, year: number) => {
        return events.filter((event) => {
            const [eventDay, eventMonth, eventYear] = event.date
                .split('-')
                .map(Number);
            return (
                eventDay === day &&
                eventMonth - 1 === month &&
                eventYear === year
            );
        });
    };

    // Render ô lịch
    const renderCalendarCell = (dayInfo: DayInfo) => {
        const { day, month, year, isCurrentMonth } = dayInfo;
        const dayEvents = getEventsForDay(day, month, year);
        const isToday =
            new Date().getDate() === day &&
            new Date().getMonth() === month &&
            new Date().getFullYear() === year;

        return (
            <td
                key={`${day}-${month}-${year}`}
                className={`position-relative ${isToday ? 'bg-light' : ''}`}
                style={{
                    height: '120px',
                    verticalAlign: 'top',
                    width: '14.28%',
                    padding: '8px',
                    opacity: isCurrentMonth ? 1 : 0.4,
                }}
                onClick={() => onDayClick && onDayClick(dayInfo)}
            >
                <div className='text-end mb-2'>{day}</div>
                {dayEvents.map((event) => (
                    <div
                        key={event.id}
                        className='p-1 mb-1 rounded'
                        style={{
                            backgroundColor:
                                event.type === 'meeting'
                                    ? '#cfe2ff'
                                    : event.type === 'report'
                                      ? '#d1e7dd'
                                      : '#e2f0fb',
                            fontSize: '0.8rem',
                            wordWrap: 'break-word',
                            whiteSpace: 'normal',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                        }}
                    >
                        {event.title}
                    </div>
                ))}
            </td>
        );
    };

    // Chia mảng ngày thành các hàng
    const renderCalendarRows = () => {
        const rows = [];
        for (let i = 0; i < calendarDays.length; i += 7) {
            const weekDays = calendarDays.slice(i, i + 7);
            rows.push(
                <tr key={i}>
                    {weekDays.map((dayInfo) => renderCalendarCell(dayInfo))}
                </tr>,
            );
        }
        return rows;
    };

    return (
        <Table
            bordered
            responsive
            className='mb-0'
            style={{ tableLayout: 'fixed' }}
        >
            <thead>
                <tr className='text-center'>
                    <th style={{ width: '14.28%' }}>Thứ hai</th>
                    <th style={{ width: '14.28%' }}>Thứ ba</th>
                    <th style={{ width: '14.28%' }}>Thứ tư</th>
                    <th style={{ width: '14.28%' }}>Thứ năm</th>
                    <th style={{ width: '14.28%' }}>Thứ sáu</th>
                    <th style={{ width: '14.28%' }}>Thứ bảy</th>
                    <th style={{ width: '14.28%' }}>Chủ nhật</th>
                </tr>
            </thead>
            <tbody>{renderCalendarRows()}</tbody>
        </Table>
    );
};

export default MonthView;
