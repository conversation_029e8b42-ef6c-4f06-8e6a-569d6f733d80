// app/api/auth/logout/route.ts
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST() {
    try {
        const cookieStore = await cookies();

        // Xóa cookies
        cookieStore.delete('access_token');
        cookieStore.delete('refresh_token');

        return NextResponse.json({
            message: 'Logged out successfully',
            status: 200,
        });
    } catch (error) {
        console.error('Logout error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 },
        );
    }
}
