import { Button, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON>Footer, ModalHeader } from 'reactstrap';

interface ModalUpdateProps {
    isOpen: boolean;
    toggle: () => void;
    onReplace?: () => void;
    onNewVersion?: () => void;
}
const ModalUpdate = ({
    isOpen,
    toggle,
    onReplace,
    onNewVersion,
}: ModalUpdateProps) => {
    return (
        <Modal isOpen={isOpen} toggle={toggle} centered>
            <ModalHeader toggle={toggle}>Thông báo</ModalHeader>
            <ModalBody>
                Bạn có muốn cập nhật phiên bản mới không, hay thay thế phiên bản
                cũ
            </ModalBody>
            <ModalFooter>
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '100%',
                        alignItems: 'center',
                    }}
                >
                    <Button
                        onClick={toggle}
                        style={{
                            backgroundColor: '#ffffff',
                            color: '#f2785f',
                            borderColor: '#f2785f',
                        }}
                    >
                        Hủy
                    </Button>
                    <div>
                        <Button
                            style={{
                                backgroundColor: '#0ab39c',
                                color: '#ffffff',
                                borderColor: '#ffffff',
                            }}
                            onClick={onReplace}
                        >
                            Thay thế
                        </Button>
                        <Button
                            style={{
                                backgroundColor: '#0ab39c',
                                color: '#ffffff',
                                borderColor: '#ffffff',
                            }}
                            onClick={onNewVersion}
                        >
                            Phiên bản mới
                        </Button>
                    </div>
                </div>
            </ModalFooter>
        </Modal>
    );
};

export default ModalUpdate;
