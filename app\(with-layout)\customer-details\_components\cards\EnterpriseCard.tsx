import React from 'react';
import { Card, CardBody, Col } from 'reactstrap';
import DataCardHeader from './DataCardHeader';
import HeaderRow from './HeaderRow';
import DataRow from './DataRow';
import ViewAllButton from './ViewAllButton';

const EnterpriseCard: React.FC = () => (
    <div className='mb-4'>
        <Card className='bg-white'>
            <DataCardHeader title='Doanh nghiệp' count={1} />
            <CardBody>
                <div className='row'>
                    <HeaderRow title='Công ty ABC' />
                    <Col xxl={4}>
                        <DataRow
                            label='Email:'
                            value='<EMAIL>'
                            badgeText='+1'
                        />
                    </Col>
                    <Col xxl={4}>
                        <DataRow label='Số điện thoại:' value='--' />
                    </Col>
                </div>
                <ViewAllButton />
            </CardBody>
        </Card>
    </div>
);

export default EnterpriseCard;
