'use client';

import FormController from '@/components/common/FormController';
import {
    Button,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Row,
} from 'reactstrap';

interface ModalSetUpCalculationFormulaProps {
    isOpen: boolean;
    onToggle: () => void;
}

const ModalSetUpCalculationFormula = (
    props: ModalSetUpCalculationFormulaProps,
) => {
    const { isOpen, onToggle } = props;

    return (
        <Modal isOpen={isOpen} toggle={onToggle} size='lg'>
            <ModalHeader toggle={onToggle}>
                Thiết lập công thức tính
            </ModalHeader>
            <ModalBody>
                <Row className='g-3'>
                    <Col md={6}>
                        <FormController
                            controlType='textInput'
                            name='returnDataType'
                            label='Kiểu dữ liệu trả về'
                            placeholder='Nhập một trường'
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='numberInput'
                            name='numberOfDecimalPlaces'
                            label='Số chữ số thập phân'
                        />
                    </Col>
                    <Col md={12}>
                        <FormController
                            controlType='textarea'
                            name='calculationFormula'
                            placeholder='Nhập một trường'
                            defaultValue='#NET Price + #IMPORT Tax + #Mark up'
                            rows={6}
                            label='Thiết lập công thức tính'
                        />
                        <div className='mt-6'>
                            <p className='mb-0'>
                                Gõ # để chọn trường thông tin.
                            </p>
                            <p className='mb-0'>
                                Các phép toán có thể thực hiện: Cộng (+), Trừ
                                (-), Nhân (*), Chia (/), Đóng mở ngoặc ().
                            </p>
                        </div>
                    </Col>
                </Row>
            </ModalBody>
            <ModalFooter>
                <Button color='danger' outline onClick={onToggle}>
                    Hủy
                </Button>
                <Button color='success'>Lưu</Button>
            </ModalFooter>
        </Modal>
    );
};

export default ModalSetUpCalculationFormula;
