import { useSearchQuoteByDealId } from '@/apis/quotes/quotes.api';
import { Option } from '@/types/app.type';
import { useMemo } from 'react';

const useGetOptionQuoteByDealId = (id: string) => {
    const { data } = useSearchQuoteByDealId(id, { enabled: !!id });
    const deals = useMemo<Option[]>(() => {
        if (Array.isArray(data) && data.length > 0) {
            return data.map((parent) => ({
                label: parent.quoteName,
                value: parent.id,
            }));
        }
        return [];
    }, [data]);
    return deals;
};
export default useGetOptionQuoteByDealId;
