import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { Row, Col, Button } from 'reactstrap';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { useEffect } from 'react';
import {
    paymentDocumentType,
    paymentType,
} from '@/constants/sharedData/sharedData';

const Payment = () => {
    const { control } = useFormContext();
    const { fields, append, remove } = useFieldArray({
        control,
        name: 'payments',
    });

    useEffect(() => {
        if (fields.length === 0) {
            append({
                paymentType: '',
                valuePercent: '',
                daysAfterSign: '',
                paymentDocumentType: '',
                paymentDocumentCount: '',
            });
        }
    }, [fields, append]);

    return (
        <CollapseApp title='THANH TOÁN'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                {fields.map((field, idx) => (
                    <div
                        key={field.id}
                        style={{ marginBottom: '20px', position: 'relative' }}
                    >
                        <h5 style={{ color: '#0ab39c' }}>{`Đợt ${idx + 1}`}</h5>
                        <div
                            style={{
                                position: 'absolute',
                                top: '50%',
                                right: -50,
                                transform: 'translateY(-50%)',
                                zIndex: 2,
                                display: 'flex',
                                alignItems: 'center',
                            }}
                        >
                            {idx === fields.length - 1 ? (
                                <Button
                                    style={{
                                        borderRadius: '50%',
                                        width: '35px',
                                        height: '35px',
                                        borderColor: '#0ab39c',
                                        backgroundColor: '#ffffff',
                                        color: '#0ab39c',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                    onClick={() =>
                                        append({
                                            paymentType: '',
                                            valuePercent: '',
                                            daysAfterSign: '',
                                            paymentDocumentType: '',
                                            paymentDocumentCount: '',
                                        })
                                    }
                                >
                                    <i className='ri-add-line'></i>
                                </Button>
                            ) : (
                                <Button
                                    color='link'
                                    style={{
                                        borderRadius: '50%',
                                        width: '35px',
                                        height: '35px',
                                        borderColor: '#ff4d4f',
                                        backgroundColor: '#ffffff',
                                        color: '#ff4d4f',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                    onClick={() => remove(idx)}
                                >
                                    <i className='ri-delete-bin-line'></i>
                                </Button>
                            )}
                        </div>
                        <div
                            style={{
                                border: '1px dashed #0ab39c',
                                padding: '10px',
                            }}
                        >
                            <Row className='align-items-end mb-3'>
                                <Col
                                    md={3}
                                    style={{
                                        marginTop: '10px',
                                        marginBottom: '10px',
                                    }}
                                >
                                    <FormController
                                        controlType='select'
                                        name={`payments.${idx}.paymentType`}
                                        label='Hình thức thanh toán'
                                        data={paymentType}
                                        placeholder='Chọn hình thức'
                                        required={true}
                                    />
                                </Col>
                                <Col
                                    md={3}
                                    style={{
                                        marginTop: '10px',
                                        marginBottom: '10px',
                                    }}
                                >
                                    <FormController
                                        controlType='textInput'
                                        name={`payments.${idx}.valuePercent`}
                                        label='Khoản giá trị'
                                        placeholder='Nhập %'
                                        required={true}
                                    />
                                </Col>
                                <Col
                                    md={6}
                                    style={{
                                        marginTop: '10px',
                                        marginBottom: '10px',
                                    }}
                                >
                                    <FormController
                                        controlType='textInput'
                                        name={`payments.${idx}.daysAfterSign`}
                                        label='Số ngày phải trả'
                                        placeholder='Nhập số ngày'
                                        required={true}
                                    />
                                </Col>
                                <Col
                                    md={6}
                                    style={{
                                        marginTop: '10px',
                                        marginBottom: '10px',
                                    }}
                                >
                                    <FormController
                                        controlType='select'
                                        name={`payments.${idx}.paymentDocumentType`}
                                        label='Hồ sơ đề nghị'
                                        data={paymentDocumentType}
                                        placeholder='Chọn loại hồ sơ'
                                        required={true}
                                    />
                                </Col>
                                <Col
                                    md={6}
                                    style={{
                                        marginTop: '10px',
                                        marginBottom: '10px',
                                    }}
                                >
                                    <FormController
                                        controlType='numberInput'
                                        name={`payments.${idx}.paymentDocumentCount`}
                                        label='Số lượng'
                                        placeholder='Nhập số lượng hồ sơ'
                                        required={true}
                                    />
                                </Col>
                            </Row>
                        </div>
                    </div>
                ))}
            </div>
        </CollapseApp>
    );
};
export default Payment;
