import { IDealResponse } from '@/apis/opportunity/opportunity.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import { Owner } from '@/types/user.type';
import { Avatar } from '@mantine/core';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

const useGetColumns = () => {
    const actions: DropdownAction<IDealResponse>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: () => console.error('View'),
            },
            {
                icon: 'ri-pencil-line',
                label: 'Sửa cơ hội',
                onClick: (data) => console.error('Edit', data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'X<PERSON>a cơ hội',
                onClick: (data) => console.error('Delete', data),
                className: 'text-danger',
            },
        ],
        [],
    );

    const columns = useMemo<MRT_ColumnDef<IDealResponse>[]>(
        () => [
            {
                accessorKey: 'title',
                header: 'Tên cơ hội',
                enableSorting: false,
                size: 250,
            },
            {
                accessorKey: 'company',
                header: 'Tên khách hàng',
                enableSorting: false,
                Cell: ({ cell }) =>
                    cell.getValue() ? (
                        <div className='d-flex align-items-center gap-2'>
                            <Avatar radius='xl' size='sm'>
                                {String(
                                    (cell.getValue() as Owner)?.name?.charAt(0),
                                )}
                            </Avatar>
                            <p className='mb-0'>
                                {String((cell.getValue() as Owner)?.name)}
                            </p>
                        </div>
                    ) : (
                        ''
                    ),
            },
            {
                accessorKey: 'pipelineStageName',
                header: 'Giai đoạn',
                Cell: ({ cell }) => (
                    <span
                        className='badge me-1'
                        style={{
                            backgroundColor: '#daf4f0',
                            color: '#2fbeab',
                            display: 'inline-block',
                            textAlign: 'center',
                            padding: '4px 8px',
                            fontSize: '12px',
                            fontWeight: 500,
                            borderRadius: '4px',
                        }}
                    >
                        {String(cell.getValue())}
                    </span>
                ),
                enableSorting: true,
            },
            {
                accessorKey: 'amount',
                header: 'Doanh thu dự kiến',
                enableSorting: false,
            },
            {
                accessorKey: 'owner',
                header: 'Nhân viên kinh doanh',
                enableSorting: false,
                Cell: ({ cell }) =>
                    cell.getValue() ? (
                        <div className='d-flex align-items-center gap-2'>
                            <Avatar radius='xl' size='sm'>
                                {String(
                                    (cell.getValue() as Owner)?.name?.charAt(0),
                                )}
                            </Avatar>
                            <p className='mb-0'>
                                {String((cell.getValue() as Owner)?.name)}
                            </p>
                        </div>
                    ) : (
                        ''
                    ),
            },

            {
                accessorKey: 'priority',
                header: 'Mức độ',
                size: 130,
                Cell: ({ cell }) => (
                    <span
                        className='badge me-1'
                        style={{
                            backgroundColor: '#daf4f0',
                            color: '#2fbeab',
                            display: 'inline-block',
                            textAlign: 'center',
                            padding: '4px 8px',
                            fontSize: '12px',
                            fontWeight: 500,
                            borderRadius: '4px',
                        }}
                    >
                        {String(cell.getValue())}
                    </span>
                ),
                enableSorting: true,
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<IDealResponse>),
                Cell: ({ row }) => (
                    <DropdownActionMenu
                        actions={actions}
                        data={row.original}
                        direction='down'
                    />
                ),
            },
        ],
        [actions],
    );

    return { columns };
};

export default useGetColumns;
