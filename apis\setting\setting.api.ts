import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { useQuery } from '@tanstack/react-query';
import {
    CheckAssociatedInfo,
    GetDistrictsParams,
    GetWardsParams,
    IBussinesType,
    IDepartment,
    IDistricts,
    IIndustries,
    IPaymentMethod,
    IPaymentTerms,
    IPosition,
    IProvince,
    IWard,
    UserCreated,
} from './setting.type';

const URI = '/api/v1.0/Setting';

export const settingKey = {
    GET_PROVINCES: 'GET_PROVINCES',
    GET_DISTRICTS: 'GET_DISTRICTS',
    GET_WARDS: 'GET_WARDS',
    CHECK_ASSOCIATED_INFO: 'CHECK_ASSOCIATED_INFO',
    GET_POSITIONS: 'GET_POSITIONS',
    GET_INDUSTRIES: 'GET_INDUSTRIES',
    GET_PAYMENT_METHODS: 'GET_PAYMENT_METHODS',
    GET_PAYMENT_TERMS: 'GET_PAYMENT_TERMS',
    SEARCH_DEPARTMENTS: 'SEARCH_DEPARTMENTS',
    GET_BUSINES_TYPE: 'GET_BUSINES_TYPE',
    GET_USER_CREATE: 'GET_USER_CREATE',
};

// /api/v1.0/Setting/districts

export const settingUri = {
    getProvinces: `${URI}/provinces`,
    getDistricts: `${URI}/districts`,
    getWards: `${URI}/wards`,
    checkAssociatedInfo: `${URI}/check-associated-info`,
    getPositions: `${URI}/positions`,
    getPaymentMethods: `${URI}/payment-methods`,
    getPaymentTerms: `${URI}/payment-terms`,
    getDepartments: `${URI}/search-departments`,
    getBussinesType: `${URI}/get-bussines-type`,
    getIndustries: `${URI}/get-bussines-type/:id`,
    getUserCreate: `${URI}/userCreated`,
};

export const settingApis = {
    getProvinces() {
        return http.get<IProvince[]>(settingUri.getProvinces);
    },
    getDistricts(params: GetDistrictsParams) {
        return http.get<IDistricts[]>(settingUri.getDistricts, { params });
    },
    getWards(params: GetWardsParams) {
        return http.get<IWard[]>(settingUri.getWards, { params });
    },
    checkAssociatedInfo(params: CheckAssociatedInfo) {
        return http.get<boolean>(settingUri.checkAssociatedInfo, { params });
    },
    getPositions() {
        return http.get<ApiResponse<IPosition[]>>(settingUri.getPositions);
    },
    // getIndustries() {
    //     return http.get<ApiResponse<IIndustry[]>>(settingUri.getIndustries);
    // },
    getPaymentMethods() {
        return http.get<ApiResponse<IPaymentMethod[]>>(
            settingUri.getPaymentMethods,
        );
    },
    getPaymentTerms() {
        return http.get<ApiResponse<IPaymentTerms[]>>(
            settingUri.getPaymentTerms,
        );
    },
    getDepartments() {
        return http.get<ApiResponseList<IDepartment[]>>(
            settingUri.getDepartments,
        );
    },
    getBussinesType() {
        return http.get<ApiResponse<IBussinesType[]>>(
            settingUri.getBussinesType,
        );
    },
    getIndustries(id: string) {
        return http.get<ApiResponse<IIndustries>>(
            settingUri.getIndustries.replace(':id', id),
        );
    },
    getUserCreate() {
        return http.get<UserCreated[]>(settingUri.getUserCreate);
    },
};
export const useGetProvinces = () => {
    return useQuery({
        queryKey: [settingKey.GET_PROVINCES],
        queryFn: () => settingApis.getProvinces(),
        select: (data) => data,
    });
};

export const useGetDistricts = (params: GetDistrictsParams) => {
    const provinceId = params.provinceId;
    const provinceName = params.provinceName;

    return useQuery({
        queryKey: [settingKey.GET_DISTRICTS, params],
        queryFn: () => settingApis.getDistricts(params),
        select: (data) => data,
        enabled: !!provinceId || !!provinceName,
    });
};
export const useGetSearchDepartments = () => {
    return useQuery({
        queryKey: [settingKey.SEARCH_DEPARTMENTS],
        queryFn: () => settingApis.getDepartments(),
        select: (data) => data.items,
    });
};

export const useGetWards = (params: GetWardsParams) => {
    const districtId = params.districtId;
    const districtName = params.districtName;

    return useQuery({
        queryKey: [settingKey.GET_WARDS, params],
        queryFn: () => settingApis.getWards(params),
        select: (data) => data,
        enabled: !!districtId || !!districtName,
    });
};

export const useCheckAssociatedInfo = (params: CheckAssociatedInfo) => {
    return useQuery({
        queryKey: [settingKey.CHECK_ASSOCIATED_INFO, params],
        queryFn: () => settingApis.checkAssociatedInfo(params),
        select: (data) => data,
    });
};

export const useGetPositions = () => {
    return useQuery({
        queryKey: [settingKey.GET_POSITIONS],
        queryFn: () => settingApis.getPositions(),
        select: (data) => data.data,
    });
};
export const useGetPaymentMethods = () => {
    return useQuery({
        queryKey: [settingKey.GET_PAYMENT_METHODS],
        queryFn: () => settingApis.getPaymentMethods(),
        select: (data) => data.data,
    });
};

export const useGetPaymentTerms = () => {
    return useQuery({
        queryKey: [settingKey.GET_PAYMENT_TERMS],
        queryFn: () => settingApis.getPaymentTerms(),
        select: (data) => data.data,
    });
};

export const useGetBussinesType = () => {
    return useQuery({
        queryKey: [settingKey.GET_BUSINES_TYPE],
        queryFn: () => settingApis.getBussinesType(),
        select: (data) => data.data,
    });
};

export const useGetIndustries = (id: string) => {
    return useQuery({
        queryKey: [settingKey.GET_INDUSTRIES, id],
        queryFn: () => settingApis.getIndustries(id),
        select: (data) => data.data,
    });
};

export const useCreate = () => {
    return useQuery({
        queryKey: [settingKey.GET_USER_CREATE],
        queryFn: () => settingApis.getUserCreate(),
        select: (data) => data,
    });
};
