'use client';

import { ICustomer, Addresses } from '@/apis/customer/customer.type';
import CollapseApp from '@/components/common/CollapseApp';
import useGetOptionsAddress from '@/hooks/useGetOptionsAddress';
import { useFormContext, useWatch } from 'react-hook-form';
import {
    Col,
    Row,
    Nav,
    NavItem,
    NavLink,
    TabContent,
    TabPane,
    Button,
} from 'reactstrap';
import { useState } from 'react';
import AddressModal from '../../Modal/AddressModal';

import BankAccountsTab from './BankAccountsTab';

interface AdditionalInfoProps {
    initValue?: ICustomer;
}

const AdditionalInfo = ({ initValue }: AdditionalInfoProps) => {
    const { control, setValue } = useFormContext<ICustomer>();

    const [activeTab, setActiveTab] = useState('1');
    const [modal, setModal] = useState(false);
    const [addressType, setAddressType] = useState('1');

    const [tempAddress, setTempAddress] = useState({
        addressName: '',
        provinceId: '',
        districtId: '',
        wardId: '',
        country: 'Việt Nam',
    });

    const formAddresses =
        useWatch({
            control,
            name: 'addresses',
        }) ||
        initValue?.addresses ||
        [];

    const { provinces, districts, wards } = useGetOptionsAddress({
        provinceId: tempAddress.provinceId,
        districtId: tempAddress.districtId,
    });

    const toggleTab = (tab: string) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };

    const toggleModal = () => {
        setModal(!modal);
        if (!modal) {
            setTempAddress({
                addressName: '',
                provinceId: '',
                districtId: '',
                wardId: '',
                country: 'Việt Nam',
            });
            setAddressType('1');
        }
    };

    const handleAddAddress = () => {
        if (
            tempAddress.addressName &&
            tempAddress.provinceId &&
            tempAddress.districtId &&
            tempAddress.wardId
        ) {
            const province =
                provinces.find((p) => p.value === tempAddress.provinceId)
                    ?.label || '';
            const district =
                districts.find((d) => d.value === tempAddress.districtId)
                    ?.label || '';
            const ward =
                wards.find((w) => w.value === tempAddress.wardId)?.label || '';

            const newAddressItem: Addresses = {
                id: Date.now().toString(),
                addressName: tempAddress.addressName,
                provinceId: tempAddress.provinceId,
                districtId: tempAddress.districtId,
                wardId: tempAddress.wardId,
                country: tempAddress.country || 'Việt Nam',
                addressType: Number(addressType),
                provinceName: province,
                districtName: district,
                wardName: ward,
                addressTypeName: getAddressTypeLabel(Number(addressType)),
                companyName: '',
            };

            const currentValidAddresses = (formAddresses || []).filter(
                (addr: Addresses) =>
                    addr.id &&
                    addr.addressName &&
                    addr.provinceId &&
                    addr.districtId &&
                    addr.wardId,
            );
            const updatedAddresses = [...currentValidAddresses, newAddressItem];

            setValue('addresses', updatedAddresses);

            toggleModal();
        }
    };

    const getAddressTypeLabel = (type: number) => {
        switch (type) {
            case 1:
                return 'Địa chỉ liên hệ';
            case 2:
                return 'Địa chỉ hóa đơn';
            case 3:
                return 'Địa chỉ giao hàng';
            default:
                return '';
        }
    };

    const handleDeleteAddress = (addressId: string) => {
        const updatedAddresses = formAddresses.filter(
            (address: Addresses) => address.id !== addressId,
        );
        setValue('addresses', updatedAddresses);
    };

    return (
        <CollapseApp title='THÔNG TIN BỔ SUNG'>
            <Row
                style={{
                    minHeight: '300px',
                    height: 'auto',
                    padding: '20px 40px 20px 40px',
                }}
            >
                <Col md='12'>
                    <Nav tabs>
                        <NavItem>
                            <NavLink
                                className={activeTab === '1' ? 'active' : ''}
                                onClick={() => toggleTab('1')}
                                style={{ cursor: 'pointer' }}
                            >
                                Địa chỉ
                            </NavLink>
                        </NavItem>
                        <NavItem>
                            <NavLink
                                className={activeTab === '2' ? 'active' : ''}
                                onClick={() => toggleTab('2')}
                                style={{ cursor: 'pointer' }}
                            >
                                Tài khoản thanh toán
                            </NavLink>
                        </NavItem>
                    </Nav>
                    <TabContent activeTab={activeTab}>
                        <TabPane tabId='1'>
                            <div className='d-flex justify-content-end mt-3'>
                                <Button
                                    className='btn-sm'
                                    onClick={toggleModal}
                                    color='success'
                                    style={{ borderColor: 'none' }}
                                >
                                    <i className='ri-add-line align-bottom me-1'></i>{' '}
                                    Thêm
                                </Button>
                            </div>
                            <Col className='mt-3' lg={12}>
                                <Row>
                                    {formAddresses
                                        .filter(
                                            (address: Addresses) =>
                                                address.id &&
                                                address.addressName &&
                                                address.provinceId &&
                                                address.districtId &&
                                                address.wardId,
                                        )
                                        .map((address: Addresses) => {
                                            const fullAddress =
                                                `${address.addressName}, ${address.wardName}, ${address.districtName}, ${address.provinceName}`.replace(
                                                    /^[,\s]+|[,\s]+$/g,
                                                    '',
                                                );
                                            return (
                                                <Col key={address.id} lg={4}>
                                                    <div
                                                        className='border rounded p-3 mb-3 position-relative'
                                                        style={{
                                                            backgroundColor:
                                                                '#f8f9fa',
                                                            paddingRight:
                                                                '30px',
                                                        }}
                                                    >
                                                        <button
                                                            type='button'
                                                            className='btn-close position-absolute'
                                                            style={{
                                                                top: '8px',
                                                                right: '8px',
                                                                fontSize:
                                                                    '12px',
                                                                background:
                                                                    'none',
                                                                border: 'none',
                                                                color: '#dc3545',
                                                                cursor: 'pointer',
                                                            }}
                                                            onClick={() =>
                                                                handleDeleteAddress(
                                                                    address.id,
                                                                )
                                                            }
                                                            title='Xóa địa chỉ'
                                                        >
                                                            <i className='ri-close-line'></i>
                                                        </button>
                                                        <div className='d-flex align-items-start'>
                                                            <i className='ri-map-pin-2-line fs-4 me-2 text-primary'></i>
                                                            <div className='flex-grow-1'>
                                                                <h6 className='mb-1'>
                                                                    {address.addressTypeName ||
                                                                        getAddressTypeLabel(
                                                                            address.addressType,
                                                                        )}
                                                                </h6>
                                                                <p className='mb-0'>
                                                                    {
                                                                        fullAddress
                                                                    }
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Col>
                                            );
                                        })}
                                </Row>
                            </Col>
                        </TabPane>
                        <TabPane tabId='2'>
                            <div style={{ marginBottom: '20px' }}>
                                <BankAccountsTab />
                            </div>
                        </TabPane>
                    </TabContent>
                </Col>
            </Row>

            <AddressModal
                isOpen={modal}
                toggle={toggleModal}
                setAddressType={setAddressType}
                handleAddAddress={handleAddAddress}
                provinces={provinces}
                districts={districts}
                wards={wards}
                tempAddress={tempAddress}
                setTempAddress={setTempAddress}
                addressType={addressType}
            />
        </CollapseApp>
    );
};

export default AdditionalInfo;
