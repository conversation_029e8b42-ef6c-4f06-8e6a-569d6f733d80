'use client';
import {
    Button,
    Col,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Input,
    Row,
} from 'reactstrap';

interface SelectHeaderProps {
    selectedYears: string[];
    setSelectedYears: (years: string[]) => void;
    yearDropdownOpen: boolean;
    toggleYearDropdown: () => void;
    time: string;
    setTime: (time: string) => void;
    objective: string;
    setObjective: (objective: string) => void;
}

const SelectHeader = ({
    selectedYears,
    setSelectedYears,
    yearDropdownOpen,
    toggleYearDropdown,
    time,
    setTime,
    objective,
    setObjective,
}: SelectHeaderProps) => {
    const availableYears = ['2021', '2022', '2023', '2024', '2025'];

    const handleYearToggle = (year: string) => {
        if (selectedYears.includes(year)) {
            setSelectedYears(selectedYears.filter((y) => y !== year));
        } else {
            setSelectedYears([...selectedYears, year]);
        }
    };

    const renderYearBadge = (year: string) => (
        <Button
            key={year}
            pill
            style={{
                backgroundColor: '#e9ebec ',
                color: 'black',
                flexShrink: 0,
                border: '1px solid #ced4da',
                height: '25px',
            }}
            className='d-flex align-items-center'
        >
            {year}
            <span
                className='ms-1'
                onClick={(e) => {
                    e.stopPropagation();
                    handleYearToggle(year);
                }}
            >
                ×
            </span>
        </Button>
    );

    return (
        <Row className='align-items-center'>
            <Col md={2}>
                <div className='mb-3 mb-md-0'>
                    <Dropdown
                        isOpen={yearDropdownOpen}
                        toggle={toggleYearDropdown}
                    >
                        <DropdownToggle
                            className='form-select d-flex justify-content-between align-items-center'
                            style={{
                                backgroundColor: 'white',
                                borderColor: '#ced4da',
                                paddingRight: '30px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                height: '37px',
                            }}
                            placeholder='Chọn năm'
                        >
                            <div
                                className='d-flex flex-nowrap gap-1'
                                style={{
                                    overflow: 'auto',
                                    maxWidth: '98%',
                                    scrollbarWidth: 'none',
                                    msOverflowStyle: 'none',
                                    cursor: 'grab',
                                }}
                                onScroll={(e) => e.stopPropagation()}
                                onMouseDown={(e) => {
                                    const ele = e.currentTarget;
                                    const startX = e.pageX - ele.scrollLeft;
                                    const handleMouseMove = (e: MouseEvent) => {
                                        ele.scrollLeft = e.pageX - startX;
                                    };
                                    const handleMouseUp = () => {
                                        document.removeEventListener(
                                            'mousemove',
                                            handleMouseMove,
                                        );
                                        document.removeEventListener(
                                            'mouseup',
                                            handleMouseUp,
                                        );
                                    };
                                    document.addEventListener(
                                        'mousemove',
                                        handleMouseMove,
                                    );
                                    document.addEventListener(
                                        'mouseup',
                                        handleMouseUp,
                                    );
                                }}
                            >
                                {selectedYears.length > 0 ? (
                                    selectedYears.map((year) =>
                                        renderYearBadge(year),
                                    )
                                ) : (
                                    <span className='text-muted'>Chọn năm</span>
                                )}
                                <style jsx>{`
                                    div::-webkit-scrollbar {
                                        display: none;
                                    }
                                `}</style>
                            </div>
                        </DropdownToggle>
                        <DropdownMenu
                            className='dropdown-menu-end'
                            positionFixed={true}
                        >
                            {availableYears.map((year) => (
                                <DropdownItem key={year} toggle={false}>
                                    <div className='form-check'>
                                        <Input
                                            type='checkbox'
                                            id={`year-${year}`}
                                            placeholder='Chọn năm'
                                            checked={selectedYears.includes(
                                                year,
                                            )}
                                            onChange={() =>
                                                handleYearToggle(year)
                                            }
                                            style={{
                                                backgroundColor:
                                                    selectedYears.includes(year)
                                                        ? '#0ab39c'
                                                        : 'white',
                                                borderColor:
                                                    selectedYears.includes(year)
                                                        ? '#0ab39c'
                                                        : '#ced4da',
                                            }}
                                            className='form-check-success'
                                            disabled={
                                                !selectedYears.includes(year) &&
                                                selectedYears.length >= 3
                                            }
                                        />
                                        <label
                                            className='form-check-label'
                                            htmlFor={`year-${year}`}
                                            style={{
                                                color:
                                                    !selectedYears.includes(
                                                        year,
                                                    ) &&
                                                    selectedYears.length >= 3
                                                        ? '#adb5bd'
                                                        : 'black',
                                            }}
                                        >
                                            {year}
                                        </label>
                                    </div>
                                </DropdownItem>
                            ))}
                            {selectedYears.length >= 3 && (
                                <div className='px-3 py-2 text-muted small'>
                                    <i className='ri-information-line me-1'></i>
                                    Chỉ được chọn tối đa 3 năm
                                </div>
                            )}
                        </DropdownMenu>
                    </Dropdown>
                </div>
            </Col>
            <Col md={2}>
                <div className='mb-3 mb-md-0'>
                    <Input
                        type='select'
                        className='form-select'
                        value={objective}
                        onChange={(e) => setObjective(e.target.value)}
                    >
                        <option>Theo cá nhân</option>
                        <option>Phòng sale</option>
                    </Input>
                </div>
            </Col>
            <Col md={2}>
                <div className='mb-3 mb-md-0'>
                    <Input
                        type='select'
                        className='form-select'
                        value={time}
                        onChange={(e) => setTime(e.target.value)}
                    >
                        <option>Năm</option>
                        <option>6 tháng</option>
                        <option>Quý</option>
                        <option>Tháng</option>
                    </Input>
                </div>
            </Col>
        </Row>
    );
};

export default SelectHeader;
