import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import SelectPositionControl from '@/components/common/FormController/SelectPositionControl';
import { useFormContext } from 'react-hook-form';
import { Col, Row } from 'reactstrap';

const Seller = () => {
    useFormContext();
    return (
        <CollapseApp title='BÊN BÁN'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankName'
                            label='Ngân hàng'
                            placeholder='Nhập ngân hàng'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankBranch'
                            label='Chi nhánh'
                            placeholder='Nhập chi nhánh'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankAccount'
                            label='Số tài khoản'
                            placeholder='Nhập số tài khoản'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <ComboboxSelectUserControl
                            name='seller.staffId'
                            label='Người đại diện'
                            placeholder='Chọn người đại diện'
                            style={{
                                width: '100%',
                            }}
                            required={true}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <SelectPositionControl
                            name='seller.position'
                            label='Chức vụ'
                            placeholder='Chọn chức vụ'
                            required={true}
                            style={{
                                width: '100%',
                            }}
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default Seller;
