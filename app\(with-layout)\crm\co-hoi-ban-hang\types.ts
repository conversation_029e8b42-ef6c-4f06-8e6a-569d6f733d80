export interface Opportunity {
    id: string;
    title: string;
    revenue: number;
    dueDate: string;
    priority: 'low' | 'medium' | 'high';
    stage: 'new' | 'evaluated' | 'negotiating' | 'success' | 'failed';
    assignees: {
        avatar: string;
        name: string;
    }[];
}

export interface Filter {
    id: string;
    name: string;
    value: string;
}

export interface KanbanColumn {
    id: string;
    title: string;
    count: number;
    opportunities: Opportunity[];
    color: string;
}

export interface FilterCondition {
    field: string;
    operator: string;
    value: string;
}

export interface AdvancedFilter {
    id: string;
    name: string;
    conditions: FilterCondition[];
}

export enum TYPE_DISPLAY {
    KANBAN = 'KANBAN',
    TABLE = 'TABLE',
}

export interface SalesOpportunity extends Record<string, string> {
    name: string;
    email: string;
    phone: string;
    business: string;
    stage: string;
    status: string;
}

export enum TabOpportunity {
    ALL = 'ALL',
    MY_OPPORTUNITY = 'MY_OPPORTUNITY',
}

export enum DealType {
    Classic = 1, // <PERSON><PERSON> hội bán hàng mới
    Cyber = 2, // <PERSON><PERSON> hội bán hàng hiện tại
}

export enum ClassificationNeedType {
    LearnProduct = 0, // Tìm hiểu sản phẩm
    ProductSpecific = 1, // Tư vấn cụ thể sản phẩm
    CompareProducts = 2, // So sánh sản phẩm
    CustomerCare = 3, //Chăm sóc khách hàng
}

export enum RequestDemo {
    Yes = 0, // Có
    No = 1, // Không
    None = 2, // Không có thông tin
}

export enum InstallationType {
    Cloud = 1, // Cài đặt trên đám mây
    OnPremises = 2, // Cài đặt tại chỗ (On-Premises)
}

export enum Priority {
    //  [Description("Thấp")]
    Low,
    // [Description("Trung bình")]
    Medium,
    // [Description("Cao")]
    High,
    // [Description("Rất cao")]
    Critical,
}
