'use client';
import {
    useRestoreContacts,
    useSearchRestoreContacts,
} from '@/apis/contact/contact.api';
import { IContactResponse, SearchContact } from '@/apis/contact/contact.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { ROUTES } from '@/lib/routes';
import { getOneMonthAgo, getToday } from '@/utils/time';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from '../_hook/useGetColumn';
import ModalRestore from '@/components/common/Modal/ModalRestore';
import ImportFileModal from '@/components/common/ImportFile';
const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then(
            (mod) => mod.default,
        ),
    {
        ssr: false,
    },
) as typeof import('@/components/common/MantineReactTable').default;
const RestoreContacts = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const methods = useForm<SearchContact>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        Name,
        DepartmentId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'DepartmentId',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { mutate: restoreContacts } = useRestoreContacts({
        onSuccess: () => {
            toast.success('Khôi phục khách hàng thành công');
            setModal(false);
            setSelectedNames([]);
            setSelectedIds([]);
            refetch();
        },
        onError: () => {
            toast.error('Khôi phục khách hàng thất bại');
        },
    });

    const handleRestoreContact = (contact: IContactResponse | undefined) => {
        if (contact) {
            restoreContacts({ ids: [contact.id] });
        }
    };

    const handleRestoreSelected = () => {
        if (selectedIds.length > 0) {
            restoreContacts({ ids: selectedIds });
        } else {
            toast.warning('Vui lòng chọn ít nhất một khách hàng để khôi phục');
        }
    };

    const columns = useGetColumn({
        page: 'restore',
        onRestore: handleRestoreContact,
    });

    const { data, refetch, isLoading } = useSearchRestoreContacts({
        Name,
        DepartmentId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    const onCreateNew = () => {
        router.push(ROUTES.CRM.PERSONAL.CREATE);
    };

    const { items: listRestoreContact = [], totalItems } = data ?? {};

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={onCreateNew}
                        onImportExcel={() => setIsImportModalOpen(true)}
                    />
                </Col>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên cá nhân...'
                                    />
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        <Button
                                            color='success'
                                            onClick={handleRestoreSelected}
                                            disabled={selectedIds.length === 0}
                                        >
                                            {selectedIds.length > 0
                                                ? `Khôi phục (${selectedIds.length})`
                                                : 'Khôi phục'}
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES.CRM.PERSONAL
                                                                .INDEX,
                                                        )
                                                    }
                                                >
                                                    Cá nhân
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listRestoreContact}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listRestoreContact.findIndex(
                                                    (
                                                        contact: IContactResponse,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listRestoreContact.findIndex(
                                                            (
                                                                contact: IContactResponse,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listRestoreContact[
                                                    parseInt(key)
                                                ];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalRestore
                onRestore={handleRestoreSelected}
                onClose={handleClose}
                isOpen={modal}
                page='cá nhân'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};

export default RestoreContacts;
