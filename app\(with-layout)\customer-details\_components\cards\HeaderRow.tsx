import React from 'react';
import { Button } from 'reactstrap';

export interface HeaderRowProps {
    title: string;
}

const HeaderRow: React.FC<HeaderRowProps> = ({ title }) => (
    <div className='d-flex w-100 justify-content-between align-items-center mb-2'>
        <h6 className='mb-0'>{title}</h6>
        <Button color='light' className='btn-icon'>
            <i className='bx bx-dots-vertical-rounded'></i>
        </Button>
    </div>
);

export default HeaderRow;
