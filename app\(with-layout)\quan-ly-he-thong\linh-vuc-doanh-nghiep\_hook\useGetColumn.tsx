import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

export interface IBusinessType {
    name: string;
    description: string;
    createdDate: string;
}

interface GetColumnProps {
    onSelectedAction: (
        action: ACTIONS,
        data: IBusinessType | undefined,
    ) => void;
}

const useGetColumn = ({ onSelectedAction }: GetColumnProps) => {
    const actions: DropdownAction<IBusinessType>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) => onSelectedAction(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<IBusinessType>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên loại hình',
                size: 150,
            },
            {
                accessorKey: 'description',
                header: 'Mô tả',
                size: 350,
                Cell: ({ cell }) => <div>{cell.getValue<string>()}</div>,
            },
            {
                accessorKey: 'createdDate',
                header: 'Ngày tạo',
                size: 100,
            },
            {
                header: 'Hành động',
                enableResizing: false,
                size: 70,
                Header: () => (
                    <div style={{ textAlign: 'center' }}>Hành động</div>
                ),
                Cell: ({ row }) => (
                    <div
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                        style={{ textAlign: 'center' }}
                    >
                        <DropdownActionMenu
                            actions={actions}
                            data={row.original}
                            direction='down'
                            end={false}
                        />
                    </div>
                ),
            },
        ],
        [actions],
    );

    return columns;
};

export default useGetColumn;
