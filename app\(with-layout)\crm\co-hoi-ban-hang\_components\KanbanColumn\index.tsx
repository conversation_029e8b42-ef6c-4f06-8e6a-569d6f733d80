import { useSearchDeals } from '@/apis/opportunity/opportunity.api';
import {
    IDealResponse,
    IPipelineStage,
} from '@/apis/opportunity/opportunity.type';
import { Droppable } from '@hello-pangea/dnd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Button } from 'reactstrap';
import { useKanban } from '../../context/KanbanContext';
import { OpportunityCard } from './OpportunityCard';

interface KanbanColumnProps {
    pipelineStage: IPipelineStage;
    opportunities?: IDealResponse[];
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({
    pipelineStage,
    opportunities: propOpportunities,
}) => {
    const { data } = useSearchDeals(
        {
            PipelineStageId: pipelineStage.id,
        },
        {
            enabled: !!pipelineStage.id && !propOpportunities,
        },
    );

    const { columns } = useKanban();
    const [isHovering, setIsHovering] = useState(false);
    const columnRef = useRef<HTMLDivElement>(null);

    // Thêm CSS cho việc ẩn/hiện thanh scroll dọc trong cột
    useEffect(() => {
        // Thêm CSS vào head của document
        const styleElement = document.createElement('style');
        styleElement.innerHTML = `
            .kanban-items-container {
                overflow-y: auto;
                scrollbar-width: thin;
                scrollbar-color: transparent transparent;
                transition: scrollbar-color 0.3s ease;
            }
            
            .kanban-items-container::-webkit-scrollbar {
                width: 6px;
                background-color: transparent;
            }
            
            .kanban-items-container::-webkit-scrollbar-thumb {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .kanban-items-container::-webkit-scrollbar-thumb:hover {
                background-color: rgba(0, 0, 0, 0.3);
            }
            
            .kanban-items-container.hovering::-webkit-scrollbar-thumb {
                opacity: 1;
            }
            
            .kanban-items-container:not(.hovering)::-webkit-scrollbar-thumb {
                opacity: 0;
            }
            
            .kanban-items-container.hovering {
                scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
            }
        `;
        document.head.appendChild(styleElement);

        return () => {
            document.head.removeChild(styleElement);
        };
    }, []);

    const opportunities = useMemo(() => {
        // Nếu có dữ liệu từ props (từ context), sử dụng nó
        if (propOpportunities) {
            return propOpportunities;
        }

        // Nếu có dữ liệu từ context, sử dụng nó
        if (columns && columns[pipelineStage.id]) {
            return columns[pipelineStage.id].items;
        }

        // Nếu không, sử dụng dữ liệu từ API
        if (data?.items) {
            return data.items;
        }

        return [];
    }, [data, pipelineStage.id, propOpportunities, columns]);

    const handleMouseEnter = () => {
        setIsHovering(true);
    };

    const handleMouseLeave = () => {
        setIsHovering(false);
    };

    // Tính tổng doanh thu
    const totalRevenue = useMemo(() => {
        return opportunities.reduce(
            (sum, opportunity) => sum + opportunity.amount,
            0,
        );
    }, [opportunities]);

    return (
        <div
            ref={columnRef}
            className='flex-grow-1 kanban-column'
            style={{
                minWidth: '280px',
                maxWidth: '280px',
                display: 'flex',
                flexDirection: 'column',
                height: 'calc(100vh - 285px)',
            }}
        >
            <div className='d-flex align-items-center mb-2'>
                <h6 className='text-uppercase mb-0 flex-grow-1'>
                    {pipelineStage.name}{' '}
                    <span className='badge bg-success rounded-pill ms-1'>
                        {pipelineStage.totalDeals}
                    </span>
                </h6>
                {pipelineStage.id === 'new' && (
                    <Button
                        style={{
                            backgroundColor: '#f3f3f9',
                            border: 'none',
                            padding: '4px',
                            color: '#878a99',
                        }}
                        size='sm'
                        className='btn-icon'
                    >
                        <i className='ri-add-line'></i>
                    </Button>
                )}
            </div>

            <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <Droppable droppableId={pipelineStage.id}>
                    {(provided, snapshot) => (
                        <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className={`kanban-items-container ${isHovering ? 'hovering' : ''}`}
                            style={{
                                minHeight: '100px',
                                backgroundColor: snapshot.isDraggingOver
                                    ? 'rgba(0, 0, 0, 0.02)'
                                    : 'transparent',
                                borderRadius: '4px',
                                transition: 'background-color 0.2s ease',
                                overflowX: 'hidden',
                                marginBottom: '0',
                                flex: 1,
                            }}
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                        >
                            {opportunities.map((opportunity, index) => (
                                <OpportunityCard
                                    key={opportunity.dealId}
                                    opportunity={opportunity}
                                    index={index}
                                />
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
                <div
                    className='d-flex justify-content-center align-items-center'
                    style={{
                        width: '100%',
                        height: '40px',
                        background: 'rgba(223, 227, 235, 1)',
                        flexShrink: 0,
                        position: 'sticky',
                        bottom: '0',
                    }}
                >
                    Tổng doanh thu:{' '}
                    <strong>{totalRevenue.toLocaleString('vi-VN')}</strong>
                </div>
            </div>
        </div>
    );
};
