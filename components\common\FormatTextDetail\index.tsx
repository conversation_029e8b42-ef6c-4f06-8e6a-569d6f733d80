import { Label } from 'reactstrap';

interface FormatTextDetailProps {
    label: string;
    p: string;
}
const FormatTextDetail = ({ label, p }: FormatTextDetailProps) => {
    return (
        <>
            <Label
                className='text-muted'
                style={{
                    fontSize: '13px',
                }}
            >
                {label}
            </Label>
            <p
                style={{
                    fontSize: '13px',
                }}
            >
                {p}
            </p>
        </>
    );
};

export default FormatTextDetail;
