import React from 'react';
import { Row, Card, CardBody } from 'reactstrap';
import AddressCard from './AddressCard';

const AddressTab: React.FC = () => (
    <>
        <Row>
            <AddressCard
                iconClass='las la-address-book'
                title='Địa chỉ liên hệ'
                address='195 Trần Cung - Bắc Từ Liêm - Hà Nội'
            />
            <AddressCard
                iconClass='las la-address-book'
                title='Địa chỉ giao hàng'
                address='195 Trần Cung - Bắc Từ Liêm - Hà Nội'
            />
            <AddressCard
                iconClass='las la-address-book'
                title='Địa chỉ xuất hóa đơn'
                address='195 Trần Cung - Bắc Từ Liêm - Hà Nội'
            />
        </Row>
        <Card className='border mt-3'>
            <CardBody>
                <div className='row'>
                    <div className='col-md-3'>
                        <label className='form-label'>QUỐC GIA</label>
                        <div>Việt Nam</div>
                    </div>
                    <div className='col-md-3'>
                        <label className='form-label'>TỈNH/THÀNH PHỐ</label>
                        <div>Hà Nội</div>
                    </div>
                    <div className='col-md-3'>
                        <label className='form-label'>QUẬN/HUYỆN</label>
                        <div>Quận Bắc Từ Liêm</div>
                    </div>
                    <div className='col-md-3'>
                        <label className='form-label'>XÃ/PHƯỜNG</label>
                        <div>Cổ Nhuế 1</div>
                    </div>
                    <div className='col-12'>
                        <label className='form-label'>ĐỊA CHỈ CỤ THỂ</label>
                        <div>
                            Số nhà 88, TDP Hoàng 9, ngõ 195 đường Trần Cung
                        </div>
                    </div>
                </div>
            </CardBody>
        </Card>
    </>
);

export default AddressTab;
