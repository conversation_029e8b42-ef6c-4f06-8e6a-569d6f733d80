export interface SearchSupplier {
    Name?: string;
    UserNameCreated?: string;
    FromDate?: string;
    ToDate?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
}

export interface ResponseSupplier {
    id: string;
    name: string;
    code: string;
    tax: string;
    address: string;
    phoneNumber: string;
    email: string;
    description: string;
    commonStatus: number;
    createdDateTime: string;
    userNameCreated: string;
}

export interface ISupplier {
    id: string;
    name: string;
    code: string;
    tax: string;
    address: string;
    phoneNumber: string;
    email: string;
    description: string;
    commonStatus: number;
    country?: string;
    createdDateTime: string;
    userNameCreated: string;
}

export interface StatusSupplier {
    data: string;
    isError: boolean;
    errorMessage: string;
    status: number;
}
