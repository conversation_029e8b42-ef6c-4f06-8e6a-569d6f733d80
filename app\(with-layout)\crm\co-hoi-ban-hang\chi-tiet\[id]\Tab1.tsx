import { IDealDetail } from '@/apis/opportunity/opportunity.type';
import { Col, Label, Row } from 'reactstrap';

interface Tab1Props {
    data?: IDealDetail;
}

const Tab1 = ({ data }: Tab1Props) => {
    return (
        <div>
            <h3>{data?.title}</h3>
            <div
                style={{
                    color: '#000000',
                    backgroundColor: '#9be4d0',
                    padding: '5px 7px 5px 7px',
                    width: 'fit-content',
                }}
            >
                Cyber Security
            </div>
            <Row className='mt-4'>
                <Col md={5}>
                    <Col md={12} className='mb-2'>
                        <Label>MÃ CƠ HỘI</Label>
                        <p>{data?.code}</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>DOANH THU DỰ KIẾN</Label>
                        <p>{data?.amount?.toLocaleString('vi-VN')}</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>GIAI ĐOẠN</Label>
                        <div
                            style={{
                                color: '#3ec3b1',
                                backgroundColor: '#daf4f0',
                                padding: '5px 7px 5px 7px',
                                width: 'fit-content',
                            }}
                        >
                            {data?.pipelineStageName}
                        </div>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>KHÁCH HÀNG</Label>
                        <p className='d-flex align-items-center'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle me-2'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {data?.companyName?.charAt(0)?.toUpperCase()}
                            </div>
                            {data?.companyName}
                        </p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>THỜI GIAN BẮT ĐẦU POC</Label>
                        <p>
                            {data?.startPoC
                                ? new Date(data.startPoC).toLocaleDateString(
                                      'vi-VN',
                                  )
                                : '-'}
                        </p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>NHÂN VIÊN KINH DOANH</Label>
                        <p className='d-flex align-items-center'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle me-2'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {data?.ownerName?.charAt(0)?.toUpperCase()}
                            </div>
                            {data?.ownerName}
                        </p>
                    </Col>
                </Col>
                <Col md={5}>
                    <Col md={12} className='mb-2'>
                        <Label>NGÀY TẠO</Label>
                        <p>
                            {data?.createdDateTime
                                ? new Date(
                                      data.createdDateTime,
                                  ).toLocaleDateString('vi-VN')
                                : '-'}
                        </p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>MỨC ĐỘ ƯU TIÊN</Label>
                        <p
                        // style={{
                        //     color: '#3ec3b1',
                        // }}
                        >
                            <i className='ri-record-circle-fill'></i>
                            {data?.priorityName}
                        </p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>XÁC XUẤT THẮNG</Label>
                        <p>{data?.probability}%</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>ĐỐI TÁC THƯƠNG MẠI</Label>
                        <p className='d-flex align-items-center'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle me-2'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {data?.tradePartnerName
                                    ?.charAt(0)
                                    ?.toUpperCase()}
                            </div>
                            {data?.tradePartnerName}
                        </p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>THỜI GIAN KẾT THÚC POC</Label>
                        <p>
                            {data?.endPoC
                                ? new Date(data.endPoC).toLocaleDateString(
                                      'vi-VN',
                                  )
                                : '-'}
                        </p>
                    </Col>
                </Col>
                <Col md={12} className='mb-2'>
                    <Label>MÔ TẢ</Label>
                    <p>{data?.description}</p>
                </Col>
            </Row>
        </div>
    );
};

export default Tab1;
