import { IParterCompany, IPartnerContact } from '@/apis/partners/partners.type';
import DropdownActionMenu from '@/components/common/DropdownActionMenu';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Button, Card, CardBody, CardHeader, Col } from 'reactstrap';
import QuickViewModal from './QuickViewModal';

interface Props {
    companies: IParterCompany[];
    contacts: IPartnerContact[];
}

const FileInfo = (props: Props) => {
    const { companies = [], contacts = [] } = props;

    const router = useRouter();

    const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<
        'customer' | 'partner' | null
    >(null);

    const toggleQuickView = (item: 'customer' | 'partner' | null = null) => {
        setIsQuickViewOpen(!isQuickViewOpen);
        setSelectedItem(item);
    };

    const customerData = {
        name: 'Gare<PERSON>',
        companyName: 'Công Ty CP Garena Việt Nam',
        email: '<EMAIL>',
        phone: '*********',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: 'Tiềm năng',
        status: 'Mới',
    };

    const partnerData = {
        name: 'Garena',
        companyName: 'Garena',
        email: '<EMAIL>',
        phone: '0354629272',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: 'Tiềm năng',
        status: 'Mới',
    };

    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {/* File items */}
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <Card className='mb-3'>
                <CardHeader className='d-flex align-items-center'>
                    <h5 className='mb-0 flex-grow-1'>Doanh nghiệp</h5>
                    <Button
                        color='success'
                        size='sm'
                        className='btn-sm'
                        style={{
                            backgroundColor: '#0ab39c',
                            border: 'none',
                        }}
                    >
                        <i className='ri-add-line align-middle'></i> Thêm
                    </Button>
                </CardHeader>
                <CardBody>
                    {companies.map((company) => (
                        <div key={company.id} className='border-bottom py-2'>
                            <div className='d-flex justify-content-between align-items-center mb-2'>
                                <h6
                                    className='m-0'
                                    style={{ height: 'fit-content' }}
                                >
                                    {company.name}
                                </h6>
                                <DropdownActionMenu
                                    actions={[
                                        {
                                            icon: 'ri-eye-line',
                                            label: 'Xem nhanh',
                                            onClick: () =>
                                                toggleQuickView('customer'),
                                        },
                                        {
                                            icon: 'ri-eye-fill',
                                            label: 'Xem chi tiết',
                                            onClick: () => {
                                                router.push(
                                                    ROUTES.CRM.CUSTOMERS.DETAIL.replace(
                                                        ':id',
                                                        company.id,
                                                    ),
                                                );
                                            },
                                        },
                                        {
                                            icon: 'ri-delete-bin-line',
                                            label: 'Xóa liên kết',
                                            onClick: () => ({}),
                                            className: 'text-danger',
                                        },
                                    ]}
                                    toggleIcon='ri-more-2-fill'
                                />
                            </div>
                            {company.associatedInfos.map((associatedInfo) => (
                                <div
                                    className='d-flex gap-2'
                                    key={associatedInfo.id}
                                >
                                    <p className='text-muted d-block m-0'>
                                        <strong>
                                            {associatedInfo.associatedInfoTypeName ===
                                            '2'
                                                ? 'Email'
                                                : 'Số điện thoại'}
                                            :
                                        </strong>
                                    </p>
                                    <p className='m-0'>
                                        {associatedInfo.value}
                                    </p>
                                </div>
                            ))}
                        </div>
                    ))}
                </CardBody>
            </Card>

            <Card>
                <CardHeader className='d-flex align-items-center'>
                    <h5 className='mb-0 flex-grow-1'>Cá nhân liên hệ</h5>
                    <Button
                        color='success'
                        size='sm'
                        className='btn-sm'
                        style={{
                            backgroundColor: '#0ab39c',
                            border: 'none',
                        }}
                    >
                        <i className='ri-add-line align-middle'></i> Thêm
                    </Button>
                </CardHeader>
                <CardBody>
                    {contacts.map((contact) => (
                        <div key={contact.id} className='border-bottom py-2'>
                            <div className='d-flex justify-content-between align-items-center mb-2'>
                                <h6
                                    className='m-0'
                                    style={{ height: 'fit-content' }}
                                >
                                    {contact.name}
                                </h6>
                                <DropdownActionMenu
                                    actions={[
                                        {
                                            icon: 'ri-eye-line',
                                            label: 'Xem nhanh',
                                            onClick: () =>
                                                toggleQuickView('customer'),
                                        },
                                        {
                                            icon: 'ri-eye-fill',
                                            label: 'Xem chi tiết',
                                            onClick: () => {
                                                router.push(
                                                    ROUTES.CRM.CUSTOMERS.DETAIL.replace(
                                                        ':id',
                                                        contact.id,
                                                    ),
                                                );
                                            },
                                        },
                                        {
                                            icon: 'ri-delete-bin-line',
                                            label: 'Xóa liên kết',
                                            onClick: () => ({}),
                                            className: 'text-danger',
                                        },
                                    ]}
                                    toggleIcon='ri-more-2-fill'
                                />
                            </div>
                            <div className='d-flex gap-2'>
                                <p className='text-muted d-block m-0'>
                                    <strong>Email</strong>
                                </p>
                                <p className='m-0'>{contact.email}</p>
                            </div>
                            <div className='d-flex gap-2'>
                                <p className='text-muted d-block m-0'>
                                    <strong>Số điện thoại</strong>
                                </p>
                                <p className='m-0'>{contact.phoneNumber}</p>
                            </div>
                            <div className='d-flex gap-2'>
                                <p className='text-muted d-block m-0'>
                                    <strong>Phòng ban</strong>
                                </p>
                                <p className='m-0'>{contact.departmentName}</p>
                            </div>
                            <div className='d-flex gap-2'>
                                <p className='text-muted d-block m-0'>
                                    <strong>Chức vụ</strong>
                                </p>
                                <p className='m-0'>{contact.positionName}</p>
                            </div>
                            <div className='d-flex gap-2'>
                                <p className='text-muted d-block m-0'>
                                    <strong>Vai trò</strong>
                                </p>
                                <p className='m-0'>{contact.roleName}</p>
                            </div>
                        </div>
                    ))}
                </CardBody>
            </Card>

            {/* Quick View Modal */}
            <QuickViewModal
                isOpen={isQuickViewOpen}
                toggle={() => toggleQuickView()}
                data={selectedItem === 'customer' ? customerData : partnerData}
            />
        </Col>
    );
};

export default FileInfo;
