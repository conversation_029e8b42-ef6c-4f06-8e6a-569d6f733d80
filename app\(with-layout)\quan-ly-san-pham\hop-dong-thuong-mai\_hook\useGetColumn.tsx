import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '@/types/actions.type';
import { ResponseContract } from '@/apis/contracts/contracts.type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: ResponseContract | undefined,
    ) => void;
    page: string;
    onRestore?: (data: ResponseContract) => void;
}

const useGetColumn = ({
    onSelectedAction,
    page,
    onRestore,
}: GetColumnProps) => {
    const actions: DropdownAction<ResponseContract>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-download-2-line',
                label: 'T<PERSON>i xuống',
                onClick: (data) => onSelectedAction?.(ACTIONS.DOWNLOAD, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<ResponseContract>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên hợp đồng',
                enableHiding: false,
                minSize: 200,
                maxSize: 300,
                size: 250,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '300px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    },
                },
            },
            {
                accessorKey: 'dealName',
                header: 'Cơ hội',
                minSize: 200,
                maxSize: 350,
                size: 250,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '200px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    },
                },
            },
            {
                accessorKey: 'buyerRepresentative',
                header: 'Tên khách hàng',
                minSize: 150,
                maxSize: 200,
                size: 200,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '250px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    },
                },
            },
            {
                accessorKey: 'numbersVersion',
                header: 'Số phiên bản',
                minSize: 50,
                maxSize: 150,
                size: 150,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        textAlign: 'center',
                        maxWidth: '150px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        textAlign: 'center',
                    },
                },
            },
            {
                accessorKey: 'createdDateTime',
                header: 'Ngày tạo',
                minSize: 120,
                maxSize: 150,
                size: 130,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        textAlign: 'center',
                        maxWidth: '150px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        textAlign: 'center',
                    },
                },
                Cell: ({ row }) => {
                    const dateStr = row.original.createdDateTime?.substring(
                        0,
                        10,
                    );
                    const [year, month, day] = dateStr?.split('-') || [];
                    const formattedDate = `${day}/${month}/${year}`;
                    return (
                        <span style={{ whiteSpace: 'nowrap' }}>
                            {formattedDate}
                        </span>
                    );
                },
            },
            {
                accessorKey: 'userNameCreated',
                header: 'Người tạo',
                minSize: 120,
                maxSize: 250,
                size: 250,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '180px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    },
                },
            },
            {
                header: 'Hành động',
                enableResizing: false,
                minSize: 100,
                maxSize: 120,
                size: 110,
                enableWrapping: false,
                muiTableBodyCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textAlign: 'center',
                        maxWidth: '120px',
                    },
                },
                muiTableHeadCellProps: {
                    sx: {
                        whiteSpace: 'nowrap !important',
                        overflow: 'hidden',
                        textAlign: 'center',
                    },
                },
                Cell: ({ row }) => {
                    if (page === 'list') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    return;
                },
            },
        ],
        [actions, page, onRestore],
    );

    return columns;
};

export default useGetColumn;
