'use client';
import ButtonHeader from '@/components/common/ButtonHeader';
import FormController from '@/components/common/FormController';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownToggle,
} from 'reactstrap';
import useGetColumn, { SearchUnitManagement } from './_hook/useGetColumns';
import { ACTIONS } from './_types/action.type';

import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import MantineTable from '@/components/common/MantineReactTable';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';

const data = [
    {
        id: 1,
        name: 'Công ty TNHH A',
        organizational_Level: 'Công ty',
        unit_Head: '<PERSON><PERSON><PERSON><PERSON>',
        status: '<PERSON><PERSON> hoạt động',
        children: [
            {
                id: 2,
                name: '<PERSON> nhánh A',
                organizational_Level: '<PERSON> nh<PERSON>h',
                unit_Head: 'Trần Văn Nam',
                status: '<PERSON>ang hoạt động',
            },
            {
                id: 3,
                name: 'Chi nhánh B',
                organizational_Level: 'Chi nhánh',
                unit_Head: 'Nguyễn Đức Hoàng',
                status: 'Đang hoạt động',
                children: [
                    {
                        id: 4,
                        name: 'Phòng Kỹ thuật',
                        organizational_Level: 'Phong ban',
                        unit_Head: '',
                        status: 'Đang hoạt động',
                    },
                    {
                        id: 5,
                        name: 'Phòng Kinh doanh',
                        organizational_Level: 'Phong ban',
                        unit_Head: 'Vỹ Xuân Huy',
                        status: 'Đang hoạt động',
                    },
                ],
            },
            {
                id: 6,
                name: 'Chi nhánh C',
                organizational_Level: 'Chi nhánh',
                unit_Head: 'Trần Văn Mạnh',
                status: 'Đang hoạt động',
            },
        ],
    },
    {
        id: 7,
        name: 'Chi nhánh TP Hồ Chí Minh',
        organizational_Level: 'Chi nhánh',
        unit_Head: 'Nguyễn Văn Trung',
        status: 'Ngừng hoạt động',
    },
];
const UnitManagement = () => {
    const router = useRouter();
    const [dropdownOpen, setDropdownOpen] = useState(false);

    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const methods = useForm({
        defaultValues: {
            pageNumber: 1,
            pageSize: 10,
        },
    });

    const handleSelectedAction = (
        action: ACTIONS,
        row: SearchUnitManagement | undefined,
    ) => {
        if (!row) {
            console.error('No contact data provided');
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                break;
            case ACTIONS.EDIT:
                break;
            case ACTIONS.VIEW_DETAIL:
                break;
            // case ACTIONS.ACTIVITY:
            //     break;
            case ACTIONS.IN_ACTIVITY:
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const column = useGetColumn({
        onSelectedAction: handleSelectedAction,
    });

    const handleCreate = () => {
        router.push(ROUTES.SYSTEM_MANAGEMENT.UNIT_MANAGEMENT.CREATE);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        showImportButton={false}
                        showExportButton={false}
                        onCreateNew={handleCreate}
                    />
                </Col>
                <Col>
                    <Card>
                        <CardHeader>
                            <div className='d-flex justify-content-between align-items-center'>
                                <div style={{ flex: 1 }}>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên đơn vị...'
                                    />
                                </div>
                                <div className='d-flex gap-2 ms-auto'>
                                    <FormController
                                        controlType='select'
                                        name='trangThai'
                                        placeholder='Trạng thái'
                                        data={[
                                            {
                                                label: 'Tất cả',
                                                value: '1',
                                            },
                                            {
                                                label: 'Đang hoạt động',
                                                value: '2',
                                            },
                                            {
                                                label: 'Ngừng hoạt động',
                                                value: '3',
                                            },
                                        ]}
                                        clearable={true}
                                        style={{ width: '200px' }}
                                    />
                                    <Dropdown
                                        isOpen={dropdownOpen}
                                        toggle={toggleDropdown}
                                        direction='down'
                                    >
                                        <DropdownToggle
                                            outline
                                            className='settings-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-settings-2-line text-info'></i>
                                        </DropdownToggle>
                                    </Dropdown>
                                </div>
                            </div>
                        </CardHeader>
                        <MantineTable
                            columns={column}
                            data={data}
                            totalItems={1}
                            onPageChange={(page: number) => {
                                console.error(page);
                            }}
                            onPageSizeChange={(size: number) => {
                                console.error(size);
                            }}
                        />
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};

export default UnitManagement;
