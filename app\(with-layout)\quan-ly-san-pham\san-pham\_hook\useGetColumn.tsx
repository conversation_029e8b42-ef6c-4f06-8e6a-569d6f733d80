import { ResponseSearchProduct } from '@/apis/product/product.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '../_types/action.type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: ResponseSearchProduct | undefined,
    ) => void;
    page: string;
    onRestore?: (data: ResponseSearchProduct) => void;
}

const useGetColumn = ({
    onSelectedAction,
    page,
    onRestore,
}: GetColumnProps) => {
    const actions: DropdownAction<ResponseSearchProduct>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<ResponseSearchProduct>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên sản phẩm',
                enableHiding: false,
                size: 280,
                minSize: 200,
                maxSize: 350,
            },
            {
                accessorKey: 'categoryName',
                header: 'Nhóm sản phẩm',
                size: 280,
                minSize: 200,
                maxSize: 350,
            },
            {
                accessorKey: 'productVersion',
                header: 'Phiên bản sử dụng',
                size: 120,
                minSize: 100,
                maxSize: 200,
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<ResponseSearchProduct>),
                size: 70,
                minSize: 50,
                maxSize: 100,
                enableColumnActions: false,
                Cell: ({ row }) => {
                    if (page === 'list') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                                style={{ marginLeft: '-50px' }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    return;
                },
            },
        ],
        [actions, page, onRestore],
    );

    return columns;
};

export default useGetColumn;
