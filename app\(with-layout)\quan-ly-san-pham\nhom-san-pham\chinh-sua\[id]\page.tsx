'use client';
import {
    useDetailProductGroups,
    useUpdateProductGroups,
} from '@/apis/product-group/product-group.api';
import { IProductGroup } from '@/apis/product-group/product-group.type';
import { ROUTES } from '@/lib/routes';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';
import FormProductGroups from '../../_components/FormProductGroups';
import { KEYS_TO_PRODUCT_GROUP } from '@/constants/key-convert';

const UpdateProductGroups = () => {
    const params = useParams();
    const router = useRouter();
    const id = params.id as string;
    const { data: productGroups } = useDetailProductGroups(id);
    const initValue = productGroups?.data;
    const { mutate: updateProductGroups } = useUpdateProductGroups({
        onSuccess: () => {
            toast.success('Chỉnh sửa thông tin nhóm sản phẩm thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.INDEX);
        },
        onError: () => {
            toast.error('Chỉnh sửa thông tin nhóm sản phẩm thất bại');
        },
    });
    const handleUpdate = (data: IProductGroup) => {
        const payload = convertFormValueToPayload(data, KEYS_TO_PRODUCT_GROUP);

        updateProductGroups(payload as IProductGroup);
    };
    const handleCancel = () => {
        router.back();
    };
    if (!initValue) {
        return <Spinner />;
    }
    return (
        <FormProductGroups
            page='chinh-sua'
            initValue={convertPayloadToFormValue(initValue) as IProductGroup}
            onEdit={handleUpdate}
            onCancel={handleCancel}
        />
    );
};
export default UpdateProductGroups;
