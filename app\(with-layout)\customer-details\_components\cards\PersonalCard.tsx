import React from 'react';
import { Card, CardBody } from 'reactstrap';
import DataCardHeader from './DataCardHeader';
import HeaderRow from './HeaderRow';
import DataRow from './DataRow';
import ViewAllButton from './ViewAllButton';

const PersonalCard: React.FC = () => (
    <div className='mb-4'>
        <Card className='bg-white'>
            <DataCardHeader title='Cá nhân' count={1} />
            <CardBody>
                <div className='d-flex w-100 justify-content-between align-items-center mb-2'>
                    <HeaderRow title='Trần Trung Hiếu' />
                </div>
                <div className='mb-2'>
                    <span className='badge bg-success-subtle text-success'>
                        Người ra quyết định chính
                    </span>
                </div>
                <DataRow
                    label='Email:'
                    value='<EMAIL>'
                    badgeText='+2'
                />
                <DataRow label='Số điện thoại:' value='--' />
                <DataRow label='Vai trò:' value='Người thẩm định kỹ thuật' />
                <ViewAllButton />
            </CardBody>
        </Card>
    </div>
);

export default PersonalCard;
