'use client';
import {
    useDeleteQuotes,
    useGetQuotes,
    useGetQuotesTemplate,
} from '@/apis/quotes/quotes.api';
import { IQuoteParams, IQuoteReponse } from '@/apis/quotes/quotes.type';
import CardHeaderTabs from '@/components/common/CardHeaderTabs';
import FormController from '@/components/common/FormController';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import InputDateRangePickerControl from '@/components/common/FormController/InputDateRangePickerControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ImportFileModal from '@/components/common/ImportFile';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { ROUTES } from '@/lib/routes';
import { ACTIONS } from '@/types/actions.type';
import { getOneMonthAgo, getToday } from '@/utils/time';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import { Button, Card, CardHeader, Col, Container } from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { statusOptions, TabValues } from './type';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<IQuoteReponse>,
        })),
    {
        ssr: false,
    },
);

const tabs = [
    {
        label: 'Báo giá liên kết',
        value: TabValues.LINK_QUOTE,
    },
    {
        label: 'Mẫu báo giá',
        value: TabValues.QUOTATION_FORM,
    },
];

const Quotes = () => {
    const router = useRouter();

    const [tabCurrent, setTabCurrent] = useState<string>(tabs[0].value);

    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);

    const methods = useForm<IQuoteParams>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        FieldSearch,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
        Status,
        FromDate,
        ToDate,
    ] = useWatch({
        control,
        name: [
            'FieldSearch',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
            'Status',
            'FromDate',
            'ToDate',
        ],
    });

    const { mutate: deleteQuotes } = useDeleteQuotes({
        onSuccess: () => {
            toast.success('Xóa khách báo giá thành công');
            setIsDeleteModalOpen(false);
            setSelectedIds([]);
            // refetch();
        },
        onError: () => {
            toast.error('Xóa báo giá thất bại');
        },
    });

    const handleSelectedAction = (action: ACTIONS, row?: IQuoteReponse) => {
        if (!row) {
            console.error('No quote data provided');
            return;
        }

        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setIsDeleteModalOpen(true);
                break;
            case ACTIONS.EDIT:
                if (tabCurrent === TabValues.LINK_QUOTE) {
                    router.push(
                        ROUTES.PRODUCT_MANAGEMENT.QUOTES.UPDATE_LINK_QUOTE.replace(
                            ':id',
                            row.id || '',
                        ),
                    );
                } else {
                    router.push(
                        ROUTES.PRODUCT_MANAGEMENT.QUOTES.UPDATE_QUOTATION_FORM.replace(
                            ':id',
                            row.id || '',
                        ),
                    );
                }

                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.QUOTES.DETAIL_LINK_QUOTE.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list-quote',
        tab: tabCurrent as TabValues,
    });

    const { data: dataQuotes, isLoading: isLoadingQuotes } = useGetQuotes({
        FieldSearch,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
        Status,
        FromDate,
        ToDate,
    });

    const { data: dataQuotesTemplate, isLoading: isLoadingQuotesTemplate } =
        useGetQuotesTemplate({
            FieldSearch,
            PageNumber,
            PageSize,
            SortField,
            IsDescending,
            Status,
            FromDate,
            ToDate,
        });

    const dataSource = useMemo(() => {
        if (tabCurrent === TabValues.LINK_QUOTE) {
            if (dataQuotes?.data?.items) {
                return dataQuotes.data.items;
            }

            return [];
        }

        if (tabCurrent === TabValues.QUOTATION_FORM) {
            if (dataQuotesTemplate?.data?.items) {
                return dataQuotesTemplate.data.items;
            }

            return [];
        }

        return [];
    }, [tabCurrent, dataQuotes?.data, dataQuotesTemplate?.data]);

    const totalItems = useMemo(() => {
        if (tabCurrent === TabValues.LINK_QUOTE) {
            if (dataQuotes?.data?.totalItems) {
                return dataQuotes?.data?.totalItems;
            }
        }

        if (tabCurrent === TabValues.QUOTATION_FORM) {
            if (dataQuotesTemplate?.data?.totalItems) {
                return dataQuotesTemplate.data.totalItems;
            }
        }

        return 0;
    }, [tabCurrent, dataQuotes?.data, dataQuotesTemplate?.data]);

    const handleConfirmDelete = () => {
        deleteQuotes({ ids: selectedIds });
    };

    const handleCloseModalDelete = () => {
        setIsDeleteModalOpen(false);
    };

    // const onCreateNew = () => {
    //     router.push(ROUTES.CRM.PARTNERS.CREATE);
    // };

    const handlePageChange = (page: number) => {
        setValue('PageNumber', page);
    };

    const handlePageSizeChange = (size: number) => {
        setValue('PageSize', size);
    };

    const placeholderSearchName = useMemo(() => {
        if (tabCurrent === TabValues.LINK_QUOTE) {
            return 'Tìm kiếm tên báo giá hoặc tên cơ hội...';
        }

        return 'Tìm kiếm theo mẫu báo giá...';
    }, [tabCurrent]);

    const handleChangeTab = (tabActive: string) => {
        setTabCurrent(tabActive);
        setSelectedIds([]); // Reset selected items khi chuyển tab

        // Reset các filter về mặc định khi chuyển tab
        setValue('PageNumber', 1);
        setValue('FieldSearch', '');
        setValue('Status', '');
        // setValue('SalePersonId', '');
    };

    const buttonCreate = useMemo(() => {
        if (tabCurrent === TabValues.QUOTATION_FORM) {
            return {
                label: 'Tạo mẫu báo giá',
                onClick: () => {
                    router.push(
                        ROUTES.PRODUCT_MANAGEMENT.QUOTES.CREATE_QUOTATION_FORM,
                    );
                },
            };
        }

        return {
            label: 'Tạo báo giá',
            onClick: () => {
                router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.CREATE_LINK_QUOTE);
            },
        };
    }, [router, tabCurrent]);

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <Card>
                        <CardHeaderTabs
                            tabs={tabs}
                            tabCurrent={tabCurrent}
                            onChangeTab={handleChangeTab}
                            actions={
                                <>
                                    <Button
                                        style={{
                                            backgroundColor: '#0ab39c',
                                            border: 'none',
                                            padding: '6px 12px',
                                        }}
                                        onClick={buttonCreate.onClick}
                                    >
                                        <i className='ri-add-line align-bottom me-1'></i>
                                        {buttonCreate.label}
                                    </Button>
                                    <Button
                                        style={{
                                            backgroundColor: '#0ab39c',
                                            border: 'none',
                                            padding: '6px 12px',
                                        }}
                                        onClick={() =>
                                            setIsImportModalOpen(true)
                                        }
                                    >
                                        <i className='ri-add-line align-bottom me-1'></i>
                                        Import báo giá
                                    </Button>
                                </>
                            }
                        />
                        <CardHeader>
                            <div className='d-flex justify-content-between align-items-center'>
                                <div className='d-flex gap-3 justify-content-between'>
                                    <div className='d-flex flex-column'>
                                        <div className='d-flex align-items-center gap-3'>
                                            <div className='me-2'>
                                                <InputDateRangePickerControl
                                                    nameFrom='FromDate'
                                                    nameTo='ToDate'
                                                    allowFutureDates={false}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder={placeholderSearchName}
                                    />

                                    <ComboboxSelectUserControl
                                        name='SalePersonId'
                                        placeholder='Nhân viên kinh doanh'
                                    />

                                    {tabCurrent === TabValues.LINK_QUOTE && (
                                        <FormController
                                            controlType='select'
                                            name='Status'
                                            placeholder='Trạng thái'
                                            data={statusOptions}
                                            style={{ width: '20%' }}
                                            clearable={true}
                                        />
                                    )}
                                </div>
                                <div className='d-flex justify-content-end gap-2'>
                                    {selectedIds.length > 0 && (
                                        <>
                                            <Button
                                                style={{
                                                    backgroundColor: 'red',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => {
                                                    setIsDeleteModalOpen(true);
                                                }}
                                            >
                                                Xóa ({selectedIds.length})
                                            </Button>
                                        </>
                                    )}
                                    <Button
                                        style={{
                                            backgroundColor: 'white',
                                            color: '#0ab39c',
                                            borderColor: '#0ab39c',
                                        }}
                                    >
                                        <i className='ri-filter-line'></i>
                                    </Button>
                                    <Button
                                        style={{
                                            backgroundColor: 'white',
                                            color: '#0ab39c',
                                            borderColor: '#0ab39c',
                                        }}
                                        onClick={() => {
                                            router.push(
                                                ROUTES.CRM.PARTNERS.RESTORE,
                                            );
                                        }}
                                    >
                                        <i className='ri-refresh-line me-1'></i>
                                    </Button>
                                </div>
                            </div>
                        </CardHeader>
                        <MantineTable
                            key={tabCurrent} // Force re-render khi chuyển tab
                            columns={columns}
                            data={dataSource}
                            isLoading={
                                isLoadingQuotes || isLoadingQuotesTemplate
                            }
                            totalItems={Number(totalItems)}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index = dataSource.findIndex(
                                                (quote: IQuoteReponse) =>
                                                    quote.id === id,
                                            );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        dataSource.findIndex(
                                                            (
                                                                quote: IQuoteReponse,
                                                            ) =>
                                                                quote.id === id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                dataSource[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name ?? item.quoteName,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>

            <ModalDelete
                onDelete={handleConfirmDelete}
                onClose={handleCloseModalDelete}
                isOpen={isDeleteModalOpen}
                page='báo giá'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};

export default Quotes;
