import React from 'react';
import { Button } from 'reactstrap';

const ProfileHeader: React.FC = () => (
    <div className='d-flex justify-content-between align-items-center mb-4'>
        <div className='d-flex align-items-center gap-4'>
            <div
                className='avatar-sm bg-success-subtle rounded-circle text-center d-flex align-items-center justify-content-center'
                style={{ width: '40px', height: '40px' }}
            >
                <span className='fs-3'>N</span>
            </div>
            <h5 className='mb-0'>Anh Nguyễn Văn Anh</h5>
        </div>
        <div className='d-flex gap-2'>
            <Button outline className='btn-soft-dark btn-icon' size='sm'>
                <i className='bx bx-note fs-16'></i>
            </Button>
            <Button outline className='btn-soft-danger btn-icon' size='sm'>
                <i className='bx bx-envelope fs-16'></i>
            </Button>
            <Button outline className='btn-soft-success btn-icon' size='sm'>
                <i className='bx bx-phone fs-16'></i>
            </Button>
            <Button outline className='btn-soft-warning btn-icon' size='sm'>
                <i className='bx bx-message-dots fs-16'></i>
            </Button>
            <Button outline className='btn-soft-info btn-icon' size='sm'>
                <i className='bx bx-calendar fs-16'></i>
            </Button>
            <Button outline className='btn-soft-secondary btn-icon' size='sm'>
                <i className='bx bx-check-square fs-16'></i>
            </Button>
        </div>
    </div>
);

export default ProfileHeader;
