import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
    DeleteContactPayload,
    DeleteContactResponse,
    IContact,
    IContactResponse,
    SearchContact,
} from './contact.type';

const URI = '/api/v1.0/Contacts';

export const contactKey = {
    SEARCH_CONTACT: 'SEARCH_CONTACT',
    DELETE_CONTACTS: 'DELETE_CONTACTS',
    GET_DETAIL_CONTACT: 'GET_DETAIL_CONTACT',
    GET_RESTORE_CONTACTS: 'GET_RESTORE_CONTACTS',
};

export const contactUri = {
    searchContact: `${URI}/search`,
    deleteContacts: `${URI}/delete-multiple`,
    createContact: `${URI}`,
    getDetailContact: `${URI}/:id`,
    updateContact: `${URI}/:id`,
    searchRestoreContacts: `${URI}/search-restores`,
    restoreContacts: `${URI}/restore-multiple`,
};

export const contactApis = {
    searchContacts: (params: SearchContact) => {
        return http.get<ApiResponseList<IContactResponse[]>>(
            contactUri.searchContact,
            {
                params,
            },
        );
    },
    deleteContacts: (payload: DeleteContactPayload) => {
        return http.post<DeleteContactResponse>(
            contactUri.deleteContacts,
            payload,
        );
    },
    createContact: (payload: IContact) => {
        return http.post<ApiResponse<IContact>>(
            contactUri.createContact,
            payload,
        );
    },
    getDetailContact: (id: string, isUpdate?: boolean) => {
        return http.get<ApiResponse<IContact>>(
            contactUri.getDetailContact.replace(':id', id),
            {
                params: { isUpdate },
            },
        );
    },
    updateContact: (payload: IContact) => {
        const id = payload.id ?? '';

        return http.put<ApiResponse<IContact>>(
            contactUri.updateContact.replace(':id', id),
            payload,
        );
    },
    searchRestoreContacts: (params: SearchContact) => {
        return http.get<ApiResponseList<IContactResponse[]>>(
            contactUri.searchRestoreContacts,
            {
                params,
            },
        );
    },
    restoreContacts: (payload: DeleteContactPayload) => {
        return http.post<DeleteContactResponse>(
            contactUri.restoreContacts,
            payload,
        );
    },
};

export const useSearchContacts = (params: SearchContact) => {
    return useQuery({
        queryKey: [contactKey.SEARCH_CONTACT, params],
        queryFn: () => contactApis.searchContacts(params),
        select: (data) => data,
        // placeholderData: (previousData) => previousData,
    });
};

export const useSearchRestoreContacts = (params: SearchContact) => {
    return useQuery({
        queryKey: [contactKey.GET_RESTORE_CONTACTS, params],
        queryFn: () => contactApis.searchRestoreContacts(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useDeleteContacts = (props?: {
    onSuccess?: (
        data: DeleteContactPayload,
        response: DeleteContactResponse,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationKey: [contactKey.DELETE_CONTACTS],
        mutationFn: (payload: DeleteContactPayload) =>
            contactApis.deleteContacts(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [contactKey.SEARCH_CONTACT],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useCreateContact = (props?: {
    onSuccess?: (data: IContact, response: IContact) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (payload: IContact) => contactApis.createContact(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [contactKey.SEARCH_CONTACT],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useGetContactDetail = (
    id: string,
    options?: { isUpdate?: boolean },
) => {
    return useQuery({
        queryKey: [contactKey.GET_DETAIL_CONTACT, id, options?.isUpdate],
        queryFn: () => contactApis.getDetailContact(id, options?.isUpdate),
        select: (data) => data.data,
        enabled: !!id,
    });
};

export const useUpdateContact = (props?: {
    onSuccess?: (data: IContact, response: IContact) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (payload: IContact) => contactApis.updateContact(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [contactKey.SEARCH_CONTACT],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useRestoreContacts = (props?: {
    onSuccess?: (
        data: DeleteContactPayload,
        response: DeleteContactResponse,
    ) => void;
    onError?: () => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationKey: [contactKey.GET_RESTORE_CONTACTS],
        mutationFn: (payload: DeleteContactPayload) =>
            contactApis.restoreContacts(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [contactKey.GET_RESTORE_CONTACTS],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};
