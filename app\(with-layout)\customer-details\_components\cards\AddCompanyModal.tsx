import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'reactstrap';
import Select from 'react-select';
import { OptionProps } from 'react-select';

interface AddCompanyModalProps {
    isOpen: boolean;
    toggle: () => void;
}

interface Option {
    value: string;
    label: string;
    website?: string;
}

const AddCompanyModal: React.FC<AddCompanyModalProps> = ({
    isOpen,
    toggle,
}) => {
    const [selectedOption, setSelectedOption] = useState<Option | null>(null);

    // Example options - replace with your actual data
    const options: Option[] = [
        { value: '2', label: 'Lựa chọn 2', website: 'ww.ABC.com' },
        { value: '3', label: 'Lựa chọn 3', website: 'www.haohao.com' },
        { value: '4', label: 'Lựa chọn 4', website: 'www.xyz.com' },
    ];

    const CustomOption: React.FC<OptionProps<Option>> = (props) => {
        const { data, innerRef, innerProps } = props;
        return (
            <div ref={innerRef} {...innerProps} className='px-3 py-2'>
                <div className='fw-medium'>{data.label}</div>
                {data.website && (
                    <div className='text-muted small'>
                        Website: {data.website}
                    </div>
                )}
            </div>
        );
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='lg'>
            <ModalHeader toggle={toggle}>Thêm doanh nghiệp</ModalHeader>
            <ModalBody>
                <div className='mb-3'>
                    <Select
                        value={selectedOption}
                        onChange={(option) =>
                            setSelectedOption(option as Option)
                        }
                        options={options}
                        components={{ Option: CustomOption }}
                        placeholder='Tìm kiếm'
                        isClearable
                        isSearchable
                        classNamePrefix='select'
                        className='react-select'
                    />
                </div>
            </ModalBody>
            <ModalFooter>
                <Button color='secondary' onClick={toggle}>
                    Hủy
                </Button>
                <Button color='success' disabled={!selectedOption}>
                    Thêm
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default AddCompanyModal;
