'use client';

import {
    useDeleteCustomerGroups,
    useGetDetailCustomerGroups,
} from '@/apis/customer-groups/customer-groups.api';
import { ROUTES } from '@/lib/routes';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';
import DeleteConfirmModal from '../../_components/DeleteConfirmModal';
import Evaluate from '../evaluate';
import FileInfo from '../file-info';

export default function BusinessDetails() {
    const params = useParams();
    const id = params.id as string;
    const router = useRouter();
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const { mutate: deleteCustomerGroups, isPending } = useDeleteCustomerGroups(
        {
            onSuccess: () => {
                toast.success('Xóa nhóm khách hàng thành công');
                setIsDeleteModalOpen(false);
                router.push(ROUTES.CRM.PERSONAL.INDEX);
            },
            onError: () => {
                toast.error('Xóa nhóm khách hàng thất bại');
            },
        },
    );

    const handleConfirmDelete = () => {
        deleteCustomerGroups({ ids: [id] });
    };
    const { data } = useGetDetailCustomerGroups(id);

    return (
        <div>
            <Row>
                <Col lg={8}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex justify-content-between align-items-center'>
                                    <h5>Thông tin chung</h5>
                                    <div className='d-flex justify-content-center align-items-center gap-2'>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                            }}
                                        >
                                            <i className=' ri-pencil-line'></i>
                                            Chỉnh sửa
                                        </Button>
                                        <UncontrolledDropdown>
                                            <DropdownToggle
                                                tag='button'
                                                className='btn'
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    padding: '4px',
                                                    minWidth: '30px',
                                                }}
                                            >
                                                <i
                                                    className='ri-more-fill'
                                                    style={{
                                                        color: 'white',
                                                    }}
                                                ></i>
                                            </DropdownToggle>
                                            <DropdownMenu end>
                                                <DropdownItem>
                                                    <i className='ri-user-received-line me-2'></i>
                                                    Bàn giao cá nhân
                                                </DropdownItem>
                                                <DropdownItem>
                                                    <i className='ri-history-line me-2'></i>
                                                    Nhật ký hoạt động
                                                </DropdownItem>
                                                <DropdownItem
                                                    className='text-danger'
                                                    onClick={() =>
                                                        setIsDeleteModalOpen(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    <i className='ri-delete-bin-line me-2'></i>
                                                    Xóa cá nhân
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </UncontrolledDropdown>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Col lg={12} className='mb-2'>
                                        <Row>
                                            <Col lg={1}>
                                                <div className='relative inline-block'>
                                                    <div
                                                        className='flex items-center justify-center rounded-full font-bold d-flex justify-content-center align-items-center'
                                                        style={{
                                                            width: '60px',
                                                            height: '60px',
                                                            backgroundColor:
                                                                '#daf4f0',
                                                            color: '#0ab39c',
                                                            borderRadius: '50%',
                                                            fontSize: '20px',
                                                        }}
                                                    >
                                                        {data?.name?.charAt(0)}
                                                    </div>

                                                    <span className='absolute right-0 bottom-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white'></span>
                                                </div>
                                            </Col>
                                            <Col lg={7}>
                                                <div className='d-flex align-items-center h-100'>
                                                    <h5>{data?.name}</h5>
                                                </div>
                                            </Col>
                                            <Col lg={4}>
                                                <div
                                                    className='d-flex justify-content-end align-items-center gap-2'
                                                    style={{ height: '65px' }}
                                                >
                                                    <Button
                                                        outline
                                                        className='btn-soft-dark btn-icon'
                                                        size='sm'
                                                    >
                                                        <i className='bx bx-note fs-16'></i>
                                                    </Button>
                                                    <Button
                                                        outline
                                                        className='btn-soft-danger btn-icon'
                                                        size='sm'
                                                    >
                                                        <i className='bx bx-envelope fs-16'></i>
                                                    </Button>
                                                    <Button
                                                        outline
                                                        className='btn-soft-info btn-icon'
                                                        size='sm'
                                                    >
                                                        <i className='bx bx-calendar fs-16'></i>
                                                    </Button>
                                                    <Button
                                                        outline
                                                        className='btn-soft-secondary btn-icon'
                                                        size='sm'
                                                    >
                                                        <i className='bx bx-check-square fs-16'></i>
                                                    </Button>
                                                </div>
                                            </Col>
                                        </Row>
                                    </Col>
                                    <Row>
                                        <Col lg='4'>
                                            <Label>Mã nhóm khách hàng</Label>
                                            <p>{data?.code}</p>
                                        </Col>
                                        <Col lg='4'>
                                            <Label>Ngày tạo</Label>
                                            <p>
                                                {data?.createdOn
                                                    ? new Date(
                                                          data.createdOn.substring(
                                                              0,
                                                              10,
                                                          ),
                                                      ).toLocaleDateString(
                                                          'vi-VN',
                                                      )
                                                    : ''}
                                            </p>
                                        </Col>
                                        <Col lg='4'>
                                            <Label>Nhân viên kinh doanh</Label>
                                            <p>{data?.salePersonName}</p>
                                        </Col>
                                        <Col lg='12'>
                                            <Label>Mô tả</Label>
                                            <p>{data?.description}</p>
                                        </Col>
                                    </Row>
                                </Row>
                            </CardBody>
                        </Card>
                    </Col>
                    <Evaluate />
                </Col>
                {data && <FileInfo data={data} />}
            </Row>
            <DeleteConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                itemName={data?.name ? [data.name] : []}
                loading={isPending}
            />
        </div>
    );
}
