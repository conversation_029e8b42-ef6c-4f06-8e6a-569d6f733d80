import { IPipelineStage } from '@/apis/opportunity/opportunity.type';
import { DragDropContext } from '@hello-pangea/dnd';
import { useEffect, useRef, useState } from 'react';
import { useKanban } from '../context/KanbanContext';
import { KanbanColumn } from './KanbanColumn';

interface DndContainerProps {
    pipelineStages: IPipelineStage[];
}

export const DndContainer: React.FC<DndContainerProps> = ({
    pipelineStages,
}) => {
    const { onDragEnd, columns } = useKanban();
    const containerRef = useRef<HTMLDivElement>(null);
    const [isHovering, setIsHovering] = useState(false);

    // Thêm CSS cho việc ẩn/hiện thanh scroll và tối ưu hiển thị
    useEffect(() => {
        // Thêm CSS vào head của document
        const styleElement = document.createElement('style');
        styleElement.innerHTML = `
            .kanban-container::-webkit-scrollbar {
                height: 8px;
                background-color: transparent;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .kanban-container::-webkit-scrollbar-thumb {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
            }
            
            .kanban-container::-webkit-scrollbar-thumb:hover {
                background-color: rgba(0, 0, 0, 0.3);
            }
            
            .kanban-container.hovering::-webkit-scrollbar-thumb {
                opacity: 1;
            }
            
            .kanban-container:not(.hovering)::-webkit-scrollbar-thumb {
                opacity: 0;
            }
            
            /* Cho Firefox */
            .kanban-container {
                scrollbar-width: thin;
                scrollbar-color: transparent transparent;
                transition: scrollbar-color 0.3s ease;
            }
            
            .kanban-container.hovering {
                scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
            }
            
            /* Tối ưu hiển thị các cột */
            .kanban-column {
                margin-right: 2px;
                flex-shrink: 0;
            }
            
            /* Đảm bảo các thẻ cơ hội không bị tràn */
            .card.task-box {
                box-sizing: border-box;
                margin-right: 2px;
                width: 100%;
                overflow-x: hidden;
            }
            
            /* Container style */
            .kanban-container {
                display: flex;
                width: 100%;
            }
            
            /* Loại bỏ thanh cuộn ngang trong các item */
            .card-body {
                overflow-x: hidden;
                word-wrap: break-word;
                white-space: normal;
            }
            
            /* Đảm bảo tất cả các phần tử trong card không bị tràn */
            .card-body h5, 
            .card-body p, 
            .card-body div {
                overflow-x: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
                white-space: normal;
                word-wrap: break-word;
            }
            
            /* Đảm bảo các phần tử text không bị tràn */
            .text-muted {
                overflow-x: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                word-wrap: break-word;
                max-width: 100%;
            }
        `;
        document.head.appendChild(styleElement);

        return () => {
            document.head.removeChild(styleElement);
        };
    }, []);

    const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
        // Kiểm tra xem click có phải vào khoảng trống giữa các cột không
        // hoặc vào bất kỳ phần nào của container
        const container = e.currentTarget;
        const startX = e.pageX;
        const scrollLeft = container.scrollLeft;

        // Thay đổi cursor khi đang kéo
        container.style.cursor = 'grabbing';

        // Hàm xử lý khi di chuyển chuột
        const handleMouseMove = (moveEvent: MouseEvent) => {
            const dx = moveEvent.pageX - startX;
            container.scrollLeft = scrollLeft - dx;
        };

        // Hàm xử lý khi thả chuột
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            container.style.cursor = 'grab';
        };

        // Đăng ký các sự kiện
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);

        // Ngăn sự kiện lan truyền
        e.stopPropagation();
        e.preventDefault();
    };

    const handleMouseEnter = () => {
        setIsHovering(true);
    };

    const handleMouseLeave = () => {
        setIsHovering(false);
    };

    return (
        <DragDropContext onDragEnd={onDragEnd}>
            <div
                ref={containerRef}
                className={`d-flex gap-2 kanban-container ${isHovering ? 'hovering' : ''}`}
                style={{
                    width: '100%',
                    maxHeight: 'calc(100vh - 285px)',
                    overflowY: 'hidden',
                    overflowX: 'auto',
                    position: 'relative',
                    cursor: 'grab',
                    display: 'flex',
                    flexWrap: 'nowrap',
                    justifyContent: 'flex-start',
                }}
                onMouseDown={handleMouseDown}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                {pipelineStages.map((pipelineStage) => (
                    <KanbanColumn
                        key={pipelineStage.id}
                        pipelineStage={pipelineStage}
                        opportunities={columns[pipelineStage.id]?.items}
                    />
                ))}
            </div>
        </DragDropContext>
    );
};
