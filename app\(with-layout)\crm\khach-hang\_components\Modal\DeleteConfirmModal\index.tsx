import { Button, Modal, ModalBody, ModalFooter } from 'reactstrap';
import LoadingButton from '@/components/common/LoadingButton';

interface DeleteConfirmModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm?: () => void;
    itemName?: string | string[];
    loading?: boolean;
}

const DeleteConfirmModal = ({
    isOpen,
    onClose,
    onConfirm,
    itemName,
    loading = false,
}: DeleteConfirmModalProps) => {
    const isMultiple = Array.isArray(itemName);
    const displayText = isMultiple
        ? `${itemName.length} khách hàng đã chọn`
        : itemName;

    return (
        <Modal isOpen={isOpen} toggle={onClose} centered>
            <div className='modal-header'>
                <h5 className='modal-title'>Thông báo</h5>
            </div>
            <ModalBody className='text-center'>
                <PERSON><PERSON>n có chắc chắn muốn xóa {displayText}?
            </ModalBody>
            <ModalFooter className='justify-content-center border-top-0'>
                <Button
                    color='secondary'
                    onClick={onClose}
                    style={{
                        backgroundColor: 'white',
                        color: '#7a7a7a',
                        border: '1px solid #7a7a7a',
                        minWidth: '100px',
                    }}
                    disabled={loading}
                >
                    Hủy
                </Button>
                <LoadingButton
                    color='success'
                    onClick={onConfirm}
                    style={{
                        backgroundColor: '#0ab39c',
                        border: 'none',
                        minWidth: '100px',
                    }}
                    loading={loading}
                >
                    Xác nhận
                </LoadingButton>
            </ModalFooter>
        </Modal>
    );
};

export default DeleteConfirmModal;
