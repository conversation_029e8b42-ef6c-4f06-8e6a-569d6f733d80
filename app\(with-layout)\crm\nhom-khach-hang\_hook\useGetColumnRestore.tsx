import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

import { ResponseSearchRestoresCustomerGroups } from '@/apis/customer-groups/customer-groups.type';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';

interface GetColumnRestoreProps {
    onRestore: (row: ResponseSearchRestoresCustomerGroups) => void;
}

const useGetColumnRestore = ({ onRestore }: GetColumnRestoreProps) => {
    const columns = useMemo<
        MRT_ColumnDef<ResponseSearchRestoresCustomerGroups>[]
    >(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên nhóm khách hàng',
                enableHiding: false,
            },
            {
                accessorKey: 'description',
                header: 'Mô tả',
            },
            {
                accessorKey: 'salePerson',
                header: 'Nhân viên kinh doanh',
            },

            {
                accessorKey: 'deletedDate',
                header: '<PERSON><PERSON><PERSON> xóa',
                Cell: ({ row }) => (
                    <FormattedDateTimeWithFormat
                        date={row.original.deletedDate}
                    />
                ),
            },

            {
                header: 'Hành động',
                enableResizing: false,
                Cell: ({
                    row,
                }: {
                    row: {
                        original: ResponseSearchRestoresCustomerGroups;
                    };
                }) => {
                    return (
                        <button
                            className='btn btn-link p-0'
                            style={{ fontSize: '13px', color: '#0ab39c' }}
                            onClick={(e) => {
                                e.stopPropagation();
                                onRestore?.(row.original);
                            }}
                        >
                            <i className='ri-refresh-line me-1'></i>
                            Khôi phục
                        </button>
                    );
                },
            },
        ],
        [onRestore],
    );

    return columns;
};

export default useGetColumnRestore;
