import React from 'react';
import { Card, CardBody } from 'reactstrap';

const DetailedAddress: React.FC = () => (
    <Card className='border'>
        <CardBody>
            <div className='row'>
                <div className='col-md-3 mb-3'>
                    <label className='form-label font-semibold'>QUỐC GIA</label>
                    <div>Việt Nam</div>
                </div>
                <div className='col-md-3 mb-3'>
                    <label className='form-label font-semibold'>
                        TỈNH/THÀNH PHỐ
                    </label>
                    <div>Hà Nội</div>
                </div>
                <div className='col-md-3 mb-3'>
                    <label className='form-label font-semibold'>
                        QUẬN/HUYỆN
                    </label>
                    <div>Quận Bắc Từ Liêm</div>
                </div>
                <div className='col-md-3 mb-3'>
                    <label className='form-label font-semibold'>
                        XÃ/PHƯỜNG
                    </label>
                    <div>Cổ Nhuế 1</div>
                </div>
                <div className='col-12'>
                    <label className='form-label font-semibold'>
                        ĐỊA CHỈ CỤ THỂ
                    </label>
                    <div>Số nhà 88, TDP Hoàng 9, ngõ 195 đường Trần Cung</div>
                </div>
            </div>
        </CardBody>
    </Card>
);

export default DetailedAddress;
