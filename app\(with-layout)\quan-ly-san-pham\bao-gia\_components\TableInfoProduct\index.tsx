import { IProductDisplayDeal, IQuotePayload } from '@/apis/quotes/quotes.type';
import { DealType } from '@/app/(with-layout)/crm/co-hoi-ban-hang/types';
import FormController from '@/components/common/FormController';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';

interface InfoProductProps {
    index: number;
    key: string;
    item: IProductDisplayDeal;
    isEdit?: boolean;
    dealType: DealType;
}

const renderStyle = (width: number, fontWeight = 600) => {
    const css: Record<string, string | number> = {
        fontSize: '16px',
        fontWeight,
    };
    if (width) {
        css.width = width + 'px';
    }

    return css;
};

const RowInfoProduct = (props: InfoProductProps) => {
    const { index, key, item, isEdit, dealType } = props;

    const methods = useFormContext<IQuotePayload>();
    const { control } = methods;

    const formData = useWatch({ control });

    const exchangeRate = Number(formData?.exchangeRate || 0);
    const serviceFee = Number(formData?.items?.[index]?.serviceFee || 0);
    const importTaxPercent = Number(
        formData?.items?.[index]?.importTaxPercent || 0,
    );
    const extendedDiscountRequest = Number(
        formData?.items?.[index]?.extendedDiscountRequest || 0,
    );
    const fctTax = Number(formData?.items?.[index]?.fctTax || 0);
    const quantity = Number(formData?.items?.[index]?.quantity || 0);
    const markUp = Number(formData?.items?.[index]?.markUp || 0);

    const listPrice = Number(item.listPrice);
    const standardDiscount = Number(item.standardDiscount);

    const totalDiscount = standardDiscount + extendedDiscountRequest;
    const netPrice = listPrice * totalDiscount;
    const totalNetPrice = netPrice * quantity;
    const importTax = importTaxPercent * netPrice;
    const totalImportTax = importTax * quantity;
    const unitFctTax = netPrice / (1 - fctTax * fctTax);
    const totalFctTax = unitFctTax * quantity;
    const unitPriceIncludingTax = netPrice + importTax + unitFctTax;
    const totalIncludingTax = unitPriceIncludingTax * quantity;

    let unitPrice = listPrice / (1 - markUp);
    if (listPrice < unitPriceIncludingTax) {
        unitPrice = unitPriceIncludingTax / (1 - markUp);
    }

    unitPrice = Math.ceil(unitPrice);

    const totalPrice = unitPrice * quantity;
    const totalPricePerSet = totalPrice * exchangeRate + serviceFee;

    if (isEdit) {
        return (
            <tr>
                <td>{index + 1}</td>
                <td>{item.modelCode}</td>
                {dealType === DealType.Classic && (
                    <td style={renderStyle(150)}>{item.optionCode}</td>
                )}
                <td>{item.description}</td>
                <td>{item.productType}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.serviceFee`}
                        placeholder='Nhập giá'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{listPrice}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.quantity`}
                        placeholder='Nhập số lượng'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{item.standardDiscount}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.extendedDiscountRequest`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{totalDiscount}</td>
                <td>{netPrice}</td>
                <td>{totalNetPrice}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.importTaxPercent`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{importTax}</td>
                <td>{totalImportTax}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.fctTax`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{unitFctTax}</td>
                <td>{totalFctTax}</td>
                <td>{unitPriceIncludingTax}</td>
                <td>{totalIncludingTax}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.markUp`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{unitPrice}</td>
                <td>{totalPrice}</td>
                <td>{totalPricePerSet}</td>
            </tr>
        );
    }

    return (
        <tr key={key}>
            <td>STT</td>
            <td>Model code</td>
            {dealType === DealType.Classic && <td>Option code</td>}
            <td>Description</td>
            <td>Subcription type</td>
            <td>Service fee</td>
            <td>List price</td>
            <td>Quantity</td>
            <td>Standard Discount</td>
            <td>Ex. dist. Request (%)</td>
            <td>Total Discount (%)</td>
            <td>NET price (USD)</td>
            <td>Total NET price (USD)</td>
            <td>IMPORT Tax (%)</td>
            <td>IMPORT Tax</td>
            <td>Total IMPORT Tax</td>
            <td>FCT tax</td>
            <td>Unit FCT Tax</td>
            <td>Total FCT Tax</td>
            <td>Unit Price INCLUDING Tax (USD)</td>
            <td>Mark up</td>
            <td>Unit Price (USD)</td>
            <td>Total Price (USD)</td>
            <td>Đơn giá (VND)</td>
            <td>Tổng giá theo bộ (VND)</td>
        </tr>
    );
};

interface TableInfoProductProps {
    dealType: DealType;
}

const TableInfoProduct = (props: TableInfoProductProps) => {
    const { dealType } = props;

    const methods = useFormContext<IQuotePayload>();
    const { control } = methods;

    const { fields: items } = useFieldArray({
        control,
        name: 'items',
    });

    const watchedItems = useWatch({
        control,
        name: 'items',
        defaultValue: [],
    });

    const renderInfoProducts = () => (
        <table
            className='table table-bordered mb-0'
            style={{ tableLayout: 'fixed', width: '100%' }}
        >
            <thead>
                <tr>
                    <th style={renderStyle(70)}>STT</th>
                    <th style={renderStyle(135)}>Model code</th>
                    {dealType === DealType.Classic && (
                        <th style={renderStyle(150)}>Option code</th>
                    )}
                    <th style={renderStyle(180)}>Description</th>
                    <th style={renderStyle(150)}>Subcription type</th>
                    <th style={renderStyle(150)}>Service fee</th>
                    <th style={renderStyle(100)}>List price</th>
                    <th style={renderStyle(160)}>Quantity</th>
                    <th style={renderStyle(170)}>Standard Discount</th>
                    <th style={renderStyle(200)}>Ex. dist. Request (%)</th>
                    <th style={renderStyle(150)}>Total Discount</th>
                    <th style={renderStyle(150)}>NET price (USD)</th>
                    <th style={renderStyle(190)}>Total NET price (USD)</th>
                    <th style={renderStyle(150)}>IMPORT Tax (%)</th>
                    <th style={renderStyle(120)}>IMPORT Tax</th>
                    <th style={renderStyle(160)}>Total IMPORT Tax</th>
                    <th style={renderStyle(130)}>FCT tax</th>
                    <th style={renderStyle(120)}>Unit FCT Tax</th>
                    <th style={renderStyle(130)}>Total FCT Tax</th>
                    <th style={renderStyle(300)}>
                        Unit Price INCLUDING Tax (USD)
                    </th>
                    <th style={renderStyle(220)}>Total Including Tax (USD)</th>
                    <th style={renderStyle(130)}>Mark up</th>
                    <th style={renderStyle(170)}>Unit Price (USD)</th>
                    <th style={renderStyle(155)}>Total Price (USD)</th>
                    <th style={renderStyle(210)}>Tổng giá theo bộ (VND)</th>
                </tr>
            </thead>
            <tbody>
                {items.length === 0 ? (
                    <tr>
                        <td colSpan={11} className='text-center py-3'>
                            Chưa có dữ liệu về sản phẩm
                        </td>
                    </tr>
                ) : (
                    <>
                        {items.map((_, index) => (
                            <RowInfoProduct
                                index={index}
                                key={`item-${index}`}
                                item={
                                    watchedItems[index] ||
                                    ({} as IProductDisplayDeal)
                                }
                                isEdit={true}
                                dealType={dealType}
                            />
                        ))}
                        <tr>
                            <td
                                colSpan={11}
                                style={renderStyle(0)}
                                className='text-center py-3'
                            >
                                Total NET price
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td
                                colSpan={11}
                                style={renderStyle(0)}
                                className='text-center py-3'
                            >
                                Discount to customer
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td
                                colSpan={11}
                                style={renderStyle(0)}
                                className='text-center py-3'
                            >
                                Final total CIP price
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </>
                )}
            </tbody>
        </table>
    );

    return (
        <div className='mb-4'>
            <label style={{ fontWeight: 500, fontSize: '14px' }}>
                Thông tin sản phẩm
            </label>
            <div
                className='table-scroll-container'
                style={{ maxWidth: '100%', overflowX: 'auto' }}
            >
                {renderInfoProducts()}
            </div>
            <style jsx>{`
                .table-scroll-container::-webkit-scrollbar {
                    height: 8px;
                }

                .table-scroll-container::-webkit-scrollbar-track {
                    background: transparent;
                }

                .table-scroll-container::-webkit-scrollbar-thumb {
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 4px;
                }

                .table-scroll-container::-webkit-scrollbar-button {
                    display: none;
                    width: 0;
                    height: 0;
                }

                .table-scroll-container::-webkit-scrollbar-button:start:decrement,
                .table-scroll-container::-webkit-scrollbar-button:end:increment {
                    display: none;
                    width: 0;
                    height: 0;
                }

                .table-scroll-container::-webkit-scrollbar-corner {
                    background: transparent;
                }

                .table-scroll-container {
                    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
                    scrollbar-width: thin;
                }
            `}</style>
        </div>
    );
};

export default TableInfoProduct;
