import React from 'react';
import { Table, Badge } from 'reactstrap';
import { Event } from './CalendarData';

interface ListViewProps {
    events: Event[];
}

const ListView: React.FC<ListViewProps> = ({ events }) => {
    const sortedEvents = [...events].sort((a, b) => {
        const dateA = a.date.split('-').reverse().join('-');
        const dateB = b.date.split('-').reverse().join('-');
        return new Date(dateB).getTime() - new Date(dateA).getTime();
    });

    const getEventTypeColor = (type: string) => {
        switch (type) {
            case 'meeting':
                return 'primary';
            case 'report':
                return 'info';
            case 'client':
                return 'warning';
            case 'personal':
                return 'success';
            default:
                return 'secondary';
        }
    };

    // Lấy tên loại sự kiện
    const getEventTypeName = (type: string) => {
        switch (type) {
            case 'meeting':
                return 'Cuộc họp';
            case 'report':
                return 'B<PERSON>o cáo';
            case 'client':
                return 'Khách hàng';
            case 'personal':
                return 'Cá nhân';
            default:
                return 'Khác';
        }
    };

    return (
        <div className='list-view mt-4'>
            <Table responsive hover>
                <thead>
                    <tr>
                        <th style={{ width: '15%' }}>Ngày</th>
                        <th style={{ width: '10%' }}>Giờ</th>
                        <th style={{ width: '15%' }}>Loại</th>
                        <th style={{ width: '60%' }}>Tiêu đề</th>
                    </tr>
                </thead>
                <tbody>
                    {sortedEvents.length > 0 ? (
                        sortedEvents.map((event) => (
                            <tr key={event.id} style={{ cursor: 'pointer' }}>
                                <td>{event.date}</td>
                                <td>{event.time}</td>
                                <td>
                                    <Badge
                                        color={getEventTypeColor(event.type)}
                                        pill
                                    >
                                        {getEventTypeName(event.type)}
                                    </Badge>
                                </td>
                                <td>{event.title}</td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan={4} className='text-center py-4'>
                                Không có sự kiện nào
                            </td>
                        </tr>
                    )}
                </tbody>
            </Table>
        </div>
    );
};

export default ListView;
