import http, { ApiResponse } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ApiResponseList } from '../../lib/apiBase';
import {
    SearchCustomerResponse,
    SearchCustomer,
    ICustomer,
    DeleteCustomer,
    SearchRestores,
    ResponseSearchRestores,
    RestoreCustomer,
    UpdateCustomerPayload,
} from './customer.type';

const URI = '/api/v1.0/Customer';

export const CustomerKey = {
    SEARCH_Customer: 'SEARCH_Customer',
    GET_DETAIL_Customer: 'GET_DETAIL_Customer',
    DELETE_Customer: 'DELETE_Customer',
    SEARCH_Restores: 'SEARCH_Restores',
    RESTORE_Customer: 'RESTORE_Customer',
    UPDATE_Customer: 'UPDATE_Customer',
};

export const CustomerUri = {
    searchCustomer: `${URI}/search`,
    createCustomer: `${URI}`,
    getDetailCustomer: `${URI}/:id`,
    deleteCustomer: `${URI}/delete-multiple`,
    searchCustomerRestores: `${URI}/search-restores`,
    restoreCustomer: `${URI}/restore-multiple`,
    updateCustomer: `${URI}/:id`,
};

export const CustomerApis = {
    searchCustomer(params: SearchCustomer) {
        return http.get<ApiResponseList<SearchCustomerResponse[]>>(
            CustomerUri.searchCustomer,
            {
                params,
            },
        );
    },
    createCustomer: (payload: ICustomer) => {
        return http.post<ApiResponse<ICustomer>>(
            CustomerUri.createCustomer,
            payload,
        );
    },
    getDetailCustomer: (id: string) => {
        return http.get<ApiResponse<ICustomer>>(
            CustomerUri.getDetailCustomer.replace(':id', id),
        );
    },
    deleteCustomer: (payload: DeleteCustomer) => {
        return http.post<ApiResponse<SearchCustomerResponse>>(
            CustomerUri.deleteCustomer,
            payload,
        );
    },
    searchCustomerRestores: (params: SearchRestores) => {
        return http.get<ApiResponseList<ResponseSearchRestores[]>>(
            CustomerUri.searchCustomerRestores,
            {
                params,
            },
        );
    },
    restoreCustomer: (payload: RestoreCustomer) => {
        return http.post<ApiResponse<ResponseSearchRestores>>(
            CustomerUri.restoreCustomer,
            payload,
        );
    },
    updateCustomer: (payload: UpdateCustomerPayload, id: string) => {
        return http.put<ApiResponse<ICustomer>>(
            CustomerUri.updateCustomer.replace(':id', id),
            payload,
        );
    },
};

export const useSearchCustomer = (
    params: SearchCustomer,
    o?: { enabled?: boolean },
) => {
    return useQuery({
        queryKey: [CustomerKey.SEARCH_Customer, params],
        queryFn: () => CustomerApis.searchCustomer(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled: o?.enabled ?? true,
    });
};

export const useCreateCustomer = (props?: {
    onSuccess?: (data: ICustomer, response: ICustomer) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: ICustomer) =>
            CustomerApis.createCustomer(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [CustomerKey.SEARCH_Customer],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useGetDetailCustomer = (id: string) => {
    return useQuery({
        queryKey: [CustomerKey.GET_DETAIL_Customer, id],
        queryFn: () => CustomerApis.getDetailCustomer(id),
        select: (data) => data,
        enabled: !!id,
    });
};

export const useDeleteCustomer = (props?: {
    onSuccess?: (
        data: DeleteCustomer,
        response: ApiResponse<SearchCustomerResponse>,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationKey: [CustomerKey.DELETE_Customer],
        mutationFn: (payload: DeleteCustomer) =>
            CustomerApis.deleteCustomer(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [CustomerKey.SEARCH_Customer],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useSearchRestoreCustomer = (params: SearchRestores) => {
    return useQuery({
        queryKey: [CustomerKey.SEARCH_Restores, params],
        queryFn: () => CustomerApis.searchCustomerRestores(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useRestoreCustomer = (props?: {
    onSuccess?: (
        data: RestoreCustomer,
        response: ApiResponse<ResponseSearchRestores>,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationKey: [CustomerKey.RESTORE_Customer],
        mutationFn: (payload: RestoreCustomer) =>
            CustomerApis.restoreCustomer(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [CustomerKey.SEARCH_Restores],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useUpdateCustomer = (props?: {
    onSuccess?: (
        data: { payload: UpdateCustomerPayload; id: string },
        response: ICustomer,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: ({
            payload,
            id,
        }: {
            payload: UpdateCustomerPayload;
            id: string;
        }) => CustomerApis.updateCustomer(payload, id),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [CustomerKey.SEARCH_Customer],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};
