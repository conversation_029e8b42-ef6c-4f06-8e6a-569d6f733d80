import { SearchCustomerResponse } from '@/apis/customer/customer.type';
import { LeadStatus } from '@/constants/sharedData/sharedData';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Button } from 'reactstrap';
import AddCustomerModal from '../Modal/AddCustomerModal';

interface DisplayCustomer {
    id: string;
    name: string;
    businessTypeName: string;
    industryName: string;
    lifecycleStageEnum: string;
}

interface CustomerProps {
    title: string;
}
const ListCustomer = ({ title }: CustomerProps) => {
    const { setValue } = useFormContext();

    const [displayedCustomers, setDisplayedCustomers] = useState<
        DisplayCustomer[]
    >([]);
    const [isModal, setIsModal] = useState(false);
    const toggleModal = () => setIsModal((prev) => !prev);

    const handleRemoveCustomer = (customerId: string) => {
        const updatedCustomers = displayedCustomers.filter(
            (c) => c.id !== customerId,
        );
        setDisplayedCustomers(updatedCustomers);
        const companyIds = updatedCustomers.map((c) => c.id);
        setValue('companyIds', companyIds);
    };

    return (
        <div className='mt-4'>
            <div className='d-flex justify-content-between align-items-center mb-3'>
                <h6 className='mb-0'>{title}</h6>
                <Button
                    color='light'
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={toggleModal}
                >
                    <i className='ri-add-line me-1'></i> Thêm
                </Button>
            </div>
            <table className='table table-bordered'>
                <thead>
                    <tr>
                        <th style={{ width: '100px' }}>Dòng</th>
                        <th>Tên khách hàng</th>
                        <th>Loại hình</th>
                        <th>Lĩnh vực</th>
                        <th>Giai đoạn</th>
                        <th style={{ width: '60px' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {displayedCustomers.length > 0 ? (
                        displayedCustomers.map((customer, index) => (
                            <tr key={customer.id}>
                                <td>{index + 1}</td>
                                <td>{customer.name || ''}</td>
                                <td>{customer.businessTypeName || ''}</td>
                                <td>{customer.industryName || ''}</td>
                                <td>{customer.lifecycleStageEnum || ''}</td>
                                <td>
                                    <button
                                        type='button'
                                        className='btn btn-sm btn-danger'
                                        onClick={() =>
                                            handleRemoveCustomer(customer.id)
                                        }
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </button>
                                </td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan={6} className='text-center py-3'>
                                Chưa có thông tin
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
            <AddCustomerModal
                isOpen={isModal}
                toggle={toggleModal}
                onSuccess={(selectedCustomers: SearchCustomerResponse[]) => {
                    const newCustomers: DisplayCustomer[] =
                        selectedCustomers.map((customer) => {
                            const status = LeadStatus.find(
                                (s) =>
                                    Number(s.value) ===
                                    Number(customer.leadStatus),
                            );
                            return {
                                id: customer.id,
                                name: customer.name,
                                businessTypeName: customer.businessTypeName,
                                industryName: customer.industryName,
                                lifecycleStageEnum: status?.label || '',
                            };
                        });

                    const updatedCustomers = [...displayedCustomers];

                    newCustomers.forEach((newCustomer) => {
                        if (
                            !updatedCustomers.some(
                                (existing) => existing.id === newCustomer.id,
                            )
                        ) {
                            updatedCustomers.push(newCustomer);
                        }
                    });

                    setDisplayedCustomers(updatedCustomers);
                    const companyIds = updatedCustomers.map((c) => c.id);
                    setValue('companyIds', companyIds, { shouldDirty: true });
                }}
            />
        </div>
    );
};
export default ListCustomer;
