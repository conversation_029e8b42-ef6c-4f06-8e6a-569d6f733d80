import { useSearchDealsByPipelineStageSort } from '@/apis/opportunity/opportunity.api';
import { Option } from '@/types/app.type';
import { useMemo } from 'react';

const useGetOptionDeal = () => {
    const { data } = useSearchDealsByPipelineStageSort({
        Sorts: ['3', '5'],
    });
    const deals = useMemo<Option[]>(() => {
        if (Array.isArray(data?.items) && data?.items.length > 0) {
            return data?.items.map((parent) => ({
                label: parent.title,
                value: parent.dealId,
            }));
        }
        return [];
    }, [data]);
    return deals;
};
export default useGetOptionDeal;
