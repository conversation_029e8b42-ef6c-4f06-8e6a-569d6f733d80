'use client';

import { ISupplier } from '@/apis/supplier/supplier.type';
import FormSuppliers from '../_components/FormSuppliers';
import { useRouter } from 'next/navigation';
import { useCreateSupplier } from '@/apis/supplier/supplier.api';
import { toast } from 'react-toastify';
import { ROUTES } from '@/lib/routes';

const CreatSuppliers = () => {
    const router = useRouter();
    const { mutate: createSuppliers } = useCreateSupplier({
        onSuccess: () => {
            toast.success('Tạo mới nhà cung cấp thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: () => {
            toast.error('Tạo mới nhà cung cấp thất bại');
        },
    });
    const handleSubmit = (data: ISupplier) => {
        createSuppliers(data);
    };
    const handleClose = () => {
        router.back();
    };
    return (
        <FormSuppliers
            page='tao-moi'
            onSubmit={handleSubmit}
            onClose={handleClose}
        />
    );
};
export default CreatSuppliers;
