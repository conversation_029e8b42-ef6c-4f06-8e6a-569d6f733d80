export interface ITask {
    id: string;
    type?: string; // Loại nhóm công việc chính
    name?: string;
    priority?: '<PERSON>' | 'Trung bình' | 'Thấp';
    status?: 'Chờ xử lý' | '<PERSON><PERSON> thực hiện';
    handler?: string;
    deadline?: string;
    subRows?: ITask[];
}

export const listTask: ITask[] = [
    {
        id: '1',
        type: 'Triển khai TCBS',
        subRows: [
            {
                id: '1-0',
                name: 'TPOC với khách hàng',
                priority: '<PERSON>',
                status: 'Chờ xử lý',
                handler: 'ĐH',
                deadline: '09-02-2025',
            },
            {
                id: '1-1',
                name: 'Tạo POC Plan cho TCBS',
                priority: 'Thấp',
                deadline: '09-02-2025',
                status: '<PERSON>ang thực hiện',
                handler: 'ĐH',
            },
            {
                id: '1-2',
                name: '<PERSON><PERSON><PERSON> đặt môi trường UAT',
                priority: 'Trung bình',
                status: '<PERSON><PERSON> thực hiện',
                handler: 'ĐH',
                deadline: '09-08-2025',
            },
        ],
    },
    {
        id: '2',
        type: 'Triển khai SHB',
        subRows: [
            {
                id: '2-0',
                name: 'Viết Test Plan',
                priority: 'Trung bình',
                status: 'Chờ xử lý',
                handler: 'ĐH',
                deadline: '10-02-2025',
            },
            {
                id: '2-1',
                name: 'Testing',
                priority: 'Trung bình',
                status: 'Đang thực hiện',
                handler: 'ĐH',
                deadline: '10-02-2025',
            },
        ],
    },
];
