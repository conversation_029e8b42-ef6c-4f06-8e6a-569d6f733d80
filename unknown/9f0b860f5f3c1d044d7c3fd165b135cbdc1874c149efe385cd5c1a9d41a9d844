import React from 'react';
import { Table } from 'reactstrap';

interface Event {
    id: number;
    date: string;
    title: string;
    type: string;
    time?: string;
}

interface DayViewProps {
    currentDate: Date;
    events: Event[];
}

const DayView: React.FC<DayViewProps> = ({ currentDate, events }) => {
    const getEventsForDay = () => {
        const day = currentDate.getDate().toString().padStart(2, '0');
        const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
        const year = currentDate.getFullYear();
        const dateString = `${day}-${month}-${year}`;

        return events.filter((event) => event.date === dateString);
    };

    const dayEvents = getEventsForDay();

    const hours = Array.from({ length: 24 }, (_, i) => i);

    // const getEventForHour = (hour: number) => {
    //     return dayEvents.find((event) => {
    //         if (!event.time) return false;
    //         const [eventHour, eventMinute] = event.time.split(':').map(Number);
    //         return eventHour === hour;
    //     });
    // };

    // // Tạo dữ liệu hiển thị cho sự kiện
    // const getEventDisplay = (event: Event) => {
    //     if (!event.time) return null;

    //     const [startHour, startMinute] = event.time.split(':').map(Number);
    //     // Giả sử mỗi sự kiện kéo dài 1 giờ
    //     const endHour = startHour + 1;
    //     const endMinute = startMinute;

    //     return {
    //         title: event.title,
    //         timeRange: `${startHour}:${startMinute.toString().padStart(2, '0')} - ${endHour}:${endMinute.toString().padStart(2, '0')}`,
    //         type: event.type,
    //     };
    // };

    return (
        <div
            className='day-view-container'
            style={{
                position: 'relative',
                height: '1200px',
                overflowY: 'auto',
            }}
        >
            <Table bordered responsive className='mb-0'>
                <tbody>
                    {hours.map((hour) => (
                        <tr key={`hour-${hour}`} style={{ height: '50px' }}>
                            <td
                                style={{
                                    width: '80px',
                                    textAlign: 'right',
                                    padding: '8px',
                                    borderRight: '1px solid #dee2e6',
                                }}
                            >
                                {hour} giờ
                            </td>
                            <td style={{ position: 'relative', padding: 0 }}>
                                {/* Vạch kẻ ngang cho mỗi giờ */}
                                <div
                                    style={{
                                        position: 'absolute',
                                        left: 0,
                                        right: 0,
                                        borderBottom: '1px dashed #dee2e6',
                                        top: '50%',
                                    }}
                                ></div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </Table>

            {dayEvents.map((event) => {
                if (!event.time) {
                    return null;
                }

                const [eventHour, eventMinute] = event.time
                    .split(':')
                    .map(Number);
                const topPosition = eventHour * 50 + (eventMinute / 60) * 50;
                const duration = 60;
                const height = (duration / 60) * 50;

                let backgroundColor = '#0d6efd';
                if (event.type === 'meeting') {
                    backgroundColor = '#0d6efd';
                } else if (event.type === 'report') {
                    backgroundColor = '#198754';
                } else if (event.type === 'client') {
                    backgroundColor = '#0dcaf0';
                }

                return (
                    <div
                        key={event.id}
                        style={{
                            position: 'absolute',
                            top: `${topPosition}px`,
                            left: '80px',
                            right: '0',
                            height: `${height}px`,
                            backgroundColor: backgroundColor,
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            zIndex: 10,
                            overflow: 'hidden',
                            margin: '0 4px',
                        }}
                    >
                        <div style={{ fontWeight: 'bold' }}>{event.title}</div>
                        <div style={{ fontSize: '0.8rem' }}>
                            {eventHour}:
                            {eventMinute.toString().padStart(2, '0')} -{' '}
                            {eventHour + 1}:
                            {eventMinute.toString().padStart(2, '0')}
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default DayView;
