export interface Event {
    id: number;
    date: string;
    title: string;
    type: string;
    time?: string;
}

export interface TodayEvent {
    id: number;
    time: string;
    timeRange: string;
    title: string;
    description: string;
}

export interface DayInfo {
    day: number;
    month: number;
    year: number;
    isCurrentMonth: boolean;
}

export interface WeekDay {
    date: Date;
    day: number;
    month: number;
    year: number;
    dayOfWeek: number;
}

// Dữ liệu mẫu cho lịch
export const events: Event[] = [
    {
        id: 1,
        date: '01-03-2023',
        title: 'Tham gia hội thảo A',
        type: 'meeting',
    },
    { id: 2, date: '06-03-2023', title: '<PERSON><PERSON><PERSON> cáo định kỳ', type: 'report' },
    {
        id: 3,
        date: '10-03-2023',
        title: 'Gặp gỡ khách hàng',
        type: 'client',
    },
    { id: 4, date: '14-03-2023', title: 'Họp bàn giao', type: 'meeting' },
    {
        id: 5,
        date: '21-03-2023',
        title: '<PERSON><PERSON><PERSON> lãnh đạo',
        type: 'meeting',
        time: '8:00',
    },
    {
        id: 6,
        date: '24-03-2023',
        title: 'Gặp gỡ khách hàng',
        type: 'client',
    },
    {
        id: 7,
        date: '16-03-2023',
        title: 'Cuộc Họp Khẩn',
        type: 'meeting',
        time: '9:00',
    },
    // Thêm sự kiện cho ngày hiện tại để test chế độ xem ngày
    {
        id: 8,
        date: new Date()
            .toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
            })
            .split('/')
            .join('-'),
        title: 'Họp giao ban',
        type: 'meeting',
        time: '9:00',
    },
    {
        id: 9,
        date: new Date()
            .toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
            })
            .split('/')
            .join('-'),
        title: 'Gặp khách hàng',
        type: 'client',
        time: '13:45',
    },
];

export const todayEvents: TodayEvent[] = [
    {
        id: 1,
        time: 'Th 4 21/03',
        timeRange: '8 giờ - 9 giờ',
        title: 'Họp lãnh đạo',
        description:
            'Thảo luận, báo cáo kết quả kinh doanh và hoạt động trong kỳ',
    },
    {
        id: 2,
        time: 'Th 5 21/03',
        timeRange: '9 giờ - 9 giờ',
        title: 'Báo cáo tiến độ dự án CRM',
        description:
            'Các nhà đầu tiền tham khảo, đánh giá rủi ro và đề xuất giải pháp tối ưu',
    },
];

// Hàm tiện ích cho lịch
export const generateCalendarDays = (date: Date): DayInfo[] => {
    const year = date.getFullYear();
    const month = date.getMonth();

    // Ngày đầu tiên của tháng
    const firstDay = new Date(year, month, 1);
    // Ngày cuối cùng của tháng
    const lastDay = new Date(year, month + 1, 0);

    // Lấy thứ của ngày đầu tiên (0 = Chủ nhật, 1 = Thứ hai, ...)
    let firstDayOfWeek = firstDay.getDay();
    // Chuyển đổi từ 0-6 (CN-T7) sang 1-7 (T2-CN)
    firstDayOfWeek = firstDayOfWeek === 0 ? 7 : firstDayOfWeek;

    const daysInMonth = lastDay.getDate();
    const days: DayInfo[] = [];

    // Thêm ngày từ tháng trước
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    for (let i = firstDayOfWeek - 1; i > 0; i--) {
        days.push({
            day: prevMonthLastDay - i + 1,
            month: month - 1,
            year: month === 0 ? year - 1 : year,
            isCurrentMonth: false,
        });
    }

    // Thêm ngày của tháng hiện tại
    for (let i = 1; i <= daysInMonth; i++) {
        days.push({
            day: i,
            month: month,
            year: year,
            isCurrentMonth: true,
        });
    }

    // Thêm ngày của tháng tiếp theo
    const remainingDays = 42 - days.length; // 6 hàng x 7 cột = 42 ô
    for (let i = 1; i <= remainingDays; i++) {
        days.push({
            day: i,
            month: month + 1,
            year: month === 11 ? year + 1 : year,
            isCurrentMonth: false,
        });
    }

    return days;
};

// Tạo mảng ngày cho chế độ xem tuần
export const generateWeekDays = (date: Date): WeekDay[] => {
    const currentDay = date.getDay(); // 0 = CN, 1 = T2, ...
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Tính số ngày cần thêm/bớt để về thứ 2

    const monday = new Date(date);
    monday.setDate(date.getDate() + mondayOffset);

    const weekDays: WeekDay[] = [];
    for (let i = 0; i < 7; i++) {
        const day = new Date(monday);
        day.setDate(monday.getDate() + i);
        weekDays.push({
            date: day,
            day: day.getDate(),
            month: day.getMonth(),
            year: day.getFullYear(),
            dayOfWeek: i + 1, // 1 = T2, 2 = T3, ..., 7 = CN
        });
    }

    return weekDays;
};

// Format hiển thị tháng
export const formatMonth = (date: Date): string => {
    const months = [
        'Tháng 1',
        'Tháng 2',
        'Tháng 3',
        'Tháng 4',
        'Tháng 5',
        'Tháng 6',
        'Tháng 7',
        'Tháng 8',
        'Tháng 9',
        'Tháng 10',
        'Tháng 11',
        'Tháng 12',
    ];
    return `${months[date.getMonth()]}, ${date.getFullYear()}`;
};
