import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ody, <PERSON>, Alert } from 'reactstrap';

interface TodayEvent {
    id: number;
    time: string;
    timeRange: string;
    title: string;
    description: string;
}

interface TodayEventsProps {
    events: TodayEvent[];
}

const TodayEvents: React.FC<TodayEventsProps> = ({ events }) => {
    const today = new Date();
    const formattedDate = `${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

    const hasEvents = events.length > 0;

    return (
        <Card>
            <CardBody>
                <h5 className='mb-3'>Lịch hôm nay ({formattedDate})</h5>

                {/* Thông báo nhắc nhở */}
                <Alert color='info' className='mb-3'>
                    <i className='ri-notification-2-line me-2'></i>
                    Bạn có {events.length} sự kiện hôm nay
                </Alert>

                {hasEvents ? (
                    <>
                        {events.map((event) => (
                            <div key={event.id} className='mb-4'>
                                <div className='d-flex align-items-center mb-2'>
                                    <div
                                        className='me-2'
                                        style={{
                                            width: '10px',
                                            height: '10px',
                                            borderRadius: '50%',
                                            backgroundColor:
                                                event.id === 1
                                                    ? '#0ab39c'
                                                    : '#f06548',
                                        }}
                                    ></div>
                                    <div className='text-muted'>
                                        {event.time}
                                    </div>
                                    <Badge color='light' className='ms-auto'>
                                        {event.timeRange}
                                    </Badge>
                                </div>
                                <h6>{event.title}</h6>
                                <p className='text-muted small mb-0'>
                                    {event.description}
                                </p>
                            </div>
                        ))}
                        <Button color='light' size='sm' className='w-100'>
                            Xem thêm
                        </Button>
                    </>
                ) : (
                    <div className='text-center py-4'>
                        <i className='ri-calendar-todo-line fs-3 text-muted mb-2'></i>
                        <p className='text-muted'>
                            Không có sự kiện nào hôm nay
                        </p>
                    </div>
                )}
            </CardBody>
        </Card>
    );
};

export default TodayEvents;
