import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { convertParamsSerializer } from '@/utils/convert-data';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
    IDealDetail,
    IDealParams,
    IDealResponse,
    IDealsByPipelineStageSortParams,
    IOpportunity,
    IPipelineStagesParams,
    IPipelineStagesResponse,
    ParamsId,
} from './opportunity.type';

const URI = '/api/v1.0/Deals';

export const dealsKey = {
    SEARCH_DEALS: 'SEARCH_DEALS',
    GET_PIPELINE_STAGES: 'GET_PIPELINE_STAGES',
    SEARCH_DEALS_TABLE_VIEW: 'SEARCH_DEALS_TABLE_VIEW',
    DEALS_BY_PIPELINE_STAGE_SORT: 'DEALS_BY_PIPELINE_STAGE_SORT',
    GET_DEAL_DETAIL: 'GET_DEAL_DETAIL',
    SEARCH_DEALS_BY_PIPELINE_STAGE_SORT: 'SEARCH_DEALS_BY_PIPELINE_STAGE_SORT',
    GET_DETAIL_DEAL: 'GET_DETAIL_DEAL',
};

export const dealsUri = {
    searchDeals: `${URI}/search-deals`,
    getPipelineStages: `${URI}/get-pipeline-stages`,
    createDeal: `${URI}`,
    searchDealsTableView: `${URI}/search-deals-table-view`,
    dealsByPipelineStageSort: `${URI}/deals-by-pipeline-stage-sort`,
    getDealDetail: `${URI}/detail/:dealId`,
};

export const dealsApi = {
    searchDeals: (params: IDealParams) => {
        return http.get<ApiResponseList<IDealResponse[]>>(
            dealsUri.searchDeals,
            {
                params,
            },
        );
    },
    getPipelineStages: (params: IPipelineStagesParams) => {
        return http.get<ApiResponseList<IPipelineStagesResponse[]>>(
            dealsUri.getPipelineStages,
            {
                params,
            },
        );
    },
    createDeal(payload: IOpportunity) {
        return http.post<ApiResponse<IOpportunity>>(
            dealsUri.createDeal,
            payload,
        );
    },
    searchDealsTableView: (params: IDealParams) => {
        return http.get<ApiResponseList<IDealResponse[]>>(
            dealsUri.searchDealsTableView,
            {
                params,
            },
        );
    },
    getDealsByPipelineStageSort: (params: IDealsByPipelineStageSortParams) => {
        return http.get<ApiResponseList<IDealResponse[]>>(
            dealsUri.dealsByPipelineStageSort,
            {
                params,
                paramsSerializer: convertParamsSerializer,
            },
        );
    },
    getDealDetail: (params: ParamsId) => {
        return http.get<ApiResponse<IDealDetail>>(
            dealsUri.getDealDetail.replace(':dealId', params.id),
            {
                params,
                paramsSerializer: convertParamsSerializer,
            },
        );
    },
};

export const useGetPipelineStages = (
    params: IPipelineStagesParams,
    o?: { enabled?: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [dealsKey.GET_PIPELINE_STAGES, params],
        queryFn: () => dealsApi.getPipelineStages(params),
        select: (data) => data,
        enabled,
    });
};
export const useSearchDeals = (
    params: IDealParams,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [dealsKey.SEARCH_DEALS, params],
        queryFn: () => dealsApi.searchDeals(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useCreateDeal = (props?: {
    onSuccess?: (data: IOpportunity, response: IOpportunity) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: IOpportunity) => dealsApi.createDeal(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [dealsKey.SEARCH_DEALS],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });

    return mutation;
};

export const useSearchDealsTableView = (
    params: IDealParams,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [dealsKey.SEARCH_DEALS, params],
        queryFn: () => dealsApi.searchDeals(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useGetDealsByPipelineStageSort = (
    params: IDealsByPipelineStageSortParams,
    o?: { enabled?: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [dealsKey.DEALS_BY_PIPELINE_STAGE_SORT, params],
        queryFn: () => dealsApi.getDealsByPipelineStageSort(params),
        select: (data) => data,
        enabled,
    });
};

export const useGetDealDetail = (
    params: ParamsId,
    o?: { enabled?: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [dealsKey.GET_DEAL_DETAIL, params],
        queryFn: () => dealsApi.getDealDetail(params),
        select: (data) => data.data,
        enabled,
    });
};
export const useSearchDealsByPipelineStageSort = (
    params: IDealsByPipelineStageSortParams,
) => {
    return useQuery({
        queryKey: [dealsKey.SEARCH_DEALS_BY_PIPELINE_STAGE_SORT, params],
        queryFn: () => dealsApi.getDealsByPipelineStageSort(params),
        select: (data) => {
            return data;
        },
    });
};
