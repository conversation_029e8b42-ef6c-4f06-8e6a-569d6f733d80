'use client';
import React, { useState, useEffect } from 'react';
import { Card, CardBody, Row, Col, Button } from 'reactstrap';
import CalendarHeader from './components/CalendarHeader';
import MonthView from './components/MonthView';
import WeekView from './components/WeekView';
import DayView from './components/DayView';
import ListView from './components/ListView';
import TodayEvents from './components/TodayEvents';
import CreateEventModal from './components/CreateEventModal';
import {
    events as initialEvents,
    todayEvents,
    generateCalendarDays,
    generateWeekDays,
    formatMonth,
    DayInfo,
    WeekDay,
    Event,
} from './components/CalendarData';

const PersonalCalendar = () => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [viewType, setViewType] = useState('Tháng');
    const [calendarDays, setCalendarDays] = useState<DayInfo[]>([]);
    const [weekDays, setWeekDays] = useState<WeekDay[]>([]);
    const [events, setEvents] = useState<Event[]>(initialEvents);
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

    useEffect(() => {
        setCalendarDays(generateCalendarDays(currentDate));
        setWeekDays(generateWeekDays(currentDate));
    }, [currentDate]);

    const goToPreviousDay = () => {
        const newDate = new Date(currentDate);
        newDate.setDate(currentDate.getDate() - 1);
        setCurrentDate(newDate);
    };

    const goToNextDay = () => {
        const newDate = new Date(currentDate);
        newDate.setDate(currentDate.getDate() + 1);
        setCurrentDate(newDate);
    };

    const goToPreviousWeek = () => {
        const newDate = new Date(currentDate);
        newDate.setDate(currentDate.getDate() - 7);
        setCurrentDate(newDate);
    };

    const goToNextWeek = () => {
        const newDate = new Date(currentDate);
        newDate.setDate(currentDate.getDate() + 7);
        setCurrentDate(newDate);
    };

    const goToPreviousMonth = () => {
        setCurrentDate(
            new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1),
        );
    };

    const goToNextMonth = () => {
        setCurrentDate(
            new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1),
        );
    };

    const formatWeek = () => {
        if (weekDays.length === 0) {
            return '';
        }
        const firstDay = weekDays[0];
        const lastDay = weekDays[6];

        const months = [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng 8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12',
        ];

        if (firstDay.month === lastDay.month) {
            return `Tuần ${firstDay.day} - ${lastDay.day} ${months[firstDay.month]}, ${firstDay.year}`;
        } else if (firstDay.year === lastDay.year) {
            return `Tuần ${firstDay.day} ${months[firstDay.month]} - ${lastDay.day} ${months[lastDay.month]}, ${firstDay.year}`;
        } else {
            return `Tuần ${firstDay.day} ${months[firstDay.month]}, ${firstDay.year} - ${lastDay.day} ${months[lastDay.month]}, ${lastDay.year}`;
        }
    };

    const handlePrevious = () => {
        if (viewType === 'Tháng') {
            goToPreviousMonth();
        } else if (viewType === 'Tuần') {
            goToPreviousWeek();
        } else if (viewType === 'Ngày') {
            goToPreviousDay();
        } else {
        }
    };

    const handleNext = () => {
        if (viewType === 'Tháng') {
            goToNextMonth();
        } else if (viewType === 'Tuần') {
            goToNextWeek();
        } else if (viewType === 'Ngày') {
            goToNextDay();
        } else {
        }
    };

    const toggleCreateModal = () => {
        setIsCreateModalOpen(!isCreateModalOpen);
    };

    const handleCreateEvent = (data: {
        date: string;
        title: string;
        eventType: string;
        startTime: string;
    }) => {
        const [year, month, day] = data.date.split('-');
        const formattedDate = `${day}-${month}-${year}`;

        const newEvent: Event = {
            id: events.length + 1,
            date: formattedDate,
            title: data.title,
            type: data.eventType,
            time: data.startTime,
        };

        setEvents([...events, newEvent]);

        alert('Tạo sự kiện thành công!');
    };

    return (
        <div className='p-2'>
            <div className='d-flex justify-content-between align-items-center mb-4'>
                <h4 className='mb-0'>Lịch cá nhân</h4>
                <Button
                    color='success'
                    onClick={toggleCreateModal}
                    style={{
                        backgroundColor: '#0ab39c',
                        borderColor: '#0ab39c',
                    }}
                >
                    <i className='ri-add-line me-1'></i>
                    Tạo mới
                </Button>
            </div>

            <Row>
                <Col lg={9}>
                    <Card>
                        <CardBody>
                            <CalendarHeader
                                currentDate={currentDate}
                                viewType={viewType}
                                setViewType={setViewType}
                                onPrevious={handlePrevious}
                                onNext={handleNext}
                                formatMonth={formatMonth}
                                formatWeek={formatWeek}
                            />

                            {viewType === 'Tháng' && (
                                <MonthView
                                    calendarDays={calendarDays}
                                    events={events}
                                    onDayClick={(day) => {
                                        if (day) {
                                            setCurrentDate(
                                                new Date(
                                                    day.year,
                                                    day.month,
                                                    day.day,
                                                ),
                                            );
                                            setViewType('Ngày');
                                        }
                                    }}
                                />
                            )}

                            {viewType === 'Tuần' && (
                                <WeekView
                                    weekDays={weekDays}
                                    events={events}
                                    onDayClick={(day) => {
                                        if (day) {
                                            setCurrentDate(
                                                new Date(
                                                    day.year,
                                                    day.month,
                                                    day.day,
                                                ),
                                            );
                                            setViewType('Ngày');
                                        }
                                    }}
                                />
                            )}

                            {viewType === 'Ngày' && (
                                <DayView
                                    currentDate={currentDate}
                                    events={events}
                                />
                            )}

                            {viewType === 'Danh sách' && (
                                <ListView events={events} />
                            )}
                        </CardBody>
                    </Card>
                </Col>

                <Col lg={3}>
                    <TodayEvents events={todayEvents} />

                    <Card className='mt-4'>
                        <CardBody>
                            <h5 className='mb-3'>Thông báo</h5>
                            <div className='notification-item d-flex align-items-start mb-3 pb-3 border-bottom'>
                                <div className='flex-shrink-0'>
                                    <div className='avatar-xs me-3'>
                                        <span className='avatar-title bg-info-subtle text-info rounded-circle fs-16'>
                                            <i className='ri-calendar-check-line'></i>
                                        </span>
                                    </div>
                                </div>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-1'>Nhắc nhở cuộc họp</h6>
                                    <p className='text-muted mb-0'>
                                        Cuộc họp lãnh đạo sẽ diễn ra sau 30 phút
                                    </p>
                                    <small className='text-muted'>
                                        15 phút trước
                                    </small>
                                </div>
                            </div>

                            <div className='notification-item d-flex align-items-start'>
                                <div className='flex-shrink-0'>
                                    <div className='avatar-xs me-3'>
                                        <span className='avatar-title bg-success-subtle text-success rounded-circle fs-16'>
                                            <i className='ri-user-follow-line'></i>
                                        </span>
                                    </div>
                                </div>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-1'>
                                        Thêm người tham gia
                                    </h6>
                                    <p className='text-muted mb-0'>
                                        Nguyễn Văn A đã được thêm vào cuộc họp
                                        Báo cáo tiến độ dự án CRM
                                    </p>
                                    <small className='text-muted'>
                                        1 giờ trước
                                    </small>
                                </div>
                            </div>

                            <div className='text-center mt-3'>
                                <Button
                                    color='light'
                                    size='sm'
                                    className='w-100'
                                >
                                    Xem tất cả
                                </Button>
                            </div>
                        </CardBody>
                    </Card>
                </Col>
            </Row>

            {/* Modal tạo sự kiện */}
            <CreateEventModal
                isOpen={isCreateModalOpen}
                toggle={toggleCreateModal}
                onSubmit={handleCreateEvent}
            />
        </div>
    );
};

export default PersonalCalendar;
