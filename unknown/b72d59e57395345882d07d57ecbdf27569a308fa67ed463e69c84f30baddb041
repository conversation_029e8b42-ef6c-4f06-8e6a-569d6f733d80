import { Avatar, Badge, Tooltip } from '@mantine/core';
import { MRT_ColumnDef } from 'mantine-react-table';
import Image from 'next/image';
import { ITask } from '../_mocks/example';

export const useGetColumn = (): MRT_ColumnDef<ITask>[] => [
    {
        accessorKey: 'type',
        header: 'Loại',
        size: 70,
        Cell: () => (
            <Image
                src='/icons/apps/icon-type-tasks.svg'
                width='16'
                height='16'
                alt=''
            />
        ),
    },
    {
        accessorKey: 'name',
        header: 'Tên công việc',
        size: 250,
    },
    {
        accessorKey: 'opportunity',
        header: 'Cơ hội',
        size: 200,
    },
    {
        accessorKey: 'priority',
        header: 'Độ ưu tiên',
        size: 150,
        Cell: ({ cell }) => {
            if (!cell.getValue()) {
                return null;
            }
            return <Badge color='gray'>{String(cell.getValue())}</Badge>;
        },
    },
    {
        accessorKey: 'status',
        header: 'Trạng thái',
        size: 100,
    },
    {
        accessorKey: 'coordinator',
        header: 'Người tạo',
        size: 100,
        Cell: ({ cell }) => {
            if (!cell.getValue()) {
                return null;
            }

            return (
                <Avatar radius='xl' size='sm'>
                    {String(cell.getValue())}
                </Avatar>
            );
        },
    },
    {
        id: 'reminder',
        header: '',
        size: 100,
        enableResizing: false,
        Cell: () => (
            <Tooltip label='Nhắc nhở'>
                <i className='ri-notification-line' />
            </Tooltip>
        ),
    },
];
