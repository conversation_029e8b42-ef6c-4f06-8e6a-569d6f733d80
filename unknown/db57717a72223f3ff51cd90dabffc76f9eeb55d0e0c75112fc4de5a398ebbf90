import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
    IPartnerDetail,
    IPartnerIds,
    IPartnerParams,
    IPartnerPayload,
    IPartnerResponse,
} from './partners.type';

const URI = '/api/v1.0/Partners';

export const partnersKey = {
    PARTNERS: 'PARTNERS',
    SEARCH_PARTNERS: 'SEARCH_PARTNERS',
    SEARCH_PARTNER: 'SEARCH_PARTNER',
    SEARCH_RESTORE: 'SEARCH_RESTORE',
    DELETE_MULTIPLE: 'GET_DETAIL_CONTACT',
    RESTORE_MULTIPLE: 'RESTORE_MULTIPLE',
};

export const partnersUri = {
    createPartner: `${URI}`,
    searchPartners: `${URI}/search`,
    searchRestorePartners: `${URI}/search-restore`,
    deleteMultiplePartners: `${URI}/delete-multiple`,
    restoreMultiplePartners: `${URI}/restore-multiple`,
    getDetailPartner: `${URI}/:id`,
    updatePartner: `${URI}/:id`,
};

export const partnersApi = {
    searchPartners: (params: IPartnerParams) => {
        return http.get<ApiResponseList<IPartnerResponse[]>>(
            partnersUri.searchPartners,
            {
                params,
            },
        );
    },
    deleteMultiplePartners: (payload: IPartnerIds) => {
        return http.post<ApiResponse<boolean>>(
            partnersUri.deleteMultiplePartners,
            payload,
        );
    },
    createPartner: (payload: IPartnerPayload) => {
        return http.post<ApiResponse<boolean>>(
            partnersUri.createPartner,
            payload,
        );
    },
    getDetailPartner: (id: string, isUpdate?: boolean) => {
        return http.get<IPartnerDetail>(
            partnersUri.getDetailPartner.replace(':id', id),
            {
                params: { isUpdate },
            },
        );
    },
    updatePartner: (payload: IPartnerPayload) => {
        const id = payload.id ?? '';

        return http.put<ApiResponse<boolean>>(
            partnersUri.updatePartner.replace(':id', id),
            payload,
        );
    },
    searchRestorePartners: (params: IPartnerParams) => {
        return http.get<ApiResponseList<IPartnerResponse[]>>(
            partnersUri.searchRestorePartners,
            {
                params,
            },
        );
    },
    restoreMultiplePartners: (payload: IPartnerIds) => {
        return http.post<ApiResponse<boolean>>(
            partnersUri.restoreMultiplePartners,
            payload,
        );
    },
};

export const useSearchPartners = (params: IPartnerParams) => {
    return useQuery({
        queryKey: [partnersKey.SEARCH_PARTNERS, params],
        queryFn: () => partnersApi.searchPartners(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useSearchRestorePartners = (params: IPartnerParams) => {
    return useQuery({
        queryKey: [partnersKey.SEARCH_RESTORE, params],
        queryFn: () => partnersApi.searchRestorePartners(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useDeletePartners = (props?: {
    onSuccess?: (data: ApiResponse<boolean>, variables: IPartnerIds) => void;
    onError?: () => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationKey: [partnersKey.DELETE_MULTIPLE],
        mutationFn: (payload: IPartnerIds) =>
            partnersApi.deleteMultiplePartners(payload),
        onSuccess: (data, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [partnersKey.SEARCH_PARTNERS],
            });
            onSuccess?.(data, variables);
        },
        onError,
    });
};

export const useCreatePartner = (props?: {
    onSuccess?: (
        data: ApiResponse<boolean>,
        variables: IPartnerPayload,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (payload: IPartnerPayload) =>
            partnersApi.createPartner(payload),
        onSuccess: (data, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [partnersKey.SEARCH_PARTNERS],
            });
            onSuccess?.(data, variables);
        },
        onError,
    });
};

export const useGetPartnerDetail = (
    id: string,
    options?: { isUpdate?: boolean },
) => {
    return useQuery({
        queryKey: [partnersKey.SEARCH_PARTNER, id, options?.isUpdate],
        queryFn: () => partnersApi.getDetailPartner(id, options?.isUpdate),
        select: (data) => data,
        enabled: !!id,
    });
};

export const useUpdatePartner = (props?: {
    onSuccess?: (
        data: ApiResponse<boolean>,
        variables: IPartnerPayload,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (payload: IPartnerPayload) =>
            partnersApi.updatePartner(payload),
        onSuccess: (data, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [partnersKey.SEARCH_PARTNERS],
            });
            onSuccess?.(data, variables);
        },
        onError,
    });
};

export const useRestorePartners = (props?: {
    onSuccess?: (data: ApiResponse<boolean>, variables: IPartnerIds) => void;
    onError?: () => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationKey: [partnersKey.RESTORE_MULTIPLE],
        mutationFn: (payload: IPartnerIds) =>
            partnersApi.restoreMultiplePartners(payload),
        onSuccess: (data, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [partnersKey.SEARCH_RESTORE],
            });
            onSuccess?.(data, variables);
        },
        onError,
    });
};
