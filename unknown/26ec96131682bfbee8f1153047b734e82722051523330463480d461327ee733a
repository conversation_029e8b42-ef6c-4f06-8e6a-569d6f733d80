export interface IProvince {
    id: string;
    name: string;
}

export interface GetDistrictsParams {
    provinceId?: string;
    provinceName?: string;
}

export interface IDistricts {
    id: string;
    name: string;
}

export interface GetWardsParams {
    districtId?: string;
    districtName?: string;
}

export interface IWard {
    id: string;
    name: string;
}

export enum TypeAssociatedInfo {
    PHONE = 1,
    EMAIL = 2,
}

export interface CheckAssociatedInfo {
    type: TypeAssociatedInfo;
    value: string;
}

export interface IPosition {
    id: string;
    title: string;
}

export interface IIndustry {
    id: string;
    value: string;
}

export interface IPaymentMethod {
    id: string;
    paymentMethodName: string;
    description: string;
}

export interface IPaymentTerms {
    id: string;
    name: string;
    sortBy: number;
}
export interface IDepartment {
    id: string;
    name: string;
}

export interface IBussinesType {
    id: string;
    name: string;
}

export interface GetIndustries {
    id: string;
}
export interface IIndustries {
    name: string;
    id: string;
    industries: Industries[];
}
export interface Industries {
    id: string;
    name: string;
}
export interface UserCreated {
    fullName: string;
    email: string;
}
