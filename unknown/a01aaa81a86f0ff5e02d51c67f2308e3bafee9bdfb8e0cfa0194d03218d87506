export interface ITask {
    id: string;
    type?: string; // Lo<PERSON>i nhóm công việc chính
    name?: string;
    priority?: '<PERSON>' | 'Trung bình' | 'Thấp';
    status?: 'Chờ xử lý' | '<PERSON>ang thực hiện';
    deadline?: string;
    createdBy?: string;
    subRows?: ITask[];
}

export const listTask: ITask[] = [
    {
        id: '1',
        type: 'Triển khai TCBS',
        subRows: [
            {
                id: '1-0',
                name: 'POC với khách hàng',
                priority: '<PERSON>',
                status: 'Chờ xử lý',
                deadline: '09-02-2025',
                createdBy: 'ĐH',
            },
            {
                id: '1-1',
                name: 'Tạo POC Plan cho TCBS',
                priority: 'Thấp',
                status: 'Đang thực hiện',
                deadline: '09-08-2025',
                createdBy: 'ĐH',
            },
            {
                id: '1-2',
                name: '<PERSON><PERSON><PERSON> đặt môi trường U<PERSON>',
                priority: 'Trung bình',
                status: '<PERSON><PERSON> thực hiện',
                deadline: '29-05-2025',
                createdBy: 'ĐH',
            },
        ],
    },
    {
        id: '2',
        type: 'Triển khai SHB',
        subRows: [
            {
                id: '2-0',
                name: 'POC với khách hàng',
                priority: 'Cao',
                status: 'Chờ xử lý',
                deadline: '10-08-2025',
                createdBy: 'ĐH',
            },
            {
                id: '2-1',
                name: 'Testing',
                priority: 'Trung bình',
                status: 'Đang thực hiện',
                deadline: '10-08-2025',
                createdBy: 'ĐH',
            },
        ],
    },
];
