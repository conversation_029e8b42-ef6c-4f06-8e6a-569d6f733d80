import http, { ApiResponse } from '@/lib/apiBase';
import { useQuery } from '@tanstack/react-query';
import { IUser } from './users.type';

const URI = '/api/v1.0/Users';

export const usersKey = {
    GET_USERS: 'GET_USERS',
};

// /api/v1.0/Setting/districts

export const usersUri = {
    getUsers: `${URI}`,
    getUsersSearch: `${URI}/search`,
};

export const usersApis = {
    getUsers() {
        return http.get<IUser[]>(usersUri.getUsers);
    },
    getUsersSearch() {
        return http.get<ApiResponse<IUser[]>>(`${usersUri.getUsersSearch}`);
    },
};

export const useGetUsers = () => {
    return useQuery({
        queryKey: [usersKey.GET_USERS],
        queryFn: () => usersApis.getUsers(),
        select: (data) => data,
    });
};

export const useGetUsersSearch = ({ name }: { name: string }) => {
    return useQuery({
        queryKey: [usersKey.GET_USERS, name],
        queryFn: () => usersApis.getUsersSearch(),
        select: (data) => data.data,
    });
};
