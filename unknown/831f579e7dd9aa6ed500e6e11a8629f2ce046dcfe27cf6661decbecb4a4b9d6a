import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
    ContractNumber,
    IContract,
    ResponseContract,
    SearchContract,
    StatusContract,
} from './contracts.type';

const URI = '/api/v1.0/Contract';

export const ContractKey = {
    SEARCH_CONTRACT: 'SEARCH_CONTRACT',
    GET_DETAIL_CONTRACT: 'GET_DETAIL_CONTRACT',
    GET_CONTRACT_NUMBER: 'GET_CONTRACT_NUMBER',
};

export const ContractUri = {
    searchContract: `${URI}`,
    createContract: `${URI}`,
    getDetailContract: `${URI}/:id`,
    deleteContract: `${URI}`,
    restoreContract: `${URI}/restore`,
    updateContract: `${URI}/:id`,
    getContractNumber: `${URI}/versions/:contractNumber`,
};

export const ContractApis = {
    searchContract: (params: SearchContract) => {
        return http.get<ApiResponseList<ResponseContract[]>>(
            ContractUri.searchContract,
            {
                params,
            },
        );
    },
    createContract: (payload: IContract) => {
        return http.post<ApiResponse<IContract>>(
            ContractUri.createContract,
            payload,
        );
    },
    getDetailContract: (id: string) => {
        return http.get<ApiResponse<IContract>>(
            ContractUri.getDetailContract.replace(':id', id),
        );
    },
    deleteContract: (ids: string[]) => {
        return http.delete<StatusContract>(ContractUri.deleteContract, {
            data: ids,
        });
    },
    updateContract: (payload: IContract, isMakeACopy?: boolean) => {
        const id = payload.id ?? '';
        const params = isMakeACopy ? { isMakeACopy } : {};
        return http.put<ApiResponse<IContract>>(
            ContractUri.updateContract.replace(':id', id),
            payload,
            { params },
        );
    },
    restoreContract: (payload: string[]) => {
        return http.put<StatusContract>(ContractUri.restoreContract, payload);
    },
    getContractNumber: (contractNumber: string) => {
        return http.get<ContractNumber[]>(
            ContractUri.getContractNumber.replace(
                ':contractNumber',
                contractNumber,
            ),
        );
    },
};

export const useGetContractNumber = (contractNumber: string) => {
    return useQuery({
        queryKey: [ContractKey.GET_CONTRACT_NUMBER, contractNumber],
        queryFn: () => ContractApis.getContractNumber(contractNumber),
        select: (data) => data,
        enabled: !!contractNumber,
    });
};

export const useSearchContract = (params: SearchContract) => {
    return useQuery({
        queryKey: [ContractKey.SEARCH_CONTRACT, params],
        queryFn: () => ContractApis.searchContract(params),
        select: (data) => data,
    });
};

export const useDetailContract = (id: string) => {
    return useQuery({
        queryKey: [ContractKey.GET_DETAIL_CONTRACT],
        queryFn: () => ContractApis.getDetailContract(id),
        select: (data) => data,
    });
};

export const useCreateContract = (props?: {
    onSuccess?: (data: IContract, response: IContract) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: IContract) =>
            ContractApis.createContract(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [ContractKey.SEARCH_CONTRACT],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useDeleteContract = (props?: {
    onSuccess?: (data: { ids: string[] }, response: StatusContract) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (params: { ids: string[] }) =>
            ContractApis.deleteContract(params.ids),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [ContractKey.SEARCH_CONTRACT],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useUpdateContract = (props?: {
    onSuccess?: (data: IContract, response: IContract) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (params: { payload: IContract; isMakeACopy?: boolean }) =>
            ContractApis.updateContract(params.payload, params.isMakeACopy),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [ContractKey.SEARCH_CONTRACT],
            });
            void queryClient.invalidateQueries({
                queryKey: [ContractKey.GET_DETAIL_CONTRACT],
            });
            onSuccess?.(variables.payload, response.data);
        },
        onError,
    });
};

export const useRestoreContract = (props?: {
    onSuccess?: (data: string[], response: StatusContract) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: string[]) =>
            ContractApis.restoreContract(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [ContractKey.SEARCH_CONTRACT],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};
