export interface ITask {
    id: string | number;
    type?: string; // <PERSON><PERSON><PERSON> nhóm công việc chính
    name?: string;
    opportunity?: string;
    priority?: 'Cao' | 'Trung bình' | 'Thấp';
    status?:
        | 'Ch<PERSON> xử lý'
        | '<PERSON><PERSON> thực hiện'
        | '<PERSON>hiệm thu'
        | 'Ch<PERSON> xử lý'
        | 'Tạm hoãn';
    coordinator?: string;
}

export const listTask: ITask[] = [
    {
        id: 1,
        name: '<PERSON><PERSON> vấn sản phẩm cho khách hàng',
        opportunity: 'SI-Tektronix RSA5000',
        priority: '<PERSON>',
        status: 'Chờ xử lý',
        coordinator: 'ĐH',
    },
    {
        id: 2,
        name: 'Gặp khách hàng để giới thiệu và tư vấn',
        opportunity: 'Vinfast - Keysight M9383A',
        priority: 'Thấp',
        status: '<PERSON>ang thực hiện',
        coordinator: 'TN',
    },
    {
        id: 3,
        name: '<PERSON><PERSON><PERSON> hì<PERSON> sản phẩm',
        opportunity: 'CVG - Anritsu MS2830A',
        priority: 'Trung bình',
        status: '<PERSON><PERSON><PERSON><PERSON> thu',
        coordinator: 'TB',
    },
    {
        id: 4,
        name: 'Meeting với khách hàng',
        opportunity: 'Intel - Anritsu MG3710A',
        priority: '<PERSON>',
        status: 'Tạm hoãn',
        coordinator: 'DA',
    },
    {
        id: 5,
        name: 'Cấu hình sản phẩm',
        opportunity: 'Rohde & Schwarz SMW200A',
        priority: 'Thấp',
        status: 'Chờ xử lý',
        coordinator: 'TA',
    },
];
