import { Button, Modal, ModalBody, ModalFooter } from 'reactstrap';

interface DeleteModalProps {
    show: boolean;
    onDeleteClick: () => void;
    onCloseClick: () => void;
    loading?: boolean;
}

const DeleteModal = ({
    show,
    onDeleteClick,
    onCloseClick,
    loading = false,
}: DeleteModalProps) => {
    return (
        <Modal isOpen={show} toggle={onCloseClick} centered>
            <div className='modal-header'>
                <h5 className='modal-title'>Xác nhận xóa</h5>
            </div>
            <ModalBody className='text-center'>
                Bạn có chắc chắn muốn xóa sự kiện này?
            </ModalBody>
            <ModalFooter className='justify-content-center border-top-0'>
                <Button
                    color='secondary'
                    onClick={onCloseClick}
                    style={{
                        backgroundColor: 'white',
                        color: '#7a7a7a',
                        border: '1px solid #7a7a7a',
                        minWidth: '100px',
                    }}
                    disabled={loading}
                >
                    Hủy
                </Button>
                <Button
                    color='danger'
                    onClick={onDeleteClick}
                    style={{
                        border: 'none',
                        minWidth: '100px',
                    }}
                    disabled={loading}
                >
                    Xác nhận
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default DeleteModal;
