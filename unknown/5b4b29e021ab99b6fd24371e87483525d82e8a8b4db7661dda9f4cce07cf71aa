export interface ITask {
    id: string;
    type?: string; // Loại nhóm công việc chính
    name?: string;
    group?: string;
    priority?: 'Cao' | 'Trung bình' | 'Thấp';
    status?: 'Chờ xử lý' | '<PERSON>ang thực hiện';
    createdBy?: string;
    subRows?: ITask[];
}

export const listTask: ITask[] = [
    {
        id: '1',
        type: 'Triển khai TCBS',
        subRows: [
            {
                id: '1-0',
                name: 'POC với khách hàng',
                group: 'Giới thiệu sản phẩm',
                priority: 'Cao',
                status: 'Chờ xử lý',
                createdBy: 'ĐH',
            },
            {
                id: '1-1',
                name: 'Tạo POC Plan cho TCBS',
                group: 'Giới thiệu sản phẩm',
                priority: 'Thấp',
                status: '<PERSON>ang thực hiện',
                createdBy: 'ĐH',
            },
            {
                id: '1-2',
                name: '<PERSON><PERSON>i đặt môi trường UAT',
                group: '<PERSON><PERSON><PERSON> thử phần mềm',
                priority: 'Trung bình',
                status: '<PERSON><PERSON> thực hiện',
                createdBy: 'ĐH',
            },
        ],
    },
    {
        id: '2',
        type: 'Triển khai SHB',
        subRows: [
            {
                id: '2-0',
                name: 'POC với khách hàng',
                group: 'Giới thiệu sản phẩm',
                priority: 'Cao',
                status: 'Chờ xử lý',
                createdBy: 'ĐH',
            },
            {
                id: '2-1',
                name: 'Testing',
                group: 'Kiểm thử phần mềm',
                priority: 'Trung bình',
                status: 'Đang thực hiện',
                createdBy: 'ĐH',
            },
        ],
    },
    {
        id: '3',
        type: 'Triển khai Napas',
        subRows: [
            {
                id: '3-1',
                name: 'Viết tài liệu giới thiệu phần mềm',
                group: 'Giới thiệu sản phẩm',
                priority: 'Cao',
                status: 'Chờ xử lý',
                createdBy: 'ĐH',
            },
        ],
    },
];
