export interface SearchProduct {
    Name?: string;
    UserNameCreated?: string;
    FromDate?: string;
    ToDate?: string;
    Page?: number;
    PageSize?: number;
    IsDeleted?: boolean;
}

export interface ResponseSearchProduct {
    id: string;
    name: string;
    code: string;
    description: string;
    categoryId: string;
    categoryName: string;
    supplierId: string;
    supplierName: string;
    productTypeName: string;
    productVersion: string;
    commonStatus: number;
    status: string;
    userNameCreated: string;
    createdDateTime: string;
    productOptionsCount: number;
    totalItems: number;
    totalPages: number;
    currentPage: number;
}

export interface IProductOption {
    id?: string;
    productId?: string;
    installationType?: number;
    userCount?: number;
    yearsOfUse?: number;
    basePrice?: number;
    installationTypeName?: string;
    productOptionType?: number;
}

export interface IProduct {
    name: string;
    code: string;
    categoryId: string;
    supplierId: string;
    description: string;
    productVersion: string;
    commonStatus: number;
    productOptions: IProductOption[];
    id: string;
    categoryName: string;
    supplierName: string;
    status: string;
    userNameCreated: string;
    createdDateTime: string;
    installationType: number;
}

export interface StatusProduct {
    data: string;
    isError: boolean;
    errorMessage: string;
    status: number;
}
