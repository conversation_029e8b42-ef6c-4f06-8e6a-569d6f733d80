'use client';
import { GetListCustomerGroupsParams } from '@/apis/customer-groups/customer-groups.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import FormController from '@/components/common/FormController';
import TableSkeleton from '@/components/common/TableSkeleton';
import { ROUTES } from '@/lib/routes';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { Button, Card, CardHeader, Col, Container } from 'reactstrap';
import { useGetColumn } from './_hook/useGetColumn';
import { ITask, listTask } from './_mocks/example';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ITask>,
        })),
    {
        ssr: false,
    },
);

const RepetitiveWork = () => {
    const router = useRouter();

    const columns = useGetColumn();

    const methods = useForm<GetListCustomerGroupsParams>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
        },
    });

    const { control, setValue } = methods;

    const [name, pageNumber, pageSize] = useWatch({
        control,
        name: ['Name', 'PageNumber', 'PageSize'],
    });

    const params = {
        pageNumber,
        pageSize,
        name,
    };

    console.error(params);

    const isLoading = false;

    const handleRedirectPageCreate = () => {
        router.push(ROUTES.CRM.CUSTOMER_GROUPS.CREATE);
    };

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        onCreateNew={handleRedirectPageCreate}
                        showImportButton={false}
                        showExportButton={false}
                        showDateFilters={true}
                    />
                </Col>

                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex flex-wrap align-items-center gap-2'>
                                <FormController
                                    controlType='textInput'
                                    name='name'
                                    placeholder='Tìm kiếm theo tên công việc lặp lại...'
                                    style={{ width: '360px' }}
                                />
                                <FormController
                                    controlType='nativeSelect'
                                    name='userProcess'
                                    data={[]}
                                    style={{ width: '150px' }}
                                />
                                <FormController
                                    controlType='nativeSelect'
                                    name='createdBy'
                                    data={[]}
                                    style={{ width: '150px' }}
                                />

                                <div className='d-flex gap-2 ms-auto'>
                                    <Button
                                        outline
                                        className='filter-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-filter-line text-primary'></i>
                                    </Button>
                                    <Button
                                        outline
                                        className='settings-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-settings-2-line text-info'></i>
                                    </Button>
                                </div>
                            </div>
                        </CardHeader>

                        {isLoading ? (
                            <div className='p-4'>
                                <TableSkeleton
                                    rows={10}
                                    columns={columns.length}
                                />
                            </div>
                        ) : (
                            <MantineTable
                                columns={columns}
                                data={listTask}
                                totalItems={listTask.length}
                                onPageChange={(page: number) => {
                                    setValue('PageNumber', page);
                                }}
                                onPageSizeChange={(size: number) => {
                                    setValue('PageSize', size);
                                }}
                            />
                        )}
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};

export default RepetitiveWork;
