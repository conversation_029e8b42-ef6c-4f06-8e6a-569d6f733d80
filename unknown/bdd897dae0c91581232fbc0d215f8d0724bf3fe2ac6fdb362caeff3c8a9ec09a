'use client';
import { GetListCustomerGroupsParams } from '@/apis/customer-groups/customer-groups.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import FormController from '@/components/common/FormController';
import TableSkeleton from '@/components/common/TableSkeleton';
import { ROUTES } from '@/lib/routes';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { But<PERSON>, Card, CardHeader, Col, Container } from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { IReminders, listReminders } from './_mocks/example';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<IReminders>,
        })),
    {
        ssr: false,
    },
);

const Reminders = () => {
    const router = useRouter();

    const columns = useGetColumn({ onSelectedAction: () => {} });

    const methods = useForm<GetListCustomerGroupsParams>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
        },
    });

    const { control, setValue } = methods;

    const [name, pageNumber, pageSize] = useWatch({
        control,
        name: ['Name', 'PageNumber', 'PageSize'],
    });

    const params = {
        pageNumber,
        pageSize,
        name,
    };

    console.error(params);

    const isLoading = false;

    const handleRedirectPageCreate = () => {
        router.push(ROUTES.CRM.CUSTOMER_GROUPS.CREATE);
    };

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        onCreateNew={handleRedirectPageCreate}
                        showImportButton={true}
                        showExportButton={false}
                        showDateFilters={true}
                    />
                </Col>

                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex flex-wrap align-items-center gap-2'>
                                <FormController
                                    controlType='textInput'
                                    name='name'
                                    placeholder='Tìm kiếm theo tên nhắc việc...'
                                    style={{ width: '360px' }}
                                />

                                <div className='d-flex gap-2 ms-auto'>
                                    <Button
                                        outline
                                        className='filter-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-filter-line text-primary'></i>
                                    </Button>
                                    <Button
                                        outline
                                        className='settings-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-settings-2-line text-info'></i>
                                    </Button>
                                </div>
                            </div>
                        </CardHeader>

                        {isLoading ? (
                            <div className='p-4'>
                                <TableSkeleton
                                    rows={10}
                                    columns={columns.length}
                                />
                            </div>
                        ) : (
                            <MantineTable
                                columns={columns}
                                data={listReminders}
                                totalItems={listReminders.length}
                                onPageChange={(page: number) => {
                                    setValue('PageNumber', page);
                                }}
                                onPageSizeChange={(size: number) => {
                                    setValue('PageSize', size);
                                }}
                            />
                        )}
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};

export default Reminders;
