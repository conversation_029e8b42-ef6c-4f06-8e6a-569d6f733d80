import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { Avatar } from '@mantine/core';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ITask } from '../_mocks/example';

export const useGetColumn = (): MRT_ColumnDef<ITask>[] => {
    const actions: DropdownAction<ITask>[] = useMemo(
        () => [
            {
                icon: 'ri-function-line',
                label: 'Hoạt động',
                onClick: (data) => console.error(data),
            },
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) => console.error(data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => console.error(data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => console.error(data),
                className: 'text-danger',
            },
        ],
        [],
    );

    return [
        {
            accessorKey: 'name',
            header: 'Tên yêu cầu',
            size: 400,
        },
        {
            accessorKey: 'startDate',
            header: 'Ngày bắt đầu',
        },
        {
            accessorKey: 'endDate',
            header: 'Ngày kết thúc',
        },
        {
            accessorKey: 'requester',
            header: 'Người yêu cầu',
            Cell: ({ cell }) => {
                if (!cell.getValue()) {
                    return null;
                }

                return (
                    <Avatar radius='xl' size='sm'>
                        {String(cell.getValue())}
                    </Avatar>
                );
            },
        },
        {
            header: 'Hành động',
            enableResizing: false,
            Cell: ({ row }) => (
                <div
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                >
                    <DropdownActionMenu
                        actions={actions}
                        data={row.original}
                        direction='down'
                        end={false}
                    />
                </div>
            ),
        },
    ];
};
