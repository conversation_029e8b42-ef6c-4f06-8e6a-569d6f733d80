export interface ITask {
    id: number;
    name: string; // Tên công việc
    startDate: string; // <PERSON><PERSON><PERSON> bắt đầu, định dạng 'HH:mm DD-MM-YYYY'
    endDate: string; // Ng<PERSON><PERSON> kết thúc, định dạng 'HH:mm DD-MM-YYYY'
    creator: string; // Ng<PERSON><PERSON><PERSON> tạo (viết tắt)
    handlers: string[]; // Người xử lý (có thể nhiều)
    taskCount: number; // Số công việc
    schedule: string; // Lịch lập
}

export const listTask: ITask[] = [
    {
        id: 1,
        name: '<PERSON>ọ<PERSON> tiến độ dự án',
        startDate: '09:00 09-02-2025',
        endDate: '09:00 10-05-2025',
        creator: '<PERSON><PERSON>',
        handlers: ['ĐH', 'ĐH'],
        taskCount: 3,
        schedule:
            'Lặp lại hàng tuần vào thứ hai lúc 09:00 AM. Tạ<PERSON> công việc trước 10 phút.',
    },
    {
        id: 2,
        name: '<PERSON><PERSON><PERSON> kết thúc <PERSON>',
        startDate: '15:00 09-08-2025',
        endDate: '17:00 09-08-2025',
        creator: 'VH',
        handlers: ['VH', 'VH'],
        taskCount: 8,
        schedule:
            'Lặp lại mỗi 2 tuần vào thứ sáu lúc 04:00 PM. Tạo trước 30 phút.',
    },
];
