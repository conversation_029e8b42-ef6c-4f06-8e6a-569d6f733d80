import FormController from '@/components/common/FormController';
import { ActionIcon } from '@mantine/core';
import { IconCalendar, IconClock } from '@tabler/icons-react';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import React, { useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
    Button,
    Col,
    FormGroup,
    Label,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Row,
} from 'reactstrap';

interface CreateEventModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSubmit: (data: {
        title: string;
        eventType: string;
        date: string;
        startTime: string;
        endTime: string;
        description: string;
        organizer: string;
        participants: string[];
        format: string;
        location: string;
        notification: string;
    }) => void;
}

const CreateEventModal: React.FC<CreateEventModalProps> = ({
    isOpen,
    toggle,
    onSubmit,
}) => {
    const methods = useForm();

    const refStart = useRef<HTMLInputElement>(null);
    const refEnd = useRef<HTMLInputElement>(null);

    const [formData, setFormData] = useState({
        title: '',
        eventType: '',
        date: '',
        startTime: '',
        endTime: '',
        description: '',
        organizer: '',
        participants: [] as string[],
        format: '',
        location: '',
        notification: 'Không thông báo',
    });

    const editor = useEditor({
        extensions: [
            StarterKit,
            Link.configure({
                openOnClick: false,
            }),
            Image.configure({
                inline: true,
                allowBase64: true,
            }),
        ],
        content: formData.description,
        onUpdate: ({ editor }) => {
            setFormData((prev) => ({
                ...prev,
                description: editor.getHTML(),
            }));
        },
    });

    const handleSubmit = () => {
        // Validate form
        const newErrors = {
            title: !formData.title,
            eventType: !formData.eventType,
            date: !formData.date,
            startTime: !formData.startTime,
            endTime: !formData.endTime,
        };

        // Check if there are any errors
        if (Object.values(newErrors).some((error) => error)) {
            return;
        }

        onSubmit(formData);
        resetForm();
        toggle();
    };

    const resetForm = () => {
        setFormData({
            title: '',
            eventType: '',
            date: '',
            startTime: '',
            endTime: '',
            description: '',
            organizer: '',
            participants: [],
            format: '',
            location: '',
            notification: 'Không thông báo',
        });
        editor?.commands.setContent('');
    };

    const MenuBar = () => {
        if (!editor) {
            return null;
        }

        return (
            <div className='editor-menubar d-flex gap-2 p-2'>
                <button
                    type='button'
                    onClick={() => editor.chain().focus().undo().run()}
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-arrow-go-back-line'></i>
                </button>
                <button
                    type='button'
                    onClick={() => editor.chain().focus().redo().run()}
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-arrow-go-forward-line'></i>
                </button>
                <select
                    className='btn btn-light btn-sm px-2 py-1'
                    onChange={() => editor.chain().focus().setParagraph().run()}
                >
                    <option>Paragraph</option>
                </select>
                <button
                    type='button'
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-bold'></i>
                </button>
                <button
                    type='button'
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-italic'></i>
                </button>
                <button
                    type='button'
                    onClick={() => {
                        const url = window.prompt('URL:');
                        if (url) {
                            editor.chain().focus().setLink({ href: url }).run();
                        } else if (editor.isActive('link')) {
                            editor.chain().focus().unsetLink().run();
                        }
                    }}
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-link'></i>
                </button>
                <button
                    type='button'
                    onClick={() => {
                        const url = window.prompt('URL hình ảnh:');
                        if (url) {
                            // Sử dụng insertContent để chèn HTML hình ảnh trực tiếp
                            editor
                                .chain()
                                .focus()
                                .insertContent(
                                    `<img src="${url}" alt="Image" />`,
                                )
                                .run();
                        }
                    }}
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-image-line'></i>
                </button>
                <div className='dropdown'>
                    <button
                        type='button'
                        className='btn btn-light btn-sm px-2 py-1 dropdown-toggle'
                        data-bs-toggle='dropdown'
                    >
                        <i className='ri-table-line'></i>
                    </button>
                </div>
                <div className='dropdown'>
                    <button
                        type='button'
                        className='btn btn-light btn-sm px-2 py-1 dropdown-toggle'
                        data-bs-toggle='dropdown'
                    >
                        <i className='ri-double-quotes-l'></i>
                    </button>
                </div>
                <div className='dropdown'>
                    <button
                        type='button'
                        className='btn btn-light btn-sm px-2 py-1 dropdown-toggle'
                        data-bs-toggle='dropdown'
                    >
                        <i className='ri-video-line'></i>
                    </button>
                </div>
                <button
                    type='button'
                    onClick={() =>
                        editor.chain().focus().toggleBulletList().run()
                    }
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-list-unordered'></i>
                </button>
                <button
                    type='button'
                    onClick={() =>
                        editor.chain().focus().toggleOrderedList().run()
                    }
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-list-ordered'></i>
                </button>
                <button
                    type='button'
                    onClick={() =>
                        editor.chain().focus().toggleBulletList().run()
                    }
                    className='btn btn-light btn-sm px-2 py-1'
                >
                    <i className='ri-list-check'></i>
                </button>
            </div>
        );
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='lg'>
            <ModalHeader toggle={toggle}>Tạo sự kiện mới</ModalHeader>
            <ModalBody>
                <FormProvider {...methods}>
                    <Row className='g-3'>
                        <Col md={6}>
                            <FormController
                                label='Tên sự kiện'
                                controlType='textInput'
                                name='eventTitle'
                                placeholder='Nhập tên đơn vị'
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Kiểu sự kiện'
                                controlType='nativeSelect'
                                name='eventType'
                                data={['Cuộc họp', 'Báo cáo', 'Khách hàng']}
                                defaultValue=''
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Ngày bắt đầu'
                                controlType='datePickerInput'
                                name='eventDate'
                                valueFormat='DD/MM/YYYY'
                                leftSection={
                                    <IconCalendar size={18} stroke={1.5} />
                                }
                                leftSectionPointerEvents='none'
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Giờ bắt đầu'
                                controlType='timeInput'
                                name='startTime'
                                // @ts-expect-error: chưa khởi tạo props ref
                                ref={refStart}
                                rightSection={
                                    <ActionIcon
                                        variant='subtle'
                                        color='gray'
                                        onClick={() =>
                                            refStart.current?.showPicker()
                                        }
                                    >
                                        <IconClock size={18} stroke={1.5} />
                                    </ActionIcon>
                                }
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Giờ kết thúc'
                                controlType='timeInput'
                                name='endTime'
                                // @ts-expect-error: chưa khởi tạo props ref
                                ref={refEnd}
                                rightSection={
                                    <ActionIcon
                                        variant='subtle'
                                        color='gray'
                                        onClick={() =>
                                            refEnd.current?.showPicker()
                                        }
                                    >
                                        <IconClock size={18} stroke={1.5} />
                                    </ActionIcon>
                                }
                            />
                        </Col>
                        <Col md={12}>
                            <FormGroup>
                                <Label for='description'>
                                    Mô tả chung về sự kiện
                                </Label>
                                <div className='border rounded'>
                                    <MenuBar />
                                    <EditorContent
                                        editor={editor}
                                        className='p-2'
                                        style={{ minHeight: '150px' }}
                                    />
                                </div>
                            </FormGroup>
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Người tổ chức'
                                controlType='nativeSelect'
                                name='organizer'
                                data={['Nguyễn Văn A', 'Trần Thị B']}
                                defaultValue=''
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Người tham gia'
                                controlType='nativeSelect'
                                name='participants'
                                data={['Nguyễn Văn A', 'Trần Thị B']}
                                defaultValue=''
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Hình thức tổ chức'
                                controlType='nativeSelect'
                                name='format'
                                data={['Trực tuyến', 'Trực tiếp', 'Kết hợp']}
                                defaultValue=''
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Địa chỉ'
                                controlType='textInput'
                                name='location'
                                defaultValue=''
                                placeholder='Nhập địa chỉ'
                            />
                        </Col>
                        <Col md={6}>
                            <FormController
                                label='Thông báo'
                                controlType='nativeSelect'
                                name='notification'
                                data={[
                                    'Không thông báo',
                                    '15 phút trước',
                                    '30 phút trước',
                                    '1 giờ trước',
                                ]}
                                defaultValue=''
                            />
                        </Col>
                    </Row>
                </FormProvider>
            </ModalBody>
            <ModalFooter>
                <Button
                    color='secondary'
                    onClick={toggle}
                    style={{
                        backgroundColor: 'white',
                        color: '#dc3545',
                        borderColor: '#dc3545',
                    }}
                >
                    Hủy
                </Button>
                <Button
                    color='primary'
                    onClick={handleSubmit}
                    style={{
                        backgroundColor: '#00d084',
                        borderColor: '#00d084',
                    }}
                >
                    Tạo mới
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default CreateEventModal;
