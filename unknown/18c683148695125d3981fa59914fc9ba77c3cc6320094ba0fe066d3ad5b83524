import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { IReminders } from '../_mocks/example';
import { ACTIONS } from '../_types/action.type';

interface GetColumnProps {
    onSelectedAction: (action: ACTIONS, data: IReminders | undefined) => void;
}

const useGetColumn = ({ onSelectedAction }: GetColumnProps) => {
    const actions: DropdownAction<IReminders>[] = useMemo(
        () => [
            {
                icon: 'ri-function-line',
                label: 'Hoạt động',
                onClick: (data) => onSelectedAction(ACTIONS.ACTIVITY, data),
            },
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) => onSelectedAction(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<IReminders>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên công việc',
                enableHiding: false,
                size: 500,
            },
            {
                accessorKey: 'createdAt',
                header: 'Ngày tạo',
            },
            {
                header: 'Hành động',
                enableResizing: false,
                Cell: ({ row }) => (
                    <div
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    >
                        <DropdownActionMenu
                            actions={actions}
                            data={row.original}
                            direction='down'
                            end={false}
                        />
                    </div>
                ),
            },
        ],
        [actions],
    );

    return columns;
};

export default useGetColumn;
