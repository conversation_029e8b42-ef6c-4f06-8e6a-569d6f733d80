import http from '@/lib/apiBase';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { IAuth, IAuthResponse, IAuthUser } from './auth.type';

const URI = '/api/v1.0/Users';

export const authUri = {
    login: `${URI}/login`,
    logout: `${URI}/logout`,
};

// Tạo axios instance riêng cho auth (không qua proxy)
const authHttp = axios.create({
    baseURL: '',
    headers: {
        'Content-Type': 'application/json',
    },
    timeout: 10000,
});

// Response interceptor cho auth
authHttp.interceptors.response.use(
    (response) => response.data,
    (error) => Promise.reject(error),
);

export const authApis = {
    login: (payload: IAuth) => {
        // Sử dụng Next.js API route thay vì gọi trực tiếp backend
        return authHttp.post<IAuthResponse>(authUri.login, payload);
    },
    logout: () => {
        return authHttp.post(authUri.logout);
    },
    getCurrentUser: () => {
        return http.get<IAuthUser>(`${URI}/me`);
    },
};

export const useLogin = (props?: {
    onSuccess?: (response: IAuthResponse, data: IAuth) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (payload: IAuth) => authApis.login(payload),
        onSuccess: (response, variables) => {
            onSuccess?.(response.data, variables);
        },
        onError,
    });
};

export const useGetCurrentUser = (props?: { enabled: boolean }) => {
    const { enabled } = props ?? {};

    return useQuery({
        queryKey: ['auth', 'currentUser'],
        queryFn: () => authApis.getCurrentUser(),
        staleTime: 0, // Luôn fetch fresh data khi F5
        enabled,
    });
};

export const useLogout = (props?: {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
}) => {
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: () => authApis.logout(),
        onSuccess,
        onError,
    });
};
