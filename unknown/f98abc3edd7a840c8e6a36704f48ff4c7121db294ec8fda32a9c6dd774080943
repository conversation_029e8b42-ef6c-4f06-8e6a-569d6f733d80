import { IContactItem } from '../contact/contact.type';

// Partner
export interface IPartnerParams {
    Name?: string;
    CompanyId?: string;
    FromDate?: string;
    ToDate?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
    SalePersonId?: string;
}

export interface IPartnerResponse {
    id: string;
    name: string;
    taxCode: string;
    salePersonName: string;
    createdDate?: string;
    deletedDate?: string;
    email: string;
    phoneNumber: string;
}

export interface IPartnerBankPayload {
    bank: string;
    bankBranch: string;
    accountNumber: string;
    accountHolderName: string;
    customSwiftCode: string;
    companyId: string | null;
}

export interface IPartnerPayload {
    id?: string;
    name: string;
    addTradePartnerDto: {
        name: string;
        shortName: string;
        email: string;
        phoneNumber: string;
        taxCode: string;
        address: string;
        addBankAccountDtos: IPartnerBankPayload[];
    };
    contactIds: string[];
    contacts?: (Partial<IContactItem> & { id: string })[];
}

export interface IPartnerIds {
    ids: string[];
}

export interface IParterCompany {
    id: string;
    name: string;
    associatedInfos: [
        {
            id: string;
            value: string;
            associatedInfoTypeName: string;
            associatedInfoType: 1;
        },
    ];
}

export interface IPartnerContact {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
    departmentId: string;
    departmentName: string;
    positionId: string;
    positionName: string;
    roleName: string;
}

export interface IPartnerDetail {
    id: string;
    name: string;
    shortName: string;
    code: string;
    email: string;
    phoneNumber: string;
    taxCode: string;
    avatar: string;
    address: string;
    salePersonName: string;
    contacts: IPartnerContact[];
    createdDate?: string;
    companies: IParterCompany[];
}
