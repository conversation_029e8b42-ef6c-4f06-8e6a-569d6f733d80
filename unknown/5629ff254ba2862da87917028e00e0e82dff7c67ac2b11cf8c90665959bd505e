import { Avatar, Badge, Tooltip } from '@mantine/core';
import { MRT_ColumnDef } from 'mantine-react-table';
import { ITask } from '../_mocks/example';

export const useGetColumn = (): MRT_ColumnDef<ITask>[] => [
    {
        accessorKey: 'type',
        header: 'Loại',
        Cell: ({ row }) =>
            row.getCanExpand() ? (
                <span className='ml-2'>{row.original.type}</span>
            ) : null,
    },
    {
        accessorKey: 'name',
        header: 'Tên công việc',
    },
    {
        accessorKey: 'priority',
        header: 'Độ ưu tiên',
        Cell: ({ cell }) => {
            if (!cell.getValue()) {
                return null;
            }
            return <Badge color='gray'>{String(cell.getValue())}</Badge>;
        },
    },
    {
        accessorKey: 'deadline',
        header: 'Hạn xử lý',
        Cell: ({ cell }) => {
            if (!cell.getValue()) {
                return null;
            }
            return <Badge color='gray'>{String(cell.getValue())}</Badge>;
        },
    },
    {
        accessorKey: 'status',
        header: 'Trạng thái',
    },
    {
        accessorKey: 'createdBy',
        header: 'Người tạo',
        Cell: ({ cell }) => {
            if (!cell.getValue()) {
                return null;
            }

            return (
                <Avatar radius='xl' size='sm'>
                    {String(cell.getValue())}
                </Avatar>
            );
        },
    },
    {
        id: 'reminder',
        header: '',
        enableResizing: false,
        Cell: () => (
            <Tooltip label='Nhắc nhở'>
                <i className='ri-notification-line' />
            </Tooltip>
        ),
    },
];
