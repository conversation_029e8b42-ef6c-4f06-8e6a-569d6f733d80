export interface GetCustomerGroupsByNameParams {
    name: string;
}

export interface GetListCustomerGroupsParams {
    Name?: string;
    SalePersonId?: string;
    FromDate?: string;
    ToDate?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
}

export interface ICustomerGroupsShorten {
    id: string;
    name: string;
    avatar: string;
}

export interface Address {
    addressName: string;
    provinceId: string;
    districtId: string;
    wardId: string;
    country: string;
    addressType: number;
    customerGroupsId: string;
}

export interface AssociatedInfo {
    value: string;
    associatedInfoType: number;
}

export interface CustomerGroupsRelation {
    relationId: string;
    jobTitle: string;
}

export interface ICustomerGroups {
    id: string;
    name: string;
    createdOn: string;
    code: string;
    description: string;
    avatar: string;
    salePersonName: string;
    companyIds: CompanIes[];
}
export interface ICustomerGroupsDetail {
    id: string;
    name: string;
    createdOn: string;
    code: string;
    description: string;
    avatar: string;
    salePersonName: string;
    companies: Companies[];
}
export interface Companies {
    businessTypeId: string;
    businessTypeName: string;
    id: string;
    industryId: string;
    industryName: string;
    lifecycleStageEnum: number;
    lifecycleStageName: string;
    name: string;
}
export interface CompanIes {
    id: string;
    name: string;
    businessTypeId: string;
    businessTypeName: string;
    industryId: string;
    industryName: string;
    lifecycleStageEnum: number;
}

export interface PayloadDeleteCompanies {
    ids: string[];
}

export interface CheckTaxParams {
    taxCode: string;
}

export interface GetDetailCustomerGroupsParams {
    id: string;
}

export interface ICustomerGroupsResponse {
    id: string;
    name: string;
    description: string;
    salePerson: string;
    createdOn: string;
    totalItems: number;
    totalPages: number;
    currentPage: number;
}

export interface SearchRestoresCustomerGroups {
    Name?: string;
    FromDate?: string;
    ToDate?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
}

export interface ResponseSearchRestoresCustomerGroups {
    id: string;
    name: string;
    description: string;
    salePerson: string;
    deletedDate: string;
    totalItems: number;
    totalPages: number;
    currentPage: number;
}

export interface PayloadRestoresCustomerGroups {
    ids: string[];
}
