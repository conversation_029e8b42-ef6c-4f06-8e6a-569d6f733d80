export interface ITask {
    id: number;
    name: string; // Tên yêu cầu
    startDate: string; // <PERSON><PERSON><PERSON> bắt đầu, định dạng 'DD-MM-YYYY'
    endDate: string | null; // <PERSON><PERSON><PERSON> kết thúc, hoặc null nếu trống
    requester: string; // Người yêu cầu (viết tắt)
}

export const listTask: ITask[] = [
    {
        id: 1,
        name: 'Trao đổi về cơ hội triển khai cho khách hàng TCBS',
        startDate: '09-02-2025',
        endDate: null,
        requester: 'N',
    },
    {
        id: 2,
        name: '<PERSON>ra<PERSON> đổi về cơ hội triển khai cho khách hàng SHB',
        startDate: '09-08-2025',
        endDate: '09-08-2025',
        requester: 'H',
    },
    {
        id: 3,
        name: 'Tra<PERSON> đổ<PERSON> về cơ hội bàn gi<PERSON><PERSON> ph<PERSON><PERSON> bả<PERSON> mậ<PERSON>',
        startDate: '10-08-2025',
        endDate: '10-08-2025',
        requester: 'N',
    },
    {
        id: 4,
        name: '<PERSON>ra<PERSON> đổi về cơ hội bán phần cứng',
        startDate: '10-08-2025',
        endDate: '10-08-2025',
        requester: 'N',
    },
    {
        id: 5,
        name: 'Trao đổi về cơ hội triển khai cho khách hàng Napas',
        startDate: '10-08-2025',
        endDate: '10-08-2025',
        requester: 'N',
    },
];
