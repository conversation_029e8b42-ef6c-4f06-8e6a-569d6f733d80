import React from 'react';
import { <PERSON><PERSON>, ButtonGroup, Col, Row } from 'reactstrap';

interface CalendarHeaderProps {
    currentDate: Date;
    viewType: string;
    setViewType: (type: string) => void;
    onPrevious: () => void;
    onNext: () => void;
    formatMonth: (date: Date) => string;
    formatWeek: () => string;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({
    currentDate,
    viewType,
    setViewType,
    onPrevious,
    onNext,
    formatMonth,
    formatWeek,
}) => {
    // Format hiển thị ngày
    const formatDay = (date: Date) => {
        const days = [
            'Ch<PERSON> nhật',
            'Thứ hai',
            'Th<PERSON> ba',
            '<PERSON>h<PERSON> tư',
            '<PERSON><PERSON><PERSON> năm',
            'Th<PERSON> sáu',
            'Th<PERSON> bảy',
        ];
        const months = [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            '<PERSON>h<PERSON>g 8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12',
        ];

        return `${days[date.getDay()]}, ${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
    };

    // Hiển thị tiêu đề theo chế độ xem
    const getHeaderTitle = () => {
        if (viewType === 'Tháng') {
            return formatMonth(currentDate);
        } else if (viewType === 'Tuần') {
            return formatWeek();
        } else if (viewType === 'Ngày') {
            return formatDay(currentDate);
        } else {
            return 'Danh sách sự kiện';
        }
    };

    return (
        <Row className='mb-4'>
            <Col md={6}>
                <div className='d-flex align-items-center'>
                    <Button color='light' className='me-2' onClick={onPrevious}>
                        <i className='bx bx-chevron-left'></i>
                    </Button>
                    <h5 className='mb-0'>{getHeaderTitle()}</h5>
                    <Button color='light' className='ms-2' onClick={onNext}>
                        <i className='bx bx-chevron-right'></i>
                    </Button>
                </div>
            </Col>
            <Col md={6} className='text-end'>
                <ButtonGroup>
                    <Button
                        color={viewType === 'Tháng' ? 'primary' : 'light'}
                        onClick={() => setViewType('Tháng')}
                        style={
                            viewType === 'Tháng'
                                ? {
                                      backgroundColor: '#0ab39c',
                                      borderColor: '#0ab39c',
                                  }
                                : {}
                        }
                    >
                        Tháng
                    </Button>
                    <Button
                        color={viewType === 'Tuần' ? 'primary' : 'light'}
                        onClick={() => setViewType('Tuần')}
                        style={
                            viewType === 'Tuần'
                                ? {
                                      backgroundColor: '#0ab39c',
                                      borderColor: '#0ab39c',
                                  }
                                : {}
                        }
                    >
                        Tuần
                    </Button>
                    <Button
                        color={viewType === 'Ngày' ? 'primary' : 'light'}
                        onClick={() => setViewType('Ngày')}
                        style={
                            viewType === 'Ngày'
                                ? {
                                      backgroundColor: '#0ab39c',
                                      borderColor: '#0ab39c',
                                  }
                                : {}
                        }
                    >
                        Ngày
                    </Button>
                    <Button
                        color={viewType === 'Danh sách' ? 'primary' : 'light'}
                        onClick={() => setViewType('Danh sách')}
                        style={
                            viewType === 'Danh sách'
                                ? {
                                      backgroundColor: '#0ab39c',
                                      borderColor: '#0ab39c',
                                  }
                                : {}
                        }
                    >
                        Danh sách
                    </Button>
                </ButtonGroup>
            </Col>
        </Row>
    );
};

export default CalendarHeader;
