import {
    ICyberProduct,
    IOpportunity,
} from '@/apis/opportunity/opportunity.type';
import FormController from '@/components/common/FormController';
import ComboboxSelectProductControl from '@/components/common/FormController/ComboboxSelectProductControl';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Button } from 'reactstrap';
import { InstallationOptions } from '../../../constant';
import { InstallationType } from '../../../types';

interface RowInfoProductProps {
    index: number;
    key: string;
    name: string;
    removeProductInfo: (index: number) => void;
}
const RowInfoProduct = (props: RowInfoProductProps) => {
    const { index, key, name, removeProductInfo } = props;

    const [isEdit, setIsEdit] = useState<boolean>(true);

    const handleToggleEdit = () => {
        setIsEdit(!isEdit);
    };

    return (
        <tr key={`row-product-info-${key}`}>
            <td>
                <div className='mt-1 text-center'>{index + 1}</div>
            </td>
            <td>
                <ComboboxSelectProductControl
                    name={`${name}.${index}.productId`}
                    placeholder='Chọn sản phẩm khách hàng đang quan tâm'
                    style={{ width: '100%' }}
                />
            </td>
            <td>
                <FormController
                    controlType='select'
                    name={`${name}.${index}.installationType`}
                    placeholder='Chọn kiểu cài đặt'
                    data={InstallationOptions}
                    style={{ width: '100%' }}
                />
            </td>
            <td>
                <div className='d-flex gap-2 mt-1'>
                    <button
                        type='button'
                        className='btn btn-sm btn-primary'
                        onClick={handleToggleEdit}
                    >
                        <i className='ri-edit-line'></i>
                    </button>
                    <button
                        type='button'
                        className='btn btn-sm btn-danger'
                        onClick={() => removeProductInfo(index)}
                    >
                        <i className='ri-delete-bin-line'></i>
                    </button>
                </div>
            </td>
        </tr>
    );
};

const TableCompareProduct = () => {
    const methods = useFormContext<IOpportunity>();
    const { control } = methods;

    const {
        fields: productInfos,
        append: appendProductInfo,
        remove: removeProductInfo,
    } = useFieldArray({
        control,
        name: 'customerNeed.cyberProducts',
    });

    const handleAddProductInfo = () => {
        const newProductInfo = {
            productId: '',
            productCompare: '',
            installationType: InstallationType.Cloud,
        };

        appendProductInfo(newProductInfo as ICyberProduct);
    };

    const renderProductInfo = () => (
        <div>
            <div className='d-flex justify-content-between align-items-center my-3'>
                <h6>Thông tin sản phẩm</h6>
                <Button
                    color='light'
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={handleAddProductInfo}
                >
                    <i className='ri-add-line me-1'></i> Thêm sản phẩm
                </Button>
            </div>
            <table
                className='table table-bordered mb-0'
                style={{ tableLayout: 'fixed', width: '100%' }}
            >
                <thead>
                    <tr>
                        <th style={{ width: '100px' }}>Số thứ tự</th>
                        <th style={{ width: '60%' }}>
                            Danh mục sản phẩm của khách hàng
                        </th>
                        <th style={{ width: '30%' }}>Kiểu cài đặt</th>
                        <th style={{ width: '150px' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {productInfos.map((_, index) => (
                        <RowInfoProduct
                            index={index}
                            key={`product-${index}`}
                            name='customerNeed.cyberProducts'
                            removeProductInfo={removeProductInfo}
                        />
                    ))}
                    {productInfos.length === 0 && (
                        <tr>
                            <td colSpan={6} className='text-center py-3'>
                                Chưa có dữ liệu về sản phẩm
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );

    return <div className='mb-4'>{renderProductInfo()}</div>;
};

export default TableCompareProduct;
