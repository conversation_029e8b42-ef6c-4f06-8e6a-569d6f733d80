'use client';

import { IPartnerPayload } from '@/apis/partners/partners.type';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, useForm } from 'react-hook-form';
import { <PERSON><PERSON>, <PERSON> } from 'reactstrap';
import * as yup from 'yup';
import AdditionalInfo from './BankInfo';
import LinkedInfo from './LinkedInfo';
import MainInfo from './MainInfo';

interface PartnerFormProps {
    initValue?: IPartnerPayload;
    onSubmit: (data: IPartnerPayload) => void;
    onCancel: () => void;
    mode?: 'create' | 'edit';
}

const partnerFormSchema: yup.ObjectSchema<IPartnerPayload> = yup
    .object()
    .shape({
        // name: yup.string().required('Tên khách hàng là trường bắt buộc'),
        // businessTypeId: yup.string().required('<PERSON><PERSON><PERSON> hình là trường bắt buộc'),
        // industryId: yup.string().required('<PERSON><PERSON><PERSON> vực là trường bắt buộc'),
        // taxCode: yup.string().matches(/^[0-9]{10,13}$/, 'Mã số thuế không hợp lệ'),
        // website: yup.string().url('Website không hợp lệ'),
        // facebook: yup.string().url('Facebook không hợp lệ'),
        // youtube: yup.string().url('Youtube không hợp lệ'),
        // linkedInPage: yup.string().url('LinkedIn không hợp lệ'),
        // associatedInfoDtos: yup.array().of(
        //     yup.object().shape({
        //         value: yup.string().when('associatedInfoType', {
        //             is: 1, // Email
        //             then: (schema) => schema.email('Email không hợp lệ'),
        //             otherwise: (schema) => schema,
        //         }),
        //     }),
        // ),
    }) as yup.ObjectSchema<IPartnerPayload>;

const CustomerForm = ({ initValue, onSubmit, onCancel }: PartnerFormProps) => {
    const methods = useForm<IPartnerPayload>({
        defaultValues: initValue ?? {},
        resolver: yupResolver(partnerFormSchema),
    });

    const handleFormSubmit = (data: IPartnerPayload) => {
        onSubmit(data);
    };

    return (
        <FormProvider {...methods}>
            <Card
                style={{ padding: '20px 40px 20px 40px' }}
                className='flex flex-column gap-3'
            >
                <MainInfo />
                <LinkedInfo />
                <AdditionalInfo />

                <div className='d-flex justify-content-end mt-4'>
                    <Button
                        color='danger'
                        className='me-2'
                        type='button'
                        onClick={onCancel}
                    >
                        Hủy
                    </Button>
                    <Button
                        color='success'
                        type='button'
                        onClick={methods.handleSubmit(handleFormSubmit)}
                    >
                        Lưu
                    </Button>
                </div>
            </Card>
        </FormProvider>
    );
};

export default CustomerForm;
