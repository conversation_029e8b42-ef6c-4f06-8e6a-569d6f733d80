'use client';

import { useParams } from 'next/navigation';
import FormSuppliers from '../../_components/FormSuppliers';
import { useDetailSupplier } from '@/apis/supplier/supplier.api';
import { Spinner } from 'reactstrap';

const DetailSuppliers = () => {
    const params = useParams();
    const id = params.id as string;
    const { data: dataSuppliers, isLoading } = useDetailSupplier(id);
    if (isLoading) {
        return <Spinner />;
    }
    return <FormSuppliers page='chi-tiet' initValue={dataSuppliers?.data} />;
};
export default DetailSuppliers;
