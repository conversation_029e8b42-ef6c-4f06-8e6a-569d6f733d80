import React from 'react';
import { Col, Row } from 'reactstrap';
import ProfileHeader from './ProfileHeader';
import Field from './Field';

const GeneralInfoTab: React.FC = () => {
    // Dữ liệu mẫu (có thể thay đổi động từ props hoặc state)
    const customerId = 'CN_250212_0001';
    const position = 'Giám đốc';
    const domain = 'Ngân hàng';
    const salesperson = 'Nhân viên Trung Hiếu';
    const email = '<EMAIL>';
    const role = 'Người ra quyết định về ngân sách';
    const createdDate = '12/02/2025';
    const source = 'Nhân viên tìm kiếm';
    const phone = '0245323421';
    const stageBadge = 'Tiềm năng';
    const statusBadge = 'Mới';

    return (
        <div>
            <ProfileHeader />
            <Row>
                <Col xxl={4}>
                    <Field label='MÃ KHÁCH HÀNG CÁ NHÂN' value={customerId} />
                    <Field label='CHỨC DANH' value={position} />
                    <Field label='LĨNH VỰC' value={domain} />
                    <Field label='NHÂN VIÊN KINH DOANH' value={salesperson} />
                </Col>
                <Col xxl={4}>
                    <Field
                        label='EMAIL'
                        value={email}
                        badgeText='+1'
                        type={1}
                    />
                    <Field label='VAI TRÒ' value={role} />
                    <Field label='NGÀY TẠO' value={createdDate} />
                    <Field label='NGUỒN' value={source} />
                </Col>
                <Col xxl={4}>
                    <Field
                        label='SỐ ĐIỆN THOẠI'
                        value={phone}
                        badgeText='+1'
                        type={1}
                    />
                    <Field
                        label='GIAI ĐOẠN'
                        value=''
                        badgeText={stageBadge}
                        type={2}
                    />
                    <Field
                        label='TRẠNG THÁI'
                        value=''
                        badgeText={statusBadge}
                        type={2}
                    />
                </Col>
            </Row>
        </div>
    );
};

export default GeneralInfoTab;
