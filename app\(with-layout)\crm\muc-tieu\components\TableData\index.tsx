'use client';
import { Table } from 'reactstrap';
import { data } from './data';
import { JSX, useState } from 'react';
interface TableDataProps {
    selectedYears: string[];
    time: string;
    objective: string;
}

const TableData = ({ selectedYears, time, objective }: TableDataProps) => {
    const goalData = data;
    const [buttonAdd, setButtonAdd] = useState(true);
    const [yearValue, setYearValue] = useState(false);
    const [monthValues, setMonthValues] = useState<{ [key: string]: string }>(
        {},
    );
    const maxYear =
        selectedYears.length > 0
            ? Math.max(...selectedYears.map(Number))
            : 2019;

    const [editingYear, setEditingYear] = useState<string>(String(maxYear + 1));

    return (
        <>
            <div className='mb-3'>
                <strong>Đơn vị: 1,000,000 (VNĐ)</strong>
            </div>
            <div>
                {objective === 'Phòng sale' ? (
                    <div
                        className='table-responsive'
                        style={{ overflowX: 'auto' }}
                    >
                        <Table
                            bordered
                            hover
                            className='align-middle'
                            style={{
                                tableLayout: 'fixed',
                                width: '100%',
                                minWidth: '900px',
                            }}
                        >
                            <colgroup>
                                <col style={{ width: '100px' }} />
                                {selectedYears.length === 0 && (
                                    <col
                                        style={{ width: 'calc(100% - 150px)' }}
                                    />
                                )}
                                {selectedYears.map((year) => (
                                    <col
                                        key={`col-${year}`}
                                        style={{
                                            width: `calc((100% - 150px) / ${selectedYears.length})`,
                                        }}
                                    />
                                ))}
                            </colgroup>
                            {time === 'Tháng' ? (
                                <>
                                    <thead className='table-light'>
                                        <tr>
                                            <th className='text-center'>
                                                Họ và Tên
                                            </th>
                                            <th className='text-center'>Năm</th>
                                            <th className='text-center'>T1</th>
                                            <th className='text-center'>T2</th>
                                            <th className='text-center'>T3</th>
                                            <th className='text-center'>T4</th>
                                            <th className='text-center'>T5</th>
                                            <th className='text-center'>T6</th>
                                            <th className='text-center'>T7</th>
                                            <th className='text-center'>T8</th>
                                            <th className='text-center'>T9</th>
                                            <th className='text-center'>T10</th>
                                            <th className='text-center'>T11</th>
                                            <th className='text-center'>T12</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {(() => {
                                            const rows: JSX.Element[] = [];
                                            let currentName = '';
                                            let nameRowSpan = 0;

                                            goalData.forEach(
                                                (row, rowIndex) => {
                                                    selectedYears.forEach(
                                                        (year, yearIndex) => {
                                                            const isFirstRow =
                                                                rowIndex ===
                                                                    0 &&
                                                                yearIndex === 0;
                                                            const isNewName =
                                                                row.name !==
                                                                currentName;

                                                            if (isNewName) {
                                                                currentName =
                                                                    row.name;

                                                                nameRowSpan =
                                                                    selectedYears.length;
                                                            }

                                                            rows.push(
                                                                <tr
                                                                    key={`${rowIndex}-${year}`}
                                                                >
                                                                    {(isNewName ||
                                                                        isFirstRow) && (
                                                                        <td
                                                                            className='text-center'
                                                                            rowSpan={
                                                                                nameRowSpan
                                                                            }
                                                                        >
                                                                            {
                                                                                row.name
                                                                            }
                                                                        </td>
                                                                    )}
                                                                    <td className='text-center'>
                                                                        {year}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t1` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t2` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t3` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t4` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t5` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t6` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t7` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t8` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t9` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t10` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t11` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                    <td className='text-center'>
                                                                        {row[
                                                                            `y${year}_t12` as keyof typeof row
                                                                        ] ||
                                                                            '-'}
                                                                    </td>
                                                                </tr>,
                                                            );
                                                        },
                                                    );
                                                },
                                            );

                                            return rows;
                                        })()}
                                    </tbody>
                                </>
                            ) : (
                                <>
                                    <thead className='table-light'>
                                        <tr>
                                            <th className='text-center'>
                                                Họ và Tên
                                            </th>
                                            {selectedYears.length === 0 && (
                                                <th></th>
                                            )}
                                            {selectedYears
                                                .sort()
                                                .map((year) => (
                                                    <th
                                                        key={`${year}-header`}
                                                        scope='col'
                                                        className='text-center'
                                                        colSpan={
                                                            time === 'Quý'
                                                                ? 4
                                                                : time ===
                                                                    '6 tháng'
                                                                  ? 2
                                                                  : 1
                                                        }
                                                    >
                                                        {year}
                                                    </th>
                                                ))}
                                        </tr>
                                        {time === 'Quý' && (
                                            <tr>
                                                <th></th>
                                                {selectedYears
                                                    .sort()
                                                    .flatMap((year) => [
                                                        <th
                                                            key={`${year}-q1`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                                whiteSpace:
                                                                    'nowrap',
                                                            }}
                                                        >
                                                            Quý I
                                                        </th>,
                                                        <th
                                                            key={`${year}-q2`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                                whiteSpace:
                                                                    'nowrap',
                                                            }}
                                                        >
                                                            Quý II
                                                        </th>,
                                                        <th
                                                            key={`${year}-q3`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                                whiteSpace:
                                                                    'nowrap',
                                                            }}
                                                        >
                                                            Quý III
                                                        </th>,
                                                        <th
                                                            key={`${year}-q4`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                                whiteSpace:
                                                                    'nowrap',
                                                            }}
                                                        >
                                                            Quý IV
                                                        </th>,
                                                    ])}
                                            </tr>
                                        )}

                                        {time === '6 tháng' && (
                                            <tr>
                                                <th></th>
                                                {selectedYears
                                                    .sort()
                                                    .flatMap((year) => [
                                                        <th
                                                            key={`${year}-s1`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                                whiteSpace:
                                                                    'nowrap',
                                                            }}
                                                        >
                                                            6 tháng đầu
                                                        </th>,
                                                        <th
                                                            key={`${year}-s2`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                                whiteSpace:
                                                                    'nowrap',
                                                            }}
                                                        >
                                                            6 tháng cuối
                                                        </th>,
                                                    ])}
                                            </tr>
                                        )}
                                        {time === 'Năm' && (
                                            <tr>
                                                <th></th>
                                                {selectedYears
                                                    .sort()
                                                    .map((year) => (
                                                        <th
                                                            key={`${year}-total`}
                                                            className='text-center'
                                                            style={{
                                                                width: '100px',
                                                            }}
                                                        >
                                                            Cả năm
                                                        </th>
                                                    ))}
                                            </tr>
                                        )}
                                    </thead>
                                    <tbody>
                                        {goalData.map((row, index) => (
                                            <tr key={index}>
                                                <td className='text-center'>
                                                    {row.name}
                                                </td>
                                                {time === 'Quý' &&
                                                    selectedYears
                                                        .sort()
                                                        .flatMap((year) => [
                                                            <td
                                                                key={`${year}-q1-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_q1` as keyof typeof row
                                                                ] || '-'}
                                                            </td>,
                                                            <td
                                                                key={`${year}-q2-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_q2` as keyof typeof row
                                                                ] || '-'}
                                                            </td>,
                                                            <td
                                                                key={`${year}-q3-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_q3` as keyof typeof row
                                                                ] || '-'}
                                                            </td>,
                                                            <td
                                                                key={`${year}-q4-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_q4` as keyof typeof row
                                                                ] || '-'}
                                                            </td>,
                                                        ])}

                                                {time === '6 tháng' &&
                                                    selectedYears
                                                        .sort()
                                                        .flatMap((year) => [
                                                            <td
                                                                key={`${year}-s1-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_s1` as keyof typeof row
                                                                ] || '-'}
                                                            </td>,
                                                            <td
                                                                key={`${year}-s2-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_s2` as keyof typeof row
                                                                ] || '-'}
                                                            </td>,
                                                        ])}

                                                {time === 'Năm' &&
                                                    selectedYears
                                                        .sort()
                                                        .map((year) => (
                                                            <td
                                                                key={`${year}-total-data`}
                                                                className='text-center'
                                                                style={{
                                                                    whiteSpace:
                                                                        'nowrap',
                                                                }}
                                                            >
                                                                {row[
                                                                    `y${year}_total` as keyof typeof row
                                                                ] || '-'}
                                                            </td>
                                                        ))}
                                            </tr>
                                        ))}
                                    </tbody>
                                </>
                            )}
                        </Table>
                    </div>
                ) : (
                    <div className='table-responsive'>
                        {yearValue && (
                            <div className='mb-3'>
                                <div className='card'>
                                    <div className='card-header d-flex justify-content-between align-items-center'>
                                        <h5 className='mb-0'>
                                            Nhập năm: {editingYear}
                                        </h5>
                                        <div>
                                            <button
                                                className='btn btn-sm btn-light me-2'
                                                onClick={() => {
                                                    // setEditingYear(null);
                                                    setButtonAdd(true);
                                                    setYearValue(false);
                                                }}
                                            >
                                                <i className='ri-close-line'></i>
                                            </button>
                                            <button
                                                className='btn btn-sm btn-success'
                                                onClick={() => {
                                                    // setEditingYear(null);
                                                    setButtonAdd(true);
                                                    setYearValue(false);
                                                }}
                                            >
                                                <i className='ri-check-line'></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div className='card-body'>
                                        <div className='row'>
                                            <div className='col-12 mb-3'>
                                                <div className='d-flex'>
                                                    <div className='flex-grow-1'>
                                                        <input
                                                            type='text'
                                                            className='form-control'
                                                            placeholder='Nhập năm...'
                                                            value={editingYear}
                                                            onChange={(e) =>
                                                                setEditingYear(
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='col-12'>
                                                <div className='table-responsive'>
                                                    <table className='table table-bordered'>
                                                        <thead>
                                                            <tr>
                                                                {Array.from(
                                                                    {
                                                                        length: 12,
                                                                    },
                                                                    (_, i) =>
                                                                        i + 1,
                                                                ).map(
                                                                    (month) => (
                                                                        <th
                                                                            key={`month-${month}`}
                                                                            className='text-center'
                                                                        >
                                                                            T
                                                                            {
                                                                                month
                                                                            }
                                                                        </th>
                                                                    ),
                                                                )}
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                {Array.from(
                                                                    {
                                                                        length: 12,
                                                                    },
                                                                    (_, i) =>
                                                                        i + 1,
                                                                ).map(
                                                                    (month) => (
                                                                        <td
                                                                            key={`month-value-${month}`}
                                                                            className='text-center'
                                                                        >
                                                                            <input
                                                                                type='text'
                                                                                className='form-control form-control-sm text-center'
                                                                                placeholder='...'
                                                                                value={
                                                                                    monthValues[
                                                                                        `${editingYear}_t${month}`
                                                                                    ] ||
                                                                                    ''
                                                                                }
                                                                                onChange={(
                                                                                    e,
                                                                                ) => {
                                                                                    setMonthValues(
                                                                                        {
                                                                                            ...monthValues,
                                                                                            [`${editingYear}_t${month}`]:
                                                                                                e
                                                                                                    .target
                                                                                                    .value,
                                                                                        },
                                                                                    );
                                                                                }}
                                                                            />
                                                                        </td>
                                                                    ),
                                                                )}
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className='d-flex justify-content-end mb-3'>
                            {buttonAdd === true && (
                                <button
                                    className='btn btn-sm'
                                    style={{
                                        backgroundColor: '#0ab39c',
                                        borderRadius: '50%',
                                        color: 'white',
                                        width: '32px',
                                        height: '32px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        padding: '0',
                                    }}
                                    onClick={() => {
                                        const newYear =
                                            selectedYears.length > 0
                                                ? (
                                                      Math.max(
                                                          ...selectedYears.map(
                                                              (y) =>
                                                                  parseInt(y),
                                                          ),
                                                      ) + 1
                                                  ).toString()
                                                : '2020';
                                        setEditingYear(newYear);
                                        setButtonAdd(false);
                                        setYearValue(true);
                                    }}
                                >
                                    <i className='ri-add-line'></i>
                                </button>
                            )}
                        </div>

                        <table className='table table-bordered'>
                            <thead>
                                <tr>
                                    {selectedYears.sort().map((year) => (
                                        <th
                                            key={year}
                                            className='text-center'
                                            style={{
                                                padding: '15px',
                                            }}
                                        >
                                            {year}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    {selectedYears.sort().map((year) => (
                                        <td
                                            key={`${year}-value`}
                                            className='text-center'
                                            style={{
                                                padding: '15px',
                                                cursor: 'pointer',
                                            }}
                                            onClick={() => {
                                                setEditingYear(year);
                                            }}
                                        >
                                            {Array.from(
                                                { length: 12 },
                                                (_, i) => i + 1,
                                            ).reduce((sum, month) => {
                                                const monthValue =
                                                    monthValues[
                                                        `${year}_t${month}`
                                                    ];
                                                return (
                                                    sum +
                                                    (monthValue
                                                        ? parseFloat(monthValue)
                                                        : 0)
                                                );
                                            }, 0) ||
                                                goalData.reduce((sum, row) => {
                                                    const value =
                                                        row[
                                                            `y${year}_total` as keyof typeof row
                                                        ];
                                                    return (
                                                        sum +
                                                        (typeof value ===
                                                        'number'
                                                            ? value
                                                            : 0)
                                                    );
                                                }, 0)}
                                        </td>
                                    ))}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </>
    );
};

export default TableData;
