'use client';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'reactstrap';
import SelectWord from './components/SelectWord';
import StackedColumn from '@/components/common/Charts/Bar/StackedColumn';
import <PERSON><PERSON><PERSON> from '@/components/common/Charts/Pie/PieChart';
import Doughnut<PERSON>hart from '@/components/common/Charts/Pie/DoughnutChart';
import BarChart from '@/components/common/Charts/Bar/BarChart';
import LinewithDataLabels from '@/components/common/Charts/Line/LinewithDataLabels';
import Stacked from '@/components/common/Charts/Bar/Stacked';
const DashboardTasks = () => {
    return (
        <div>
            <Col lg={12}>
                <SelectWord />
            </Col>
            <Row>
                <Col lg={6}>
                    <Row>
                        <Col lg={12}>
                            <Card>
                                <PieChart
                                    dataColors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger", "--vz-info"]'
                                    data={[
                                        { value: 1048, name: 'Search Engine' },
                                        { value: 735, name: 'Direct' },
                                        { value: 580, name: 'Em<PERSON>' },
                                        { value: 484, name: 'Union Ads' },
                                        { value: 300, name: 'Video Ads' },
                                    ]}
                                />
                            </Card>
                        </Col>
                        <Col lg={6}>
                            <Card>
                                <DoughnutChart
                                    dataColors='[ "--vz-warning", "--vz-danger", "--vz-info"]'
                                    data={[
                                        { value: 1048, name: 'Search Engine' },
                                        { value: 735, name: 'Direct' },
                                    ]}
                                />
                            </Card>
                        </Col>
                        <Col lg={6}>
                            <Card>
                                <DoughnutChart
                                    dataColors='["--vz-primary", "--vz-success"]'
                                    data={[
                                        { value: 1048, name: 'Search Engine' },
                                        { value: 735, name: 'Direct' },
                                    ]}
                                />
                            </Card>
                        </Col>
                    </Row>
                </Col>
                <Col lg={6}>
                    <Card>
                        <StackedColumn
                            dataColors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger"]'
                            series={[
                                {
                                    name: 'Hoàn thành',
                                    data: [44, 55, 41, 67, 22, 43],
                                },
                                {
                                    name: 'Đang thực hiện',
                                    data: [13, 23, 20, 8, 13, 27],
                                },
                                {
                                    name: 'Quá hạn',
                                    data: [11, 17, 15, 15, 21, 14],
                                },
                                {
                                    name: 'Sắp quá hạn',
                                    data: [21, 7, 25, 13, 22, 8],
                                },
                            ]}
                            categories={[
                                'Tháng 1',
                                'Tháng 2',
                                'Tháng 3',
                                'Tháng 4',
                                'Tháng 5',
                                'Tháng 6',
                            ]}
                            height={724}
                        />
                    </Card>
                </Col>
                <Col lg={6}>
                    <Card>
                        <CardHeader>
                            Tình hình thực hiện mục tiêu doanh thu
                        </CardHeader>
                        <BarChart dataColors='["--vz-primary"]' />
                    </Card>
                </Col>
                <Col lg={6}>
                    <Card>
                        <CardHeader>
                            Tình hình thực hiện mục tiêu doanh thu
                        </CardHeader>
                        <BarChart dataColors='["--vz-primary"]' />
                    </Card>
                </Col>
                <Col lg={12}>
                    <Card>
                        <LinewithDataLabels
                            dataColors='["--vz-primary", "--vz-success"]'
                            series={[
                                {
                                    name: 'High - 2023',
                                    data: [28, 29, 33, 36, 32, 32, 33],
                                },
                                {
                                    name: 'Low - 2023',
                                    data: [12, 11, 14, 18, 17, 13, 13],
                                },
                            ]}
                            categories={[
                                'Jan',
                                'Feb',
                                'Mar',
                                'Apr',
                                'May',
                                'Jun',
                                'Jul',
                            ]}
                            title='Monthly Temperature Trends'
                        />
                    </Card>
                </Col>
                <Col lg={12}>
                    <Card>
                        <Stacked
                            dataColors='["--vz-primary", "--vz-success", "--vz-warning", "--vz-danger", "--vz-info"]'
                            series={[
                                {
                                    name: 'Product A',
                                    data: [44, 55, 41, 37, 22],
                                },
                                {
                                    name: 'Product B',
                                    data: [53, 32, 33, 52, 13],
                                },
                            ]}
                            categories={[2019, 2020, 2021, 2022, 2023]}
                            title='Product Sales'
                            horizontal={true}
                        />
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default DashboardTasks;
