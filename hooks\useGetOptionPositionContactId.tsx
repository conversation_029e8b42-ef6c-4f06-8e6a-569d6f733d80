import { useSearchContacts } from '@/apis/contact/contact.api';
import { Option } from '@/types/app.type';
import { useMemo } from 'react';

const useGetOptionPositionContactId = (id: string) => {
    const { data } = useSearchContacts({
        CompanyId: id,
    });

    const contacts = useMemo<Option[]>(() => {
        if (Array.isArray(data?.items) && data?.items.length > 0) {
            const uniquePositions = data.items.reduce(
                (acc, current) => {
                    const existingPosition = acc.find(
                        (item) => item.positionName === current.positionName,
                    );
                    if (!existingPosition) {
                        acc.push(current);
                    }
                    return acc;
                },
                [] as typeof data.items,
            );

            return uniquePositions.map((parent) => ({
                label: parent.positionName,
                value: parent.positionName,
            }));
        }
        return [];
    }, [data]);

    return contacts;
};
export default useGetOptionPositionContactId;
