import FormController from '@/components/common/FormController';
import { FormProvider, useForm } from 'react-hook-form';
import { But<PERSON>, Card, Col, Row } from 'reactstrap';

const Form = () => {
    const methods = useForm<FormData>();
    return (
        <FormProvider {...methods}>
            <Card className='p-4'>
                <Row>
                    <Col md={6}>
                        <FormController
                            controlType='textInput'
                            name='name'
                            placeholder='Nhập tên đơn vị'
                            label='Tên đơn vị'
                            required={true}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='select'
                            name='parent_unit'
                            placeholder='Chọn đơn vị cha'
                            label='Đơn vị cha'
                            required={true}
                            data={[]}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='select'
                            name='organization_level'
                            placeholder='Chọn cấp tổ chức'
                            label='Cấp tổ chức'
                            required={true}
                            data={[]}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='textInput'
                            name='unit_head'
                            placeholder='Nhập tên trưởng đơn vị'
                            label='Trưởng đơn vị'
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='textInput'
                            name='description'
                            placeholder='Nhập mô tả'
                            label='Mô tả'
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='select'
                            name='country'
                            placeholder='Chọn quốc gia'
                            label='Quốc gia'
                            data={[]}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='select'
                            name='province'
                            placeholder='Chọn tỉnh/thành phố'
                            label='Tỉnh/Thành phố'
                            data={[]}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='select'
                            name='district'
                            placeholder='Chọn quận/huyện'
                            label='Quận/Huyện'
                            data={[]}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='select'
                            name='ward'
                            placeholder='Chọn phường/xã'
                            label='Phường/Xã'
                            data={[]}
                        />
                    </Col>
                    <Col md={6}>
                        <FormController
                            controlType='textInput'
                            name='address'
                            placeholder='Nhập địa chỉ'
                            label='Địa chỉ'
                        />
                    </Col>
                </Row>
                <div className='d-flex justify-content-end p-3 gap-2'>
                    <Button
                        color='secondary'
                        outline
                        style={{
                            minWidth: '100px',
                            border: '1px solid #f17055',
                            color: '#f17055',
                        }}
                    >
                        Hủy
                    </Button>
                    <Button
                        color='success'
                        style={{
                            minWidth: '100px',
                            backgroundColor: '#0ab39c',
                            border: 'none',
                        }}
                    >
                        Tạo mới
                    </Button>
                </div>
            </Card>
        </FormProvider>
    );
};
export default Form;
