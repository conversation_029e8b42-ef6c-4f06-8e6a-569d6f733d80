'use client';
import ButtonHeader from '@/components/common/ButtonHeader';
import FormController from '@/components/common/FormController';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import MantineTable from '@/components/common/MantineReactTable';
import { ACTIONS } from '@/types/actions.type';
import { FormProvider, useForm } from 'react-hook-form';
import { Card, CardHeader, Col, Container } from 'reactstrap';
import useGetColumn, { IPositionManagement } from './_hook/useGetColumn';

const data = [
    {
        name: '<PERSON><PERSON><PERSON><PERSON> viên CSKH',
        MoTaChucVu:
            'Chịu trách nhiệm đảm bảo trải niệm khách hàng tốt nhất thông qua việc hỗ trợ, giải đáp thắc mắc',
        status: 'Đang hoạt động',
    },
    {
        name: '<PERSON><PERSON><PERSON> viên hỗ trợ',
        MoTaChucVu: 'Hỗ trợ, giải đáp thắc mắc của khách hàng',
        status: 'Đang hoạt động',
    },
    {
        name: 'Phó phòng',
        MoTaChucVu:
            'Giám sát, hướng dẫn và hỗ trợ nhân viên kinh doanh trong quá trình tiếp cận, đàm phấn với KH',
        status: 'Đang hoạt động',
    },
    {
        name: 'Trưởng phòng',
        MoTaChucVu:
            'QUản lý đội ngũ kinh doanh, xây dựng kế hoạch bán hàng, theo dõi hiệu suất',
        status: 'Đang hoạt động',
    },
    {
        name: 'Nhân viên kho',
        MoTaChucVu: '',
        status: 'Ngừng hoạt động',
    },
];
const PositionManagement = () => {
    const handleSelect = (
        action: ACTIONS,
        selectedData: IPositionManagement | undefined,
    ) => {
        if (!selectedData) {
            return;
        }

        switch (action) {
            case ACTIONS.EDIT: {
                // Handle edit action for selectedData
                break;
            }
            case ACTIONS.DELETE: {
                // Handle delete action for selectedData
                break;
            }
            case ACTIONS.VIEW_DETAIL: {
                // Handle view action for selectedData
                break;
            }
        }
    };

    const column = useGetColumn({ onSelectedAction: handleSelect });

    const methods = useForm({
        defaultValues: {
            pageNumber: 1,
            pageSize: 10,
        },
    });
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        showImportButton={false}
                        showExportButton={false}
                    />
                </Col>
                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex justify-content-between align-items-center'>
                                <div style={{ flex: 1 }}>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên chức vụ...'
                                    />
                                </div>

                                <div>
                                    <FormController
                                        controlType='select'
                                        name='ungDung'
                                        placeholder='Tất cả'
                                        data={[
                                            {
                                                label: 'Đang hoạt động',
                                                value: '1',
                                            },
                                            {
                                                label: 'Ngừng hoạt động',
                                                value: '2',
                                            },
                                        ]}
                                        clearable={true}
                                        style={{ width: '200px' }}
                                    />
                                </div>
                            </div>
                        </CardHeader>
                        {/* {isLoading ? (
                            <div className='p-4'>
                                <TableSkeleton
                                    rows={10}
                                    columns={columns.length}
                                />
                            </div>
                        ) : ( */}
                        <MantineTable
                            columns={column}
                            data={data}
                            totalItems={1}
                            onPageChange={(page: number) => {
                                console.error(page);
                            }}
                            onPageSizeChange={(size: number) => {
                                console.error(size);
                            }}
                        />
                        {/* )} */}
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};

export default PositionManagement;
