import FormController from '@/components/common/FormController';
import { Col, Row } from 'reactstrap';

const TabInformation = () => {
    return (
        <Row className='p-3'>
            <Col md={5}>
                <FormController
                    controlType='textInput'
                    name='name'
                    placeholder='Nhập tên nhóm'
                    label='Tên nhóm người dùng'
                    required={true}
                />
            </Col>
            <Col md={1}></Col>
            <Col md={5}>
                <FormController
                    controlType='textInput'
                    name='moTa'
                    placeholder='Nhập mô tả'
                    label='Mô tả'
                />
            </Col>
            <Col md={1}></Col>
            <Col md={5}>
                <FormController
                    controlType='select'
                    name='loại phần mềm'
                    label='Loại phần mềm'
                    placeholder='Chọn phần mềm'
                    data={[
                        {
                            label: 'CRM',
                            value: '0',
                        },
                        {
                            label: 'Quy trình công việc',
                            value: '1',
                        },
                        {
                            label: 'Quản lý công việc',
                            value: '2',
                        },
                    ]}
                    required={true}
                />
            </Col>
            <Col lg={1}></Col>
            <Col md={5}>
                <FormController
                    controlType='select'
                    name='Nhân bản từ nhóm'
                    label='Nhân bản từ nhóm'
                    placeholder='Chọn nhân bản từ nhóm'
                    data={[
                        {
                            label: 'Nhân viên',
                            value: '0',
                        },
                        {
                            label: 'Quản lý',
                            value: '1',
                        },
                        {
                            label: 'Giám đốc',
                            value: '2',
                        },
                    ]}
                    required={true}
                />
            </Col>
        </Row>
    );
};

export default TabInformation;
