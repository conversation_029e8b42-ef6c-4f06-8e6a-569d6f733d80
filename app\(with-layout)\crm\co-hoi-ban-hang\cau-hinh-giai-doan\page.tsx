'use client';

import { useState } from 'react';
import { Button, Input, Nav, NavItem, NavLink } from 'reactstrap';

interface Stage {
    id: string;
    name: string;
}

interface AutomationRule {
    id: string;
    enabled: boolean;
    title: string;
    targetStage: string;
}

export default function SettingsOpportunity() {
    const [activeTab, setActiveTab] = useState('stages');
    const [stages, setStages] = useState<Stage[]>([
        { id: '1', name: '<PERSON><PERSON> hội mới' },
        { id: '2', name: '<PERSON><PERSON> đánh giá chất lượng' },
        { id: '3', name: '<PERSON><PERSON><PERSON> phán' },
        { id: '4', name: '<PERSON><PERSON> hội thành công' },
        { id: '5', name: '<PERSON><PERSON> hội thất bại' },
    ]);

    const [automationRules, setAutomationRules] = useState<AutomationRule[]>([
        {
            id: '1',
            enabled: true,
            title: 'Tự động đặt trạng thái cơ hội khi liên kết liên hệ với cơ hội',
            targetStage: '<PERSON><PERSON> hội mới',
        },
        {
            id: '2',
            enabled: true,
            title: 'Tự động chuyển trạng thái cơ hội khi một Hoạt động được tạo',
            targetStage: 'Đã đánh giá chất lượng',
        },
        {
            id: '3',
            enabled: true,
            title: 'Tự động chuyển trạng thái cơ hội khi một Hoạt động được Hoàn thành',
            targetStage: 'Đàm phán',
        },
        {
            id: '4',
            enabled: true,
            title: 'Tự động chuyển trạng thái cơ hội khi một Báo giá được Xác nhận',
            targetStage: 'Cơ hội thành công',
        },
        {
            id: '5',
            enabled: true,
            title: 'Tự động chuyển trạng thái cơ hội khi một Báo giá bị Từ chối',
            targetStage: 'Cơ hội thất bại',
        },
    ]);

    const handleMoveStage = (dragIndex: number, dropIndex: number) => {
        const newStages = [...stages];
        const [removed] = newStages.splice(dragIndex, 1);
        newStages.splice(dropIndex, 0, removed);
        setStages(newStages);
    };

    const handleDeleteStage = (stageId: string) => {
        setStages(stages.filter((stage) => stage.id !== stageId));
    };

    const handleToggleRule = (ruleId: string) => {
        setAutomationRules((rules) =>
            rules.map((rule) =>
                rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule,
            ),
        );
    };

    const handleChangeTargetStage = (ruleId: string, stageName: string) => {
        setAutomationRules((rules) =>
            rules.map((rule) =>
                rule.id === ruleId ? { ...rule, targetStage: stageName } : rule,
            ),
        );
    };

    const renderStagesConfig = () => (
        <div className='mb-3'>
            <div
                className='d-flex justify-content-between align-items-center mb-4 pb-2'
                style={{ borderBottom: '2px solid #f3f6f9' }}
            >
                <h6
                    className='mb-0 text-uppercase fw-semibold'
                    style={{ color: '#495057', letterSpacing: '0.5px' }}
                >
                    TÊN GIAI ĐOẠN
                </h6>
                <h6
                    className='mb-0 text-uppercase fw-semibold'
                    style={{ color: '#495057', letterSpacing: '0.5px' }}
                >
                    HÀNH ĐỘNG
                </h6>
            </div>

            <div className='stages-list'>
                {stages.map((stage, index) => (
                    <div
                        key={stage.id}
                        className='stage-item d-flex justify-content-between align-items-center p-3 mb-3 rounded'
                        style={{
                            backgroundColor: '#f8f9fa',
                            cursor: 'move',
                            transition: 'all 0.2s ease',
                            border: '1px solid transparent',
                            boxShadow: '0 1px 2px rgba(56,65,74,0.05)',
                        }}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#ffffff';
                            e.currentTarget.style.borderColor = '#e9ebec';
                            e.currentTarget.style.boxShadow =
                                '0 2px 4px rgba(56,65,74,0.1)';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#f8f9fa';
                            e.currentTarget.style.borderColor = 'transparent';
                            e.currentTarget.style.boxShadow =
                                '0 1px 2px rgba(56,65,74,0.05)';
                        }}
                    >
                        <div className='d-flex align-items-center gap-3'>
                            <i
                                className='ri-drag-move-2-line'
                                style={{ color: '#adb5bd', fontSize: '18px' }}
                            ></i>
                            <span
                                style={{ color: '#212529', fontSize: '14px' }}
                            >
                                {stage.name}
                            </span>
                        </div>
                        <div className='d-flex align-items-center gap-1'>
                            <button
                                className='btn btn-icon rounded-circle'
                                style={{
                                    color: '#878a99',
                                    transition: 'all 0.2s ease',
                                    width: '32px',
                                    height: '32px',
                                    padding: 0,
                                    backgroundColor: 'transparent',
                                }}
                                title='Di chuyển lên'
                                onClick={() =>
                                    index > 0 &&
                                    handleMoveStage(index, index - 1)
                                }
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.color = '#0ab39c';
                                    e.currentTarget.style.backgroundColor =
                                        '#f3f6f9';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.color = '#878a99';
                                    e.currentTarget.style.backgroundColor =
                                        'transparent';
                                }}
                            >
                                <i className='ri-arrow-up-s-line fs-5'></i>
                            </button>
                            <button
                                className='btn btn-icon rounded-circle'
                                style={{
                                    color: '#878a99',
                                    transition: 'all 0.2s ease',
                                    width: '32px',
                                    height: '32px',
                                    padding: 0,
                                    backgroundColor: 'transparent',
                                }}
                                title='Di chuyển xuống'
                                onClick={() =>
                                    index < stages.length - 1 &&
                                    handleMoveStage(index, index + 1)
                                }
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.color = '#0ab39c';
                                    e.currentTarget.style.backgroundColor =
                                        '#f3f6f9';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.color = '#878a99';
                                    e.currentTarget.style.backgroundColor =
                                        'transparent';
                                }}
                            >
                                <i className='ri-arrow-down-s-line fs-5'></i>
                            </button>
                            <button
                                className='btn btn-icon rounded-circle'
                                style={{
                                    color: '#878a99',
                                    transition: 'all 0.2s ease',
                                    width: '32px',
                                    height: '32px',
                                    padding: 0,
                                    backgroundColor: 'transparent',
                                }}
                                title='Xóa'
                                onClick={() => handleDeleteStage(stage.id)}
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.color = '#f06548';
                                    e.currentTarget.style.backgroundColor =
                                        '#fff1f0';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.color = '#878a99';
                                    e.currentTarget.style.backgroundColor =
                                        'transparent';
                                }}
                            >
                                <i className='ri-delete-bin-line fs-5'></i>
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            <button
                className='btn btn-link text-primary p-0 mt-2'
                style={{
                    fontSize: '14px',
                    transition: 'all 0.2s ease',
                    fontWeight: 500,
                }}
                onMouseEnter={(e) => (e.currentTarget.style.opacity = '0.8')}
                onMouseLeave={(e) => (e.currentTarget.style.opacity = '1')}
                onClick={() => {
                    const newStage = {
                        id: Date.now().toString(),
                        name: 'Giai đoạn mới',
                    };
                    setStages([...stages, newStage]);
                }}
            >
                <i className='ri-add-line me-1 align-middle'></i>
                Thêm giai đoạn
            </button>
        </div>
    );

    const renderWorkflowConfig = () => (
        <div className='mb-3'>
            {automationRules.map((rule) => (
                <div
                    key={rule.id}
                    className='automation-rule mb-3 p-3 rounded'
                    style={{
                        backgroundColor: '#f8f9fa',
                        transition: 'all 0.2s ease',
                        border: '1px solid transparent',
                        boxShadow: '0 1px 2px rgba(56,65,74,0.05)',
                    }}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#ffffff';
                        e.currentTarget.style.borderColor = '#e9ebec';
                        e.currentTarget.style.boxShadow =
                            '0 2px 4px rgba(56,65,74,0.1)';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                        e.currentTarget.style.borderColor = 'transparent';
                        e.currentTarget.style.boxShadow =
                            '0 1px 2px rgba(56,65,74,0.05)';
                    }}
                >
                    <div className='d-flex align-items-start gap-3'>
                        <div style={{ marginTop: '2px' }}>
                            <Input
                                type='checkbox'
                                checked={rule.enabled}
                                onChange={() => handleToggleRule(rule.id)}
                                style={{
                                    width: '18px',
                                    height: '18px',
                                    cursor: 'pointer',
                                    borderColor: rule.enabled
                                        ? '#0ab39c'
                                        : '#e9ebec',
                                    backgroundColor: rule.enabled
                                        ? '#0ab39c'
                                        : 'white',
                                    borderRadius: '4px',
                                }}
                            />
                        </div>
                        <div className='flex-grow-1'>
                            <div
                                className='mb-3'
                                style={{
                                    color: rule.enabled ? '#212529' : '#878a99',
                                    fontSize: '14px',
                                    fontWeight: 500,
                                }}
                            >
                                {rule.title}
                            </div>
                            <div className='d-flex align-items-center flex-wrap gap-2'>
                                <span
                                    className='text-muted'
                                    style={{ fontSize: '13px' }}
                                >
                                    Chuyển trạng thái cơ hội thành
                                </span>
                                <Input
                                    type='select'
                                    value={rule.targetStage}
                                    onChange={(e) =>
                                        handleChangeTargetStage(
                                            rule.id,
                                            e.target.value,
                                        )
                                    }
                                    style={{
                                        width: '200px',
                                        backgroundColor: rule.enabled
                                            ? 'white'
                                            : '#f8f9fa',
                                        border: '1px solid #e9ebec',
                                        height: '32px',
                                        borderRadius: '4px',
                                        fontSize: '13px',
                                        cursor: rule.enabled
                                            ? 'pointer'
                                            : 'not-allowed',
                                        color: rule.enabled
                                            ? '#212529'
                                            : '#878a99',
                                        transition: 'all 0.2s ease',
                                    }}
                                    disabled={!rule.enabled}
                                >
                                    {stages.map((stage) => (
                                        <option
                                            key={stage.id}
                                            value={stage.name}
                                        >
                                            {stage.name}
                                        </option>
                                    ))}
                                </Input>
                            </div>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );

    return (
        <div
            className='bg-white rounded-3 p-4'
            style={{ boxShadow: '0 1px 2px rgba(56,65,74,0.15)' }}
        >
            <Nav tabs className='mb-4 border-bottom-0 gap-4'>
                <NavItem>
                    <NavLink
                        className={`cursor-pointer border-0 px-0 ${activeTab === 'stages' ? 'active text-primary' : 'text-muted'}`}
                        onClick={() => setActiveTab('stages')}
                        style={{
                            borderBottom:
                                activeTab === 'stages'
                                    ? '2px solid #0ab39c'
                                    : '2px solid transparent',
                            backgroundColor: 'transparent',
                            transition: 'all 0.2s ease',
                            fontWeight: 500,
                            fontSize: '15px',
                        }}
                    >
                        Cấu hình
                    </NavLink>
                </NavItem>
                <NavItem>
                    <NavLink
                        className={`cursor-pointer border-0 px-0 ${activeTab === 'workflow' ? 'active text-primary' : 'text-muted'}`}
                        onClick={() => setActiveTab('workflow')}
                        style={{
                            borderBottom:
                                activeTab === 'workflow'
                                    ? '2px solid #0ab39c'
                                    : '2px solid transparent',
                            backgroundColor: 'transparent',
                            transition: 'all 0.2s ease',
                            fontWeight: 500,
                            fontSize: '15px',
                        }}
                    >
                        Quy trình tự động
                    </NavLink>
                </NavItem>
            </Nav>

            {activeTab === 'stages'
                ? renderStagesConfig()
                : renderWorkflowConfig()}

            <div
                className='d-flex justify-content-end gap-2 mt-4 pt-3'
                style={{ borderTop: '2px solid #f3f6f9' }}
            >
                <Button
                    color='light'
                    style={{
                        fontSize: '14px',
                        padding: '8px 20px',
                        transition: 'all 0.2s ease',
                        fontWeight: 500,
                    }}
                    onMouseEnter={(e) =>
                        (e.currentTarget.style.backgroundColor = '#f3f6f9')
                    }
                    onMouseLeave={(e) =>
                        (e.currentTarget.style.backgroundColor = '#f8f9fa')
                    }
                >
                    Hủy
                </Button>
                <Button
                    style={{
                        backgroundColor: '#0ab39c',
                        fontSize: '14px',
                        padding: '8px 20px',
                        border: 'none',
                        transition: 'all 0.2s ease',
                        fontWeight: 500,
                    }}
                    onMouseEnter={(e) =>
                        (e.currentTarget.style.backgroundColor = '#099885')
                    }
                    onMouseLeave={(e) =>
                        (e.currentTarget.style.backgroundColor = '#0ab39c')
                    }
                >
                    Lưu
                </Button>
            </div>
        </div>
    );
}
