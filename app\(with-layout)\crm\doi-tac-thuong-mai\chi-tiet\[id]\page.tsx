'use client';
import { ROUTES } from '@/lib/routes';
import { useParams, useRouter } from 'next/navigation';

import {
    useDeletePartners,
    useGetPartnerDetail,
} from '@/apis/partners/partners.api';
import { useState } from 'react';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';
import DeleteConfirmModal from '../../_components/DeleteConfirmModal';
import Evaluate from '../evaluate';
import FileInfo from '../file-info';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';

const DetailPartner = () => {
    const params = useParams();
    const id = params.id as string;
    const { data } = useGetPartnerDetail(id, { isUpdate: false });

    const { companies = [], contacts = [] } = data ?? {};

    const router = useRouter();
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

    const handlePut = () => {
        router.push(ROUTES.CRM.PARTNERS.UPDATE.replace(':id', id));
    };

    const { mutate: deleteContacts, isPending } = useDeletePartners({
        onSuccess: () => {
            toast.success('Xóa đối tác thương mại thành công');
            setIsDeleteModalOpen(false);
            router.push(ROUTES.CRM.PARTNERS.INDEX);
        },
        onError: () => {
            toast.error('Xóa đối tác thương mại thất bại');
        },
    });

    const handleConfirmDelete = () => {
        deleteContacts({ ids: [id] });
    };
    return (
        <div>
            <Row>
                <Col lg={9}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex justify-content-between align-items-center'>
                                    <h5>Thông tin chung</h5>
                                    <div className='d-flex justify-content-center align-items-center gap-2'>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                            }}
                                            onClick={handlePut}
                                        >
                                            <i className=' ri-pencil-line'></i>{' '}
                                            Chỉnh sửa
                                        </Button>
                                        <UncontrolledDropdown>
                                            <DropdownToggle
                                                tag='button'
                                                className='btn'
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    padding: '4px',
                                                    minWidth: '30px',
                                                }}
                                            >
                                                <i
                                                    className='ri-more-fill'
                                                    style={{
                                                        color: 'white',
                                                    }}
                                                ></i>
                                            </DropdownToggle>
                                            <DropdownMenu end>
                                                <DropdownItem>
                                                    <i className='ri-user-received-line me-2'></i>
                                                    Bàn giao cá nhân
                                                </DropdownItem>
                                                <DropdownItem>
                                                    <i className='ri-history-line me-2'></i>
                                                    Nhật ký hoạt động
                                                </DropdownItem>
                                                <DropdownItem
                                                    className='text-danger'
                                                    onClick={() =>
                                                        setIsDeleteModalOpen(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    <i className='ri-delete-bin-line me-2'></i>
                                                    Xóa cá nhân
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </UncontrolledDropdown>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <Col lg={12} className='mb-2'>
                                    <Row>
                                        <Col lg={1}>
                                            <div className='relative inline-block'>
                                                <div
                                                    className='flex items-center justify-center rounded-full font-bold d-flex justify-content-center align-items-center'
                                                    style={{
                                                        width: '60px',
                                                        height: '60px',
                                                        backgroundColor:
                                                            '#daf4f0',
                                                        color: data?.avatar,
                                                        borderRadius: '50%',
                                                        fontSize: '20px',
                                                    }}
                                                >
                                                    {data?.name
                                                        ?.trim()
                                                        .split(' ')
                                                        .filter(Boolean)
                                                        .at(1)
                                                        ?.charAt(0)
                                                        ?.toUpperCase() ||
                                                        data?.name
                                                            ?.trim()
                                                            ?.charAt(0)
                                                            ?.toUpperCase()}
                                                </div>

                                                <span className='absolute right-0 bottom-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white'></span>
                                            </div>
                                        </Col>
                                        <Col
                                            lg={7}
                                            className='d-flex align-items-center'
                                        >
                                            <Label
                                                style={{
                                                    fontSize: '15px',
                                                    marginLeft: '1%',
                                                }}
                                            >
                                                {data?.name}
                                            </Label>
                                        </Col>
                                        <Col lg={4}>
                                            <div
                                                className='d-flex justify-content-end align-items-center gap-2'
                                                style={{ height: '65px' }}
                                            >
                                                <Button
                                                    outline
                                                    className='btn-soft-dark btn-icon'
                                                    size='sm'
                                                >
                                                    <i className='bx bx-note fs-16'></i>
                                                </Button>
                                                <Button
                                                    outline
                                                    className='btn-soft-danger btn-icon'
                                                    size='sm'
                                                >
                                                    <i className='bx bx-envelope fs-16'></i>
                                                </Button>

                                                <Button
                                                    outline
                                                    className='btn-soft-info btn-icon'
                                                    size='sm'
                                                >
                                                    <i className='bx bx-calendar fs-16'></i>
                                                </Button>
                                                <Button
                                                    outline
                                                    className='btn-soft-secondary btn-icon'
                                                    size='sm'
                                                >
                                                    <i className='bx bx-check-square fs-16'></i>
                                                </Button>
                                            </div>
                                        </Col>
                                    </Row>
                                </Col>
                                <Row className='mt-4'>
                                    <Col lg='4'>
                                        <p>
                                            <strong>
                                                MÃ ĐỐI TÁC THƯƠNG MẠI
                                            </strong>
                                            <br />
                                            {data?.id}
                                        </p>
                                        <p>
                                            <strong>SỐ ĐIỆN THOẠI</strong>
                                            <br />
                                            {data?.phoneNumber}
                                        </p>
                                    </Col>

                                    <Col lg='4'>
                                        <p>
                                            <strong>MÃ SỐ THUẾ</strong>
                                            <br />
                                            {data?.taxCode}
                                        </p>

                                        <p>
                                            <strong>NGÀY TẠO</strong>
                                            <br />
                                            <FormattedDateTimeWithFormat
                                                date={data?.createdDate ?? ''}
                                            />
                                        </p>
                                    </Col>

                                    <Col lg='4'>
                                        <p>
                                            <strong>EMAIL</strong>
                                            <br />
                                            {data?.email}
                                        </p>
                                        <p>
                                            <strong>
                                                NHÂN VIÊN KINH DOANH
                                            </strong>
                                            <br />
                                            {data?.salePersonName}
                                        </p>
                                    </Col>

                                    <Col lg='12'>
                                        <p>
                                            <strong>ĐỊA CHỈ</strong>
                                            <br />
                                            {data?.address}
                                        </p>
                                    </Col>
                                </Row>
                            </CardBody>
                        </Card>
                    </Col>
                    <Evaluate />
                </Col>

                <FileInfo companies={companies} contacts={contacts} />
            </Row>
            <DeleteConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                itemName={data?.name ?? ''}
                loading={isPending}
            />
        </div>
    );
};

export default DetailPartner;
