'use client';

import { addressTypes } from '@/constants/sharedData/sharedData';

import { Radio } from '@mantine/core';
import {
    Button,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Row,
    Input,
    Label,
} from 'reactstrap';

interface IOption {
    value: string;
    label: string;
}

interface TempAddress {
    addressName: string;
    provinceId: string;
    districtId: string;
    wardId: string;
    country: string;
}

interface AddressModalProps {
    isOpen: boolean;
    toggle: () => void;
    setAddressType: (type: string) => void;
    handleAddAddress: () => void;
    provinces: IOption[];
    districts: IOption[];
    wards: IOption[];
    tempAddress: TempAddress;
    setTempAddress: (address: TempAddress) => void;
    addressType: string;
}

const AddressModal = ({
    isOpen,
    toggle,
    setAddressType,
    handleAddAddress,
    provinces,
    districts,
    wards,
    tempAddress,
    setTempAddress,
    addressType,
}: AddressModalProps) => {
    const handleRadioChange = (value: string) => {
        setAddressType(value);
    };

    const handleInputChange = (field: keyof TempAddress, value: string) => {
        setTempAddress({
            ...tempAddress,
            [field]: value,
        });
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} centered size='xl'>
            <ModalHeader toggle={toggle}>Thêm địa chỉ</ModalHeader>
            <ModalBody>
                <Row className='g-3'>
                    <Col md='12'>
                        <Radio.Group
                            name='addressType'
                            onChange={handleRadioChange}
                            value={addressType}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    gap: '16px',
                                    marginTop: '8px',
                                }}
                            >
                                {addressTypes.map((type, index) => (
                                    <div
                                        key={`address-type-${index}`}
                                        style={{
                                            flex: 1,
                                            textAlign: 'center',
                                        }}
                                    >
                                        <Radio
                                            value={type.value}
                                            label={type.label}
                                            size='md'
                                            styles={{
                                                root: {
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                },
                                                body: {
                                                    alignItems: 'center',
                                                },
                                                radio: {
                                                    backgroundColor: '#ffffff',
                                                    borderColor:
                                                        addressType ===
                                                        type.value
                                                            ? '#000000'
                                                            : '#d1d5db',
                                                    borderWidth: '2px',
                                                    '&:checked': {
                                                        backgroundColor:
                                                            '#ffffff',
                                                        borderColor: '#000000',
                                                    },
                                                    '&:checked .mantine-Radio-icon':
                                                        {
                                                            color: '#000000',
                                                        },
                                                },
                                                icon: {
                                                    color:
                                                        addressType ===
                                                        type.value
                                                            ? '#000000'
                                                            : 'transparent',
                                                },
                                                label: {
                                                    fontSize: '14px',
                                                    fontWeight: 500,
                                                    color: '#374151',
                                                    marginTop: '4px',
                                                },
                                            }}
                                        />
                                    </div>
                                ))}
                            </div>
                        </Radio.Group>
                    </Col>
                    <Col md='6'>
                        <Label>Quốc gia</Label>
                        <Input
                            type='text'
                            value={tempAddress.country}
                            onChange={(e) =>
                                handleInputChange('country', e.target.value)
                            }
                            placeholder='Việt Nam'
                        />
                    </Col>
                    <Col md='6'>
                        <Label>Tỉnh/Thành phố</Label>
                        <Input
                            type='select'
                            value={tempAddress.provinceId}
                            onChange={(e) =>
                                handleInputChange('provinceId', e.target.value)
                            }
                        >
                            <option value=''>Chọn tỉnh/thành phố</option>
                            {provinces.map((province) => (
                                <option
                                    key={province.value}
                                    value={province.value}
                                >
                                    {province.label}
                                </option>
                            ))}
                        </Input>
                    </Col>
                    <Col md='6'>
                        <Label>Quận/Huyện</Label>
                        <Input
                            type='select'
                            value={tempAddress.districtId}
                            onChange={(e) =>
                                handleInputChange('districtId', e.target.value)
                            }
                        >
                            <option value=''>Chọn quận/huyện</option>
                            {districts.map((district) => (
                                <option
                                    key={district.value}
                                    value={district.value}
                                >
                                    {district.label}
                                </option>
                            ))}
                        </Input>
                    </Col>
                    <Col md='6'>
                        <Label>Phường/Xã</Label>
                        <Input
                            type='select'
                            value={tempAddress.wardId}
                            onChange={(e) =>
                                handleInputChange('wardId', e.target.value)
                            }
                        >
                            <option value=''>Chọn phường/xã</option>
                            {wards.map((ward) => (
                                <option key={ward.value} value={ward.value}>
                                    {ward.label}
                                </option>
                            ))}
                        </Input>
                    </Col>
                    <Col md='12'>
                        <Label>Địa chỉ cụ thể</Label>
                        <Input
                            type='text'
                            value={tempAddress.addressName}
                            onChange={(e) =>
                                handleInputChange('addressName', e.target.value)
                            }
                            placeholder='Nhập địa chỉ cụ thể (Số nhà, đường,..)'
                        />
                    </Col>
                </Row>
            </ModalBody>
            <ModalFooter>
                <Button
                    onClick={toggle}
                    style={{
                        backgroundColor: '#ffffff',
                        color: '#F06548',
                        borderColor: '#F06548',
                    }}
                >
                    Hủy
                </Button>
                <Button
                    onClick={handleAddAddress}
                    style={{
                        backgroundColor: '#0AB39C',
                        color: '#ffffff',
                        borderColor: '#0AB39C',
                    }}
                >
                    Lưu
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default AddressModal;
