'use client';

import { useDetailProduct, useUpdateProduct } from '@/apis/product/product.api';
import { IProduct } from '@/apis/product/product.type';
import { ROUTES } from '@/lib/routes';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';
import FormProducts from '../../_components/FormProducts';
import { KEYS_TO_PRODUCT } from '@/constants/key-convert';

const UpdateProducts = () => {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { data: product } = useDetailProduct(id);
    const initValue = product?.data;
    const { mutate: updateProduct } = useUpdateProduct({
        onSuccess: () => {
            toast.success('Chỉnh sửa thông tin sản phẩm thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX);
        },
        onError: () => {
            toast.error('Chỉnh sửa thông tin sản phẩm thất bại');
        },
    });
    const handleSubmit = (data: IProduct) => {
        data.productOptions.map((item) => {
            item.installationType = Number(item.installationType);
            item.productOptionType = Number(item.productOptionType);
        });

        const payload = convertFormValueToPayload(data, KEYS_TO_PRODUCT);

        updateProduct(payload as IProduct);
    };
    const handleClose = () => {
        router.back();
    };
    if (!initValue) {
        return <Spinner />;
    }
    return (
        <FormProducts
            page='chinh-sua'
            initValue={convertPayloadToFormValue(initValue) as IProduct}
            onSubmit={handleSubmit}
            onClose={handleClose}
        />
    );
};
export default UpdateProducts;
