import { IDealDetail } from '@/apis/opportunity/opportunity.type';
import { Col, Label, Row } from 'reactstrap';

interface Tab3Props {
    data?: IDealDetail;
}

const Tab3 = ({ data }: Tab3Props) => {
    const customerNeed = data?.customerNeed;

    return (
        <div>
            <Row>
                <Col md={6}>
                    <Col md={12} className='mb-3'>
                        <Label className='text-muted fw-normal'>
                            THỜI GIAN TIẾP NHẬN NHU CẦU
                        </Label>
                        <p>
                            {customerNeed?.timeRequest
                                ? new Date(
                                      customerNeed?.timeRequest,
                                  ).toLocaleDateString('vi-VN')
                                : '-'}
                        </p>
                    </Col>
                    <Col md={12} className='mb-3'>
                        <Label className='text-muted fw-normal'>
                            PHÂN LOẠI NHU CẦU
                        </Label>
                        <p className='mb-0'>
                            {customerNeed?.classificationNeedTypeName || '-'}
                        </p>
                    </Col>
                    <Col md={12} className='mb-3'>
                        <Label className='text-muted fw-normal'>
                            NGÂN SÁCH DỰ KIẾN
                        </Label>
                        <p>
                            {customerNeed?.estimatedBudget.toLocaleString(
                                'vi-VN',
                            )}
                        </p>
                    </Col>
                </Col>
                <Col md={6}>
                    <Col md={12} className='mb-3'>
                        <Label className='text-muted fw-normal'>
                            THỜI GIAN MUA HÀNG DỰ KIẾN
                        </Label>
                        <p>
                            {customerNeed?.estimatedPurchase
                                ? new Date(
                                      customerNeed?.estimatedPurchase,
                                  ).toLocaleDateString('vi-VN')
                                : '-'}
                        </p>
                    </Col>
                    <Col md={12} className='mb-3'>
                        <Label className='text-muted fw-normal'>
                            NHU CẦU QUAN TÂM
                        </Label>
                        <p className='mb-0'>
                            {customerNeed?.needAttention || '-'}
                        </p>
                    </Col>
                </Col>
            </Row>
            <Row>
                <Col md={12} className='mb-3'>
                    <Label className='text-muted fw-normal'>
                        NỘI DUNG TRAO ĐỔI
                    </Label>
                    <p className='mb-0' style={{ lineHeight: '1.6' }}>
                        {customerNeed?.contentExchange}
                    </p>
                </Col>
            </Row>
        </div>
    );
};

export default Tab3;
