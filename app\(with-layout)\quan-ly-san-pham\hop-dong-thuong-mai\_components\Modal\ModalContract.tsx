import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { <PERSON><PERSON>, ModalHeader, <PERSON>dal<PERSON><PERSON>, ModalFooter, Button } from 'reactstrap';

interface ContractModalProps {
    isOpen: boolean;
    toggle: () => void;
}
const ContractModal = ({ isOpen, toggle }: ContractModalProps) => {
    const router = useRouter();
    const [selectedType, setSelectedType] = useState('asic');
    const handleRouter = () => {
        if (selectedType === 'asic') {
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.CREATE_ASIC);
            return;
        }
        if (selectedType === 'customer') {
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.CREATE_CUSTOMER);
            return;
        }
        return;
    };
    return (
        <Modal isOpen={isOpen} toggle={toggle} centered>
            <ModalHeader toggle={toggle}><PERSON><PERSON><PERSON> hợp đồng</ModalHeader>
            <ModalBody>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 16,
                        marginBottom: 16,
                    }}
                >
                    <label
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            cursor: 'pointer',
                            gap: 8,
                            padding: 8,

                            border: 'none',
                        }}
                    >
                        <input
                            type='radio'
                            name='contractType'
                            checked={selectedType === 'asic'}
                            onChange={() => setSelectedType('asic')}
                            style={{
                                accentColor: '#28a745',
                                width: 15,
                                height: 15,
                            }}
                        />
                        <span
                            style={{
                                fontWeight: 500,
                                fontSize: '14px',
                            }}
                        >
                            Hợp đồng theo mẫu của ASIC
                        </span>
                    </label>
                    <label
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            cursor: 'pointer',
                            gap: 8,
                            padding: 8,
                            border: 'none',
                        }}
                    >
                        <input
                            type='radio'
                            name='contractType'
                            checked={selectedType === 'customer'}
                            onChange={() => setSelectedType('customer')}
                            style={{
                                accentColor: '#28a745',
                                width: 15,
                                height: 15,
                            }}
                        />
                        <span
                            style={{
                                fontWeight: 500,
                                fontSize: '14px',
                            }}
                        >
                            Hợp đồng theo mẫu khách hàng
                        </span>
                    </label>
                </div>
            </ModalBody>
            <ModalFooter>
                <Button outline color='danger' onClick={toggle}>
                    Hủy
                </Button>
                <Button color='success' onClick={handleRouter}>
                    Tiếp tục
                </Button>
            </ModalFooter>
        </Modal>
    );
};
export default ContractModal;
