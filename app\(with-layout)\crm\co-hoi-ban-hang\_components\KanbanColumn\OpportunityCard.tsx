import { IDealResponse } from '@/apis/opportunity/opportunity.type';
import { ROUTES } from '@/lib/routes';
import { Draggable } from '@hello-pangea/dnd';
import { Avatar } from '@mantine/core';
import { useRouter } from 'next/navigation';

interface OpportunityCardProps {
    opportunity: IDealResponse;
    index: number;
}

export const OpportunityCard: React.FC<OpportunityCardProps> = ({
    opportunity,
    index,
}) => {
    const router = useRouter();
    return (
        <Draggable draggableId={opportunity.dealId} index={index}>
            {(provided, snapshot) => {
                return (
                    <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className='card task-box mb-2'
                        style={{
                            ...provided.draggableProps.style,
                            width: '100%',
                            maxWidth: '280px',
                            minHeight: '271px',
                            opacity: snapshot.isDragging ? 0.8 : 1,
                            transform: snapshot.isDragging
                                ? `${provided.draggableProps.style?.transform} rotate(1deg)`
                                : provided.draggableProps.style?.transform,
                            overflowX: 'hidden',
                        }}
                        onClick={() =>
                            router.push(
                                ROUTES.CRM.SALES_OPPORTUNITIES.DETAIL.replace(
                                    ':id',
                                    opportunity.dealId,
                                ),
                            )
                        }
                    >
                        <div
                            className='card-body d-flex flex-column gap-2'
                            style={{ overflowX: 'hidden' }}
                        >
                            <div
                                className='d-flex flex-column mb-2 gap-2'
                                style={{ overflowX: 'hidden' }}
                            >
                                <h5
                                    className='fs-16 fw-bold flex-grow-1 mb-0'
                                    style={{
                                        overflowX: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'normal',
                                        wordWrap: 'break-word',
                                    }}
                                >
                                    {opportunity.title}
                                </h5>
                                <div
                                    className='rounded'
                                    style={{
                                        background: 'rgba(155, 228, 208, 1)',
                                        width: 'fit-content',
                                        padding: '2px 8px',
                                    }}
                                >
                                    Cyber Security
                                </div>
                                <p
                                    className='text-muted mb-2'
                                    style={{ color: 'rgba(69, 82, 102, 1)' }}
                                >
                                    Doanh thu dự kiến:{' '}
                                    {opportunity.amount.toLocaleString('vi-VN')}{' '}
                                    VND
                                </p>
                                <p
                                    className='text-muted mb-2'
                                    style={{ color: 'rgba(69, 82, 102, 1)' }}
                                >
                                    Xác suất thắng: 0%
                                </p>
                                <p
                                    className='text-muted mb-2'
                                    style={{ color: 'rgba(69, 82, 102, 1)' }}
                                >
                                    Nhu cầu: Tìm hiểu sản phẩm
                                </p>
                                <div
                                    className='flex-grow-1 fw-bold'
                                    style={{
                                        background: 'rgba(247, 176, 54, 0.1)',
                                        color: 'rgba(247, 176, 54, 1)',
                                        width: 'fit-content',
                                    }}
                                >
                                    Trung bình
                                </div>
                            </div>

                            {opportunity?.contacts && (
                                <>
                                    <div
                                        className='border-top'
                                        style={{
                                            borderColor:
                                                'rgba(238, 238, 238, 1)',
                                        }}
                                    ></div>
                                    {opportunity?.contacts?.map(
                                        (contact, idx) => (
                                            <div
                                                key={idx}
                                                className='avatar-group-item'
                                            >
                                                <Avatar
                                                    src={
                                                        contact?.avatarUrl ??
                                                        contact.userName
                                                    }
                                                />
                                            </div>
                                        ),
                                    )}
                                </>
                            )}

                            <div
                                className='border-top'
                                style={{
                                    borderColor: 'rgba(238, 238, 238, 1)',
                                }}
                            ></div>

                            <div className='d-flex justify-content-between align-items-center'>
                                <p>Ngày tạo: 08/01/2025</p>
                                <Avatar
                                    src={
                                        opportunity?.owner?.avatarUrl ??
                                        opportunity?.owner.userName
                                    }
                                />
                            </div>
                        </div>
                    </div>
                );
            }}
        </Draggable>
    );
};
