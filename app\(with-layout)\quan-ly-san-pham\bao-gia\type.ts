import { Option } from '@/types/app.type';

export enum TabValues {
    QUOTATION_FORM = 'QUOTATION_FORM',
    LINK_QUOTE = 'LINK_QUOTE',
}

export enum Status {
    QUOTE = 'QUOTE',
    SENDED = 'SENDED',
    HAS_REFUSED = 'HAS_REFUSED',
    CONFIRMED = 'CONFIRMED',
}

export const statusOptions: Option[] = [
    {
        label: 'Báo giá',
        value: Status.QUOTE,
    },
    {
        label: 'Đã gửi',
        value: Status.SENDED,
    },
    {
        label: 'Đã từ chối',
        value: Status.HAS_REFUSED,
    },
    {
        label: 'Đã xác nhận',
        value: Status.CONFIRMED,
    },
];

export enum FieldType {
    DOCUMENT = 'DOCUMENT',
    NUMBER = 'NUMBER',
    CURRENCY = 'CURRENCY',
    SELECT_DATE = 'SELECT_DATE',
    SELECT_TIME = 'SELECT_TIME',
    PERCENT = 'PERCENT',
    FRACTION = 'FRACTION',
    PHONE_NUMBER = 'PHONE_NUMBER',
}

export const fieldTypeOptions: Option[] = [
    {
        label: 'Văn bản',
        value: FieldType.DOCUMENT,
    },
    {
        label: 'Số',
        value: FieldType.NUMBER,
    },
    {
        label: 'Tiền tệ',
        value: FieldType.CURRENCY,
    },
    {
        label: 'Chọn ngày',
        value: FieldType.SELECT_DATE,
    },
    {
        label: 'Chọn giờ',
        value: FieldType.SELECT_TIME,
    },
    {
        label: 'Phần trăm',
        value: FieldType.PERCENT,
    },
    {
        label: 'Phân số',
        value: FieldType.FRACTION,
    },
    {
        label: 'Số điện thoại',
        value: FieldType.PHONE_NUMBER,
    },
];

export const linkQuoteTemplateOptions = [
    { label: 'Báo giá kiểm thử xâm nhập', value: 'penetration_testing' },
    {
        label: 'Báo giá giải pháp an ninh mạng',
        value: 'cybersecurity_solutions',
    },
    { label: 'Báo giá điện-điện tử rada', value: 'radar_systems' },
];

export interface TypeFormCustomerAndParter {
    companyName: string | null;
    companyId: string | null;
    tradePartnerId: string | null;
    tradePartnerName: string | null;
}
