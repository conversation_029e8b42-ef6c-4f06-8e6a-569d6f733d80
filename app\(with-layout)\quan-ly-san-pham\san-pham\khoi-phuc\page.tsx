'use client';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    <PERSON><PERSON>,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';

import {
    useRestoreProduct,
    useSearchProduct,
} from '@/apis/product/product.api';
import {
    ResponseSearchProduct,
    SearchProduct,
} from '@/apis/product/product.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import { ROUTES } from '@/lib/routes';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';

import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ModalRestore from '@/components/common/Modal/ModalRestore';
import { getOneMonthAgo, getToday } from '@/utils/time';
import { toast } from 'react-toastify';
import useGetColumn from '../_hook/useGetColumn';
const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ResponseSearchProduct>,
        })),
    {
        ssr: false,
    },
);
const RestoreProduct = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const methods = useForm<SearchProduct>({
        defaultValues: {
            Page: 1,
            PageSize: 10,
            IsDeleted: true,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const { control } = methods;
    const [Name, UserNameCreated, Page, PageSize, IsDeleted, FromDate, ToDate] =
        useWatch({
            control,
            name: [
                'Name',
                'UserNameCreated',
                'Page',
                'PageSize',
                'IsDeleted',
                'FromDate',
                'ToDate',
            ],
        });

    const handleOneRestore = (data: ResponseSearchProduct) => {
        setSelectedIds([data.id]);
        setSelectedNames([data.name]);
        setModal(true);
    };
    const columns = useGetColumn({
        page: 'restore',
        onRestore: handleOneRestore,
    });
    const { data, refetch, isLoading } = useSearchProduct({
        Name,
        UserNameCreated,
        FromDate,
        ToDate,
        Page,
        PageSize,
        IsDeleted,
    });
    const { items: listProduct = [], totalItems } = data ?? {};
    const handlePageChange = (page: number) => {
        methods.setValue('Page', page);
    };

    const handlePageSizeChange = (size: number) => {
        methods.setValue('PageSize', size);
        methods.setValue('Page', 1);

        setTimeout(() => {
            refetch();
        }, 0);
    };
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    const { mutate: restoreProduct } = useRestoreProduct({
        onSuccess: () => {
            toast.success('Khôi phục sản phẩm thành công');
            setModal(false);
            setSelectedIds([]);
            setSelectedNames([]);
            refetch();
        },
        onError: () => {
            toast.error('Khôi phục sản phẩm thất bại');
        },
    });
    const handleConfimRestore = () => {
        restoreProduct(selectedIds);
    };
    const handleRestore = () => {
        setModal(true);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col md={12}>
                    <ButtonHeader
                        onCreateNew={() =>
                            router.push(
                                ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.CREATE,
                            )
                        }
                        showDateFilters={true}
                    />
                </Col>

                <Col md={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên sản phẩm...'
                                    />
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <Button
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => handleRestore()}
                                            >
                                                Khôi phục
                                            </Button>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES
                                                                .PRODUCT_MANAGEMENT
                                                                .PRODUCTS.INDEX,
                                                        )
                                                    }
                                                >
                                                    Sản phẩm
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listProduct}
                            isLoading={isLoading}
                            totalItems={totalItems ?? 0}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                            tableProps={{
                                enableRowSelection: true,
                                enableMultiRowSelection: true,
                                selectAllMode: 'page',
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: 'customGreen',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: 'customGreen',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                initialState: {
                                    pagination: {
                                        pageIndex: (Page || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                },
                                state: {
                                    pagination: {
                                        pageIndex: (Page || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index = listProduct.findIndex(
                                                (
                                                    product: ResponseSearchProduct,
                                                ) => product.id === id,
                                            );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                },
                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listProduct.findIndex(
                                                            (
                                                                product: ResponseSearchProduct,
                                                            ) =>
                                                                product.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listProduct[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableHeadCellProps: {
                                    align: 'left',
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                                onPaginationChange: (updater) => {
                                    let newPagination;
                                    if (typeof updater === 'function') {
                                        newPagination = updater({
                                            pageIndex: (Page || 1) - 1,
                                            pageSize: PageSize || 10,
                                        });
                                    } else {
                                        newPagination = updater;
                                    }

                                    if (
                                        newPagination.pageIndex !==
                                        (Page || 1) - 1
                                    ) {
                                        handlePageChange(
                                            newPagination.pageIndex + 1,
                                        );
                                    }

                                    if (
                                        newPagination.pageSize !==
                                        (PageSize || 10)
                                    ) {
                                        handlePageSizeChange(
                                            newPagination.pageSize,
                                        );
                                    }
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalRestore
                onRestore={handleConfimRestore}
                onClose={handleClose}
                isOpen={modal}
                page='nhóm sản phẩm'
                data={selectedNames}
            />
        </FormProvider>
    );
};
export default RestoreProduct;
