import {
    Button,
    Input,
    Modal,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ModalHeader,
} from 'reactstrap';

interface FilterModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export const FilterModal: React.FC<FilterModalProps> = ({
    isOpen,
    onClose,
}) => {
    return (
        <Modal isOpen={isOpen} toggle={onClose} size='lg'>
            <ModalHeader toggle={onClose} className='border-bottom pb-3'>
                <h5
                    className='m-0'
                    style={{
                        color: '#212529',
                        fontSize: '16px',
                        fontWeight: 500,
                    }}
                >
                    Bộ lọc nâng cao
                </h5>
            </ModalHeader>
            <ModalBody className='p-4'>
                <div className='mb-4'>
                    <label
                        className='form-label mb-2'
                        style={{ fontSize: '14px', color: '#212529' }}
                    >
                        Tên bộ lọc
                    </label>
                    <Input
                        type='text'
                        placeholder='Nhập tên bộ lọc'
                        style={{
                            backgroundColor: 'white',
                            border: '1px solid #e9ebec',
                            height: '36px',
                            borderRadius: '4px',
                            fontSize: '13px',
                        }}
                    />
                </div>

                <div className='bg-light rounded p-3'>
                    <div
                        className='d-flex mb-2'
                        style={{
                            backgroundColor: '#f3f6f9',
                            padding: '10px',
                            borderRadius: '4px',
                            paddingLeft: '32px',
                        }}
                    >
                        <div
                            style={{
                                flex: '30%',
                                color: '#212529',
                                fontSize: '13px',
                                fontWeight: 500,
                            }}
                        >
                            Trường thông tin
                        </div>
                        <div
                            style={{
                                flex: '25%',
                                color: '#212529',
                                fontSize: '13px',
                                fontWeight: 500,
                            }}
                        >
                            Điều kiện
                        </div>
                        <div
                            style={{
                                flex: '40%',
                                color: '#212529',
                                fontSize: '13px',
                                fontWeight: 500,
                            }}
                        >
                            Giá trị
                        </div>
                        <div style={{ flex: '5%' }}></div>
                    </div>

                    <div
                        className='d-flex align-items-center pt-2'
                        style={{ paddingLeft: '32px' }}
                    >
                        <div style={{ flex: '30%', paddingRight: '8px' }}>
                            <Input
                                type='select'
                                style={{
                                    height: '36px',
                                    fontSize: '13px',
                                    backgroundColor: 'white',
                                    border: '1px solid #e9ebec',
                                    width: '100%',
                                }}
                            >
                                <option>Họ và tên</option>
                            </Input>
                        </div>
                        <div style={{ flex: '25%', paddingRight: '8px' }}>
                            <Input
                                type='select'
                                style={{
                                    height: '36px',
                                    fontSize: '13px',
                                    backgroundColor: 'white',
                                    border: '1px solid #e9ebec',
                                    width: '100%',
                                }}
                            >
                                <option>Là</option>
                            </Input>
                        </div>
                        <div style={{ flex: '40%', paddingRight: '8px' }}>
                            <Input
                                type='text'
                                placeholder='Cơ hội mua máy móc sản xuất'
                                style={{
                                    height: '36px',
                                    fontSize: '13px',
                                    backgroundColor: 'white',
                                    border: '1px solid #e9ebec',
                                    width: '100%',
                                }}
                            />
                        </div>
                        <div style={{ flex: '5%' }} className='text-center'>
                            <Button
                                color='link'
                                className='text-danger p-0'
                                style={{ fontSize: '18px' }}
                            >
                                <i className='ri-delete-bin-line'></i>
                            </Button>
                        </div>
                    </div>

                    <span
                        className='badge bg-light text-dark me-2'
                        style={{ fontSize: '12px' }}
                    >
                        Và
                    </span>

                    <div
                        className='d-flex align-items-center pt-2'
                        style={{ paddingLeft: '32px' }}
                    >
                        <div style={{ flex: '30%', paddingRight: '8px' }}>
                            <Input
                                type='select'
                                style={{
                                    height: '36px',
                                    fontSize: '13px',
                                    backgroundColor: 'white',
                                    border: '1px solid #e9ebec',
                                    width: '100%',
                                }}
                            >
                                <option>Họ và tên</option>
                            </Input>
                        </div>
                        <div style={{ flex: '25%', paddingRight: '8px' }}>
                            <Input
                                type='select'
                                style={{
                                    height: '36px',
                                    fontSize: '13px',
                                    backgroundColor: 'white',
                                    border: '1px solid #e9ebec',
                                    width: '100%',
                                }}
                            >
                                <option>Là</option>
                            </Input>
                        </div>
                        <div style={{ flex: '40%', paddingRight: '8px' }}>
                            <Input
                                type='text'
                                placeholder='Cơ hội mua máy móc sản xuất'
                                style={{
                                    height: '36px',
                                    fontSize: '13px',
                                    backgroundColor: 'white',
                                    border: '1px solid #e9ebec',
                                    width: '100%',
                                }}
                            />
                        </div>
                        <div style={{ flex: '5%' }} className='text-center'>
                            <Button
                                color='link'
                                className='text-danger p-0'
                                style={{ fontSize: '18px' }}
                            >
                                <i className='ri-delete-bin-line'></i>
                            </Button>
                        </div>
                    </div>
                </div>

                <Button
                    color='light'
                    size='sm'
                    className='mt-3'
                    style={{
                        padding: '6px 12px',
                        fontSize: '13px',
                        backgroundColor: '#f3f6f9',
                        border: 'none',
                    }}
                >
                    <i className='ri-add-line align-bottom me-1'></i>
                    Thêm điều kiện
                </Button>
            </ModalBody>
            <ModalFooter className='border-top pt-3 gap-2'>
                <Button
                    color='light'
                    onClick={onClose}
                    style={{
                        fontSize: '13px',
                        backgroundColor: '#f3f6f9',
                        border: 'none',
                        padding: '6px 12px',
                    }}
                >
                    Bỏ lọc
                </Button>
                <Button
                    color='primary'
                    style={{
                        fontSize: '13px',
                        backgroundColor: '#405189',
                        border: 'none',
                        padding: '6px 12px',
                    }}
                >
                    Tìm kiếm
                </Button>
                <Button
                    color='success'
                    style={{
                        fontSize: '13px',
                        backgroundColor: '#0ab39c',
                        border: 'none',
                        padding: '6px 12px',
                    }}
                >
                    Lưu và tìm kiếm
                </Button>
            </ModalFooter>
        </Modal>
    );
};
