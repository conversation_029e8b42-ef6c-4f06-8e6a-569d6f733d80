const columnDateDefault = {
    size: 90,
    minSize: 90, // Set minSize để không bị thu nhỏ
    maxSize: 120, // Set maxSize để giữ nguyên kích thước
    header: '<PERSON><PERSON><PERSON> tạo',
    enableResizing: false, // <PERSON>hông cho resize
    enableGrouping: false,
    enableSorting: false,
    enableColumnFilter: false,
    mantineTableHeadCellProps: {
        style: {
            textAlign: 'center',
            backgroundColor: '#f3f6f9', // Thêm background color
        },
    },
    mantineTableBodyCellProps: {
        style: { textAlign: 'center' },
    },
    accessorKey: 'createdOn',
};

export default columnDateDefault;
