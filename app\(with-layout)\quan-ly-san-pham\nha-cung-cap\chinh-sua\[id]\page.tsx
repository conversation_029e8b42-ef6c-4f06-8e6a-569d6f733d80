'use client';

import { useParams, useRouter } from 'next/navigation';
import FormSuppliers from '../../_components/FormSuppliers';
import {
    useDetailSupplier,
    useUpdateSupplier,
} from '@/apis/supplier/supplier.api';
import { Spinner } from 'reactstrap';
import { ISupplier } from '@/apis/supplier/supplier.type';
import { toast } from 'react-toastify';
import { ROUTES } from '@/lib/routes';

const UpdateSuppliers = () => {
    const params = useParams();
    const router = useRouter();
    const id = params.id as string;
    const { data: dataSuppliers, isLoading } = useDetailSupplier(id);

    const { mutate: updateSupplier } = useUpdateSupplier({
        onSuccess: () => {
            toast.success('Chỉnh sửa nhà cung cấp thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: () => {
            toast.error('Chỉnh sửa nhà cung cấp thất bại');
        },
    });

    const handleUpdate = (data: ISupplier) => {
        data.commonStatus = Number(data.commonStatus);
        updateSupplier(data);
    };

    const handleClose = () => {
        router.back();
    };

    if (isLoading) {
        return <Spinner />;
    }

    return (
        <FormSuppliers
            page='chinh-sua'
            initValue={dataSuppliers?.data}
            onSubmit={handleUpdate}
            onClose={handleClose}
        />
    );
};
export default UpdateSuppliers;
