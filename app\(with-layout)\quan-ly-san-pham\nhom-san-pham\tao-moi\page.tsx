'use client';
import { useCreateProductGroups } from '@/apis/product-group/product-group.api';
import { IProductGroup } from '@/apis/product-group/product-group.type';
import { KEYS_TO_PRODUCT_GROUP } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import FormProductGroups from '../_components/FormProductGroups';

const CreatProductGroups = () => {
    const router = useRouter();
    const { mutate: createProductGroup } = useCreateProductGroups({
        onSuccess: () => {
            toast.success('Tạo mới nhóm sản phẩm thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.INDEX);
        },
        onError: () => {
            toast.error('Tạo mới nhóm sản phẩm thất bại');
        },
    });
    const handleCreate = (data: IProductGroup) => {
        const payload = convertFormValueToPayload(data, KEYS_TO_PRODUCT_GROUP);
        createProductGroup(payload as IProductGroup);
    };
    const handleCancel = () => {
        router.back();
    };
    return (
        <FormProductGroups
            page='tao-moi'
            onSubmit={handleCreate}
            onCancel={handleCancel}
        />
    );
};
export default CreatProductGroups;
