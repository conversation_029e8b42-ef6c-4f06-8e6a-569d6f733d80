'use client';
import {
    useDeleteSupplier,
    useSearchSupplier,
} from '@/apis/supplier/supplier.api';
import {
    ResponseSupplier,
    SearchSupplier,
} from '@/apis/supplier/supplier.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { ROUTES } from '@/lib/routes';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { ACTIONS } from './_types/action.type';
const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ResponseSupplier>,
        })),
    {
        ssr: false,
    },
);
const Suppliers = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const methods = useForm<SearchSupplier>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            IsDescending: false,
        },
    });
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const { control, setValue } = methods;
    const [
        Name,
        UserNameCreated,
        PageNumber,
        PageSize,
        IsDescending,
        FromDate,
        ToDate,
        SortField,
    ] = useWatch({
        control,
        name: [
            'Name',
            'UserNameCreated',
            'PageNumber',
            'PageSize',
            'IsDescending',
            'FromDate',
            'ToDate',
            'SortField',
        ],
    });
    const handleSelectedAction = (action: ACTIONS, row?: ResponseSupplier) => {
        if (!row) {
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                break;
            case ACTIONS.EDIT:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.UPDATE.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.DETAIL.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list',
    });
    const { data, refetch, isLoading } = useSearchSupplier({
        Name,
        UserNameCreated,
        PageNumber,
        PageSize,
        IsDescending,
        FromDate,
        ToDate,
        SortField,
    });
    const { items: listSupplier = [], totalItems } = data ?? {};

    const handleCreate = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.CREATE);
    };
    const { mutate: deleteSupplier } = useDeleteSupplier({
        onSuccess: () => {
            toast.success('Xóa nhà cung cấp thành công');
            setModal(false);
            setSelectedIds([]);
            setSelectedNames([]);
            refetch();
        },
        onError: (error) => {
            // Debug error in development
            if (process.env.NODE_ENV === 'development') {
                console.error('=== DELETE ERROR ===');
                console.error('Full error object:', error);
                console.error('Error status:', error?.status);
                console.error('Error message:', error?.message);
            }

            const status = error?.status;
            if (status === 400 || status === 401) {
                toast.warning(error?.message || 'Lỗi xác thực');
                return;
            }
            toast.error(
                `Xóa nhà cung cấp thất bại${error?.message ? ': ' + error.message : ''}`,
            );
        },
    });
    const handleConfimDelete = () => {
        deleteSupplier({ ids: selectedIds, isDeleted: false });
    };
    const handleDelete = () => {
        setModal(true);
    };
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col md={12}>
                    <ButtonHeader
                        showExportButton={false}
                        onCreateNew={handleCreate}
                        showDateFilters={true}
                    />
                </Col>
                <Col md={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex gap-3'>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên nhà cung cấp'
                                        />

                                        <ComboboxSelectUserControl
                                            name='UserNameCreated'
                                            placeholder='Người tạo'
                                            style={{ width: '250px' }}
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <Button
                                                style={{
                                                    backgroundColor: 'red',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => handleDelete()}
                                            >
                                                Xóa
                                            </Button>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES
                                                                .PRODUCT_MANAGEMENT
                                                                .SUPPLIERS
                                                                .RESTORE,
                                                        )
                                                    }
                                                >
                                                    Khôi phục tài khoản
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listSupplier}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listSupplier.findIndex(
                                                    (
                                                        contact: ResponseSupplier,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listSupplier.findIndex(
                                                            (
                                                                contact: ResponseSupplier,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listSupplier[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='nhà cung cấp'
                data={selectedNames}
            />
        </FormProvider>
    );
};
export default Suppliers;
