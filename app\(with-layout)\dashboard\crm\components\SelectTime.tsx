import FormController from '@/components/common/FormController';
import { FormProvider, useForm } from 'react-hook-form';
import { Card, CardBody, Col, Row } from 'reactstrap';

const SelectTime = () => {
    const methods = useForm();

    const optionsTime = [
        {
            label: '-- Chọn thời gian --',
            value: '',
            disabled: true,
        },
        {
            label: 'Hôm nay',
            value: 'today',
        },
        {
            label: 'Tuần trước',
            value: 'lastWeek',
        },
    ];

    const optionsOrganization = [
        {
            label: '-- Ch<PERSON><PERSON> cơ cấu tổ chức --',
            value: '',
            disabled: true,
        },
        {
            label: '<PERSON> cá nhân',
            value: 'individual',
        },
        {
            label: 'Theo cá nhân',
            value: 'organizationalStructure',
        },
    ];

    const optionsDashboard = [
        {
            label: 'Tổng quan cơ hội',
            value: 'oppotunityOverview',
        },
        {
            label: 'Tổng quan khách hàng',
            value: 'customerOverview',
        },
    ];

    const optionsTypeCompany = [
        {
            label: 'Chính phủ',
            value: 'government',
        },
        {
            label: 'Thương mại',
            value: 'commerce',
        },
        {
            label: 'Đầu tư nước ngoài',
            value: 'foreignInvestment',
        },
    ];

    return (
        <Card>
            <CardBody>
                <FormProvider {...methods}>
                    <Row>
                        <Col xl={3} lg={6}>
                            <FormController
                                label='Chọn thời gian'
                                controlType='nativeSelect'
                                name='time'
                                data={optionsTime}
                                defaultValue=''
                            />
                        </Col>
                        <Col xl={3} lg={6}>
                            <FormController
                                label='Chọn thời gian'
                                controlType='nativeSelect'
                                name='organization'
                                data={optionsOrganization}
                                defaultValue=''
                            />
                        </Col>
                        <Col xl={3} lg={6}>
                            <FormController
                                label='Chọn thời gian'
                                controlType='nativeSelect'
                                name='dashboard'
                                data={optionsDashboard}
                                defaultValue=''
                            />
                        </Col>
                        <Col xl={3} lg={6}>
                            <FormController
                                label='Chọn thời gian'
                                controlType='nativeSelect'
                                name='company'
                                data={optionsTypeCompany}
                                defaultValue=''
                            />
                        </Col>
                    </Row>
                </FormProvider>
            </CardBody>
        </Card>
    );
};

export default SelectTime;
