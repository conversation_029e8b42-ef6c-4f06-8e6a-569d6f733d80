import {
    IClassProduct,
    IOpportunity,
} from '@/apis/opportunity/opportunity.type';
import FormController from '@/components/common/FormController';
import ComboboxSelectProductControl from '@/components/common/FormController/ComboboxSelectProductControl';
import SelectProductOptionsControl from '@/components/common/FormController/SelectProductOptionsControl';
import { useState } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { Button } from 'reactstrap';

interface RowInfoProductProps {
    index: number;
    key: string;
    name: string;
    removeProductInfo: (index: number) => void;
}
const RowInfoProduct = (props: RowInfoProductProps) => {
    const { index, key, name, removeProductInfo } = props;

    const [isEdit, setIsEdit] = useState<boolean>(true);

    const handleToggleEdit = () => {
        setIsEdit(!isEdit);
    };

    const methods = useFormContext<IOpportunity>();

    const { control } = methods;

    const productId = useWatch({
        control,
        // @ts-expect-error: lỗi type
        name: `${name}.${index}.productClassicId`,
    }) as string;

    return (
        <tr key={`row-product-info-${key}`}>
            <th style={{ width: '100px' }}>
                <div className='mt-1 text-center'>{index + 1}</div>
            </th>
            <th style={{ width: '30%' }}>
                <ComboboxSelectProductControl
                    name={`${name}.${index}.productClassicId`}
                    placeholder='Chọn sản phẩm khách hàng đang quan tâm'
                    style={{ width: '100%' }}
                />
            </th>
            <th style={{ width: '20%' }}>
                <SelectProductOptionsControl
                    name={`${name}.${index}.productClassicOptionId`}
                    productId={productId ?? ''}
                    placeholder='Chọn option sản phẩm'
                    style={{ width: '100%' }}
                />
            </th>
            <th style={{ width: '40%' }}>
                <FormController
                    controlType='textInput'
                    name={`${name}.${index}.productClassicOptionId`}
                    style={{ width: '100%' }}
                />
            </th>
            <th style={{ width: '150px' }}>
                <td>
                    <div className='d-flex gap-2 mt-1'>
                        <button
                            type='button'
                            className='btn btn-sm btn-primary'
                            onClick={handleToggleEdit}
                        >
                            <i className='ri-edit-line'></i>
                        </button>
                        <button
                            type='button'
                            className='btn btn-sm btn-danger'
                            onClick={() => removeProductInfo(index)}
                        >
                            <i className='ri-delete-bin-line'></i>
                        </button>
                    </div>
                </td>
            </th>
        </tr>
    );
};

const TableInfoProduct = () => {
    const methods = useFormContext<IOpportunity>();
    const { control } = methods;

    const {
        fields: productInfos,
        append: appendProductInfo,
        remove: removeProductInfo,
    } = useFieldArray({
        control,
        name: 'customerNeed.classicProducts',
    });

    const handleAddProductInfo = () => {
        const newProductInfo = {
            productClassicId: '',
            productClassicOptionId: '',
            description: '',
        };

        appendProductInfo(newProductInfo as IClassProduct);
    };

    const renderProductInfo = () => (
        <div>
            <div className='d-flex justify-content-between align-items-center my-3'>
                <h6>Thông tin sản phẩm</h6>
                <Button
                    color='light'
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={handleAddProductInfo}
                >
                    <i className='ri-add-line me-1'></i> Thêm sản phẩm
                </Button>
            </div>
            <table
                className='table table-bordered mb-0'
                style={{ tableLayout: 'fixed', width: '100%' }}
            >
                <thead>
                    <tr>
                        <th style={{ width: '100px' }}>Số thứ tự</th>
                        <th style={{ width: '30%' }}>Model code</th>
                        <th style={{ width: '20%' }}>Option</th>
                        <th style={{ width: '40%' }}>Mô tả</th>
                        <th style={{ width: '150px' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {productInfos.map((_, index) => (
                        <RowInfoProduct
                            index={index}
                            key={`product-${index}`}
                            name='customerNeed.classicProducts'
                            removeProductInfo={removeProductInfo}
                        />
                    ))}
                    {productInfos.length === 0 && (
                        <tr>
                            <td colSpan={6} className='text-center py-3'>
                                Chưa có dữ liệu về sản phẩm
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );

    return <div className='mb-4'>{renderProductInfo()}</div>;
};

export default TableInfoProduct;
