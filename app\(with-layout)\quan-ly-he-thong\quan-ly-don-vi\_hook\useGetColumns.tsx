import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '../_types/action.type';

export interface SearchUnitManagement {
    id: number;
    name: string;
    organizational_Level: string;
    unit_Head: string;
    status: string;
    children?: SearchUnitManagement[];
}
interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: SearchUnitManagement | undefined,
    ) => void;
}

const useGetColumn = ({ onSelectedAction }: GetColumnProps) => {
    const actions: DropdownAction<SearchUnitManagement>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
            {
                icon: 'ri-prohibited-line text-danger',
                label: 'Ngừng hoạt động',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.IN_ACTIVITY, data),
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<SearchUnitManagement>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên đơn vị',
                enableHiding: false,
            },
            {
                accessorKey: 'organizational_Level',
                header: 'Cấp tổ chức',
            },
            {
                accessorKey: 'unit_Head',
                header: 'Trưởng đơn vị',
            },
            {
                accessorKey: 'status',
                header: 'Trạng thái',
                Cell: ({ cell }) => {
                    const value = cell.getValue<string>();
                    const isActive = value === 'Đang hoạt động';
                    return (
                        <span
                            className='badge me-1'
                            style={{
                                backgroundColor: isActive
                                    ? '#daf4f0'
                                    : '#dadada',
                                color: isActive ? '#2fbeab' : '#ababab',
                                display: 'inline-block',
                                textAlign: 'center',
                                padding: '4px 8px',
                                fontSize: '12px',
                                fontWeight: 500,
                                borderRadius: '4px',
                            }}
                        >
                            {value}
                        </span>
                    );
                },
            },
            {
                header: 'Hành động',
                enableResizing: false,
                Cell: ({ row }) => {
                    return (
                        <div
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        >
                            <DropdownActionMenu
                                actions={actions}
                                data={row.original}
                                direction='down'
                                end={false}
                            />
                        </div>
                    );
                },
            },
        ],
        [actions],
    );

    return columns;
};

export default useGetColumn;
