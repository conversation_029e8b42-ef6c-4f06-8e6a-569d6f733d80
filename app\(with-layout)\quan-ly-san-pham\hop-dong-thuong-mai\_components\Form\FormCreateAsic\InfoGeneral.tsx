import { useFormContext } from 'react-hook-form';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { Row, Col } from 'reactstrap';
import useGetOptionDeal from '@/hooks/useGetOptionDeal';
import useGetOptionQuoteByDealId from '@/hooks/useGetOptionQuoteByDealId';

const InfoGeneral = () => {
    const { watch } = useFormContext();
    const deal = useGetOptionDeal();
    const dealId = watch('dealId');
    const quotes = useGetOptionQuoteByDealId(dealId);

    return (
        <CollapseApp title='THÔNG TIN CHUNG'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Tên hợp đồng'
                            placeholder='Nhập tên hợp đồng'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='version'
                            label='Phiên bản'
                            placeholder='Nhập tên phiên bản cho hợp đồng'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='dealId'
                            label='Cơ hội'
                            placeholder='Chọn cơ hội'
                            data={deal}
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='quotationId'
                            label='Báo giá'
                            placeholder='Chọn báo giá'
                            data={quotes}
                            required={true}
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default InfoGeneral;
