'use client';

import {
    useGetPipelineStages,
    useSearchDealsTableView,
} from '@/apis/opportunity/opportunity.api';
import CardHeaderTabs from '@/components/common/CardHeaderTabs';
import MantineTable from '@/components/common/MantineReactTable';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Button, Container } from 'reactstrap';
import { DndContainer } from './_components/DndContainer';
import { FilterDrawer } from './_components/FilterDrawer';
import { FilterModal } from './_components/FilterModal';
import { SearchFilter } from './_components/SearchFilter';
import { KanbanProvider } from './context/KanbanContext';
import useGetColumns from './hooks/useGetColumns';
import { mockAdvancedFilters } from './mockData';
import { Filter, TabOpportunity, TYPE_DISPLAY } from './types';

const tabs = [
    {
        label: 'Tất cả cơ hội',
        value: TabOpportunity.ALL,
    },
    {
        label: 'Cơ hội của tôi',
        value: TabOpportunity.MY_OPPORTUNITY,
    },
];

export default function SalesOpportunity() {
    const { columns } = useGetColumns();

    const router = useRouter();

    const [tabCurrent, setTabCurrent] = useState<string>(tabs[0].value);
    // const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [activeFilters, setActiveFilters] = useState<Filter[]>([]);
    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
    const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
    const [activeDisplay, setActiveDisplay] = useState(TYPE_DISPLAY.KANBAN);

    const methods = useForm();

    const { data: response } = useGetPipelineStages({
        PageNumber: 1,
        PageSize: 10,
    });

    const { items = [] } = response ?? {};

    const pipelineStages = useMemo(() => {
        if (items[0]?.pipelineStages) {
            return items[0].pipelineStages;
        }

        return [];
    }, [items]);

    const pipelineStagesOptions = useMemo(() => {
        return pipelineStages.map((item) => ({
            label: item.name,
            value: item.id,
        }));
    }, [pipelineStages]);

    const { data, isLoading } = useSearchDealsTableView({
        PageNumber: '1',
        PageSize: '50',
    });

    const listOpportunities = useMemo(() => {
        if (data?.items && Array.isArray(data?.items)) {
            return data?.items;
        }

        return [];
    }, [data]);

    const handleFilterClick = (filterName: string) => {
        const newFilter = {
            id: Date.now().toString(),
            name: filterName,
            value: '',
        };
        if (!activeFilters.find((f) => f.name === filterName)) {
            setActiveFilters([...activeFilters, newFilter]);
        }
    };

    const removeFilter = (filterId: string) => {
        setActiveFilters(activeFilters.filter((f) => f.id !== filterId));
    };

    const toggleFilterDrawer = () => {
        setIsFilterDrawerOpen(!isFilterDrawerOpen);
    };

    const toggleFilterModal = () => {
        setIsFilterModalOpen(!isFilterModalOpen);
    };

    const toggleDisplay = (typeDisplay: TYPE_DISPLAY) => {
        if (typeDisplay === TYPE_DISPLAY.KANBAN) {
            document
                .querySelector('.page-content')
                ?.classList.add('ignore-padding-bottom-page-content');
        } else {
            document
                .querySelector('.page-content')
                ?.classList.remove('ignore-padding-bottom-page-content');
        }

        setActiveDisplay(typeDisplay);
    };

    const handleChangeTab = (tabActive: string) => {
        setTabCurrent(tabActive);
    };

    const handleCreateOpportunity = () => {
        router.push(ROUTES.CRM.SALES_OPPORTUNITIES.CREATE);
    };

    useEffect(() => {
        document
            .querySelector('.page-content')
            ?.classList.add('ignore-padding-bottom-page-content');
    }, []);

    return (
        <div className='bg-light'>
            <FormProvider {...methods}>
                <Container fluid>
                    <CardHeaderTabs
                        tabs={tabs}
                        tabCurrent={tabCurrent}
                        onChangeTab={handleChangeTab}
                        actions={
                            <>
                                <Button
                                    style={{
                                        backgroundColor: '#0ab39c',
                                        border: 'none',
                                        padding: '6px 12px',
                                    }}
                                    onClick={handleCreateOpportunity}
                                >
                                    <i className='ri-add-line align-bottom me-1'></i>
                                    Tạo cơ hội
                                </Button>
                                <Button
                                    style={{
                                        backgroundColor: '#0ab39c',
                                        border: 'none',
                                        padding: '6px 12px',
                                    }}
                                    onClick={() => {
                                        // setIsImportModalOpen(true)
                                    }}
                                >
                                    <i className='ri-add-line align-bottom me-1'></i>
                                    Import báo giá
                                </Button>
                            </>
                        }
                    />

                    <SearchFilter
                        pipelineStagesOptions={pipelineStagesOptions}
                        activeFilters={activeFilters}
                        onFilterClick={handleFilterClick}
                        onFilterRemove={removeFilter}
                        onFilterDrawerOpen={toggleFilterDrawer}
                        onFilterModalOpen={toggleFilterModal}
                        activeDisplay={activeDisplay}
                        onDisplayChange={toggleDisplay}
                    />

                    {activeDisplay === TYPE_DISPLAY.KANBAN && (
                        <KanbanProvider
                            pipelineStages={pipelineStages}
                            opportunities={{}}
                        >
                            <DndContainer pipelineStages={pipelineStages} />
                        </KanbanProvider>
                    )}

                    {activeDisplay === TYPE_DISPLAY.TABLE && (
                        <MantineTable
                            columns={columns}
                            data={listOpportunities}
                            isLoading={isLoading}
                            totalItems={listOpportunities.length ?? 0}
                        />
                    )}
                </Container>

                <FilterDrawer
                    isOpen={isFilterDrawerOpen}
                    onClose={toggleFilterDrawer}
                    filters={mockAdvancedFilters}
                />

                <FilterModal
                    isOpen={isFilterModalOpen}
                    onClose={toggleFilterModal}
                />
            </FormProvider>
        </div>
    );
}
