'use client';
import { useState } from 'react';
import { Card, CardBody, CardHeader, Container } from 'reactstrap';

import SelectHeader from './components/SelectHeader';
import TableData from './components/TableData';

const Goals = () => {
    const [yearDropdownOpen, setYearDropdownOpen] = useState(false);
    const [selectedYears, setSelectedYears] = useState<string[]>([]);
    const [time, setTime] = useState<string>('Năm');
    const [objective, setObjective] = useState<string>('Theo cá nhân');
    const toggleYearDropdown = () => setYearDropdownOpen(!yearDropdownOpen);

    return (
        <Container fluid>
            <Card>
                <CardHeader>
                    <SelectHeader
                        selectedYears={selectedYears}
                        setSelectedYears={setSelectedYears}
                        yearDropdownOpen={yearDropdownOpen}
                        toggleYearDropdown={toggleYearDropdown}
                        time={time}
                        setTime={setTime}
                        objective={objective}
                        setObjective={setObjective}
                    />
                </CardHeader>
                <CardBody>
                    <TableData
                        selectedYears={selectedYears}
                        time={time}
                        objective={objective}
                    />
                </CardBody>
            </Card>
        </Container>
    );
};

export default Goals;
