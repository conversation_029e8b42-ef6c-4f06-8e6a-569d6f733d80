'use client';

import { useCreateQuote } from '@/apis/quotes/quotes.api';
import { IQuotePayload } from '@/apis/quotes/quotes.type';
import { KEYS_TO_QUOTE } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import LinkQuoteForm from '../../_components/LinkQuoteForm';

const CreateLinkQuote = () => {
    const router = useRouter();

    const { mutate: createQuote } = useCreateQuote({
        onSuccess: () => {
            toast.success('Tạo mới báo giá thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX);
        },
        onError: (error) => {
            toast.error(
                <div className='flex flex-columns gap-1'>
                    <p className='p-0 m-0 mt-3'>Tạo mới báo giá thất bại</p>
                    <p className='p-0 m-0'>{error.detail}</p>
                </div>,
            );
        },
    });

    const handleCreateQuote = (data: IQuotePayload) => {
        if (data.customer) {
            delete data.customer;
        }
        if (data.partner) {
            delete data.partner;
        }

        if (data.expectedEndDate) {
            data.expectedEndDate = data.expectedEndDate.replace(' ', 'T');
        }

        const payload = convertFormValueToPayload(data, KEYS_TO_QUOTE);

        createQuote(payload);
    };

    const handleCancel = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX);
    };

    return (
        <LinkQuoteForm onSubmit={handleCreateQuote} onCancel={handleCancel} />
    );
};

export default CreateLinkQuote;
