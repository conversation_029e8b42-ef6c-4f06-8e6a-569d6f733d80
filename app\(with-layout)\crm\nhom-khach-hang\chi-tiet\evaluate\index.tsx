'use client';
import { useState } from 'react';
import {
    <PERSON><PERSON>,
    Card,
    CardBody,
    CardHeader,
    Col,
    Input,
    InputGroup,
    Nav,
    NavItem,
    NavLink,
    TabContent,
    TabPane,
} from 'reactstrap';

const Evaluate = () => {
    const [activeTab, setActiveTab] = useState('hoatdong');

    return (
        <Col lg={12}>
            <Card>
                <CardHeader className='p-0 border-0'>
                    <Nav tabs className='nav-tabs-custom'>
                        <NavItem>
                            <NavLink
                                style={{ cursor: 'pointer' }}
                                className={
                                    activeTab === 'hoatdong' ? 'active' : ''
                                }
                                onClick={() => setActiveTab('hoatdong')}
                            >
                                Hoạt động (9+)
                            </NavLink>
                        </NavItem>
                        <NavItem>
                            <NavLink
                                style={{ cursor: 'pointer' }}
                                className={
                                    activeTab === 'ghichu' ? 'active' : ''
                                }
                                onClick={() => setActiveTab('ghichu')}
                            >
                                Ghi chú
                            </NavLink>
                        </NavItem>
                        <NavItem>
                            <NavLink
                                style={{ cursor: 'pointer' }}
                                className={
                                    activeTab === 'email' ? 'active' : ''
                                }
                                onClick={() => setActiveTab('email')}
                            >
                                Email
                            </NavLink>
                        </NavItem>
                        <NavItem>
                            <NavLink
                                style={{ cursor: 'pointer' }}
                                className={
                                    activeTab === 'cuochen' ? 'active' : ''
                                }
                                onClick={() => setActiveTab('cuochen')}
                            >
                                Cuộc hẹn
                            </NavLink>
                        </NavItem>
                        <NavItem>
                            <NavLink
                                style={{ cursor: 'pointer' }}
                                className={
                                    activeTab === 'nhiemvu' ? 'active' : ''
                                }
                                onClick={() => setActiveTab('nhiemvu')}
                            >
                                Nhiệm vụ
                            </NavLink>
                        </NavItem>
                    </Nav>
                </CardHeader>
                <CardBody>
                    <div className='mb-4'>
                        <InputGroup>
                            <Input
                                placeholder='Tìm kiếm hoạt động'
                                className='py-2'
                            />
                            <Button color='light'>
                                <i className='ri-search-line'></i>
                            </Button>
                        </InputGroup>
                    </div>

                    <TabContent activeTab={activeTab}>
                        <TabPane tabId='hoatdong'>
                            <div className='activity-list'>
                                {/* All activities */}
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-file-text-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Ghi chú - Nguyễn Văn Sỹ
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Khách hàng yêu cầu gửi sớm hơn 1 ngày
                                    </p>
                                </div>

                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-mail-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Email: Trả lời: Báo giá sản phẩm
                                                tháng 2 - Người gửi:
                                                <EMAIL>
                                            </h6>
                                            <small className='text-muted'>
                                                Đến: NVBH Trung Hiếu
                                            </small>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Tôi đã nhận được thông tin
                                    </p>
                                </div>

                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-calendar-2-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Cuộc hẹn: Đề xuất sản phẩm -
                                                Người chủ trì: NVBH Hiền Lương
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                </div>

                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-checkbox-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Nhiệm vụ: Gửi Email cho khách
                                                hàng - Giao cho: NVBH Đỗ Tiến
                                                Dũng
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Gửi email thông báo cho khách hàng về
                                        sản phẩm mới...
                                    </p>
                                    <div className='mt-2'>
                                        <div className='d-flex align-items-center'>
                                            <small className='text-muted me-2'>
                                                Trạng thái:{' '}
                                            </small>
                                            <span className='badge bg-warning-subtle text-warning'>
                                                Chưa hoàn thành
                                            </span>
                                        </div>
                                        <div className='d-flex align-items-center mt-1'>
                                            <i className='ri-calendar-event-line text-muted me-1'></i>
                                            <small className='text-muted'>
                                                Ngày hết hạn: 20/01/2025 15:00
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='ghichu'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Tạo ghi chú
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-file-text-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Ghi chú - Nguyễn Văn Sỹ
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Khách hàng yêu cầu gửi sớm hơn 1 ngày
                                    </p>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='email'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Gửi email
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-mail-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Email: Trả lời: Báo giá sản phẩm
                                                tháng 2 - Người gửi:
                                                <EMAIL>
                                            </h6>
                                            <small className='text-muted'>
                                                Đến: NVBH Trung Hiếu
                                            </small>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Tôi đã nhận được thông tin
                                    </p>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='cuochen'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Tạo cuộc hẹn
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-calendar-2-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Cuộc hẹn: Đề xuất sản phẩm -
                                                Người chủ trì: NVBH Hiền Lương
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='nhiemvu'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Tạo nhiệm vụ
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-checkbox-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Nhiệm vụ: Gửi Email cho khách
                                                hàng - Giao cho: NVBH Đỗ Tiến
                                                Dũng
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Gửi email thông báo cho khách hàng về
                                        sản phẩm mới...
                                    </p>
                                    <div className='mt-2'>
                                        <div className='d-flex align-items-center'>
                                            <small className='text-muted me-2'>
                                                Trạng thái:{' '}
                                            </small>
                                            <span className='badge bg-warning-subtle text-warning'>
                                                Chưa hoàn thành
                                            </span>
                                        </div>
                                        <div className='d-flex align-items-center mt-1'>
                                            <i className='ri-calendar-event-line text-muted me-1'></i>
                                            <small className='text-muted'>
                                                Ngày hết hạn: 20/01/2025 15:00
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TabPane>
                    </TabContent>
                </CardBody>
            </Card>
        </Col>
    );
};

export default Evaluate;
