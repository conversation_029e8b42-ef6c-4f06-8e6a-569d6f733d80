'use client';

import React, { useState } from 'react';
import { Card, CardBody, Row, Col, Label, Table, Button } from 'reactstrap';
import { FormProvider, useForm } from 'react-hook-form';
import FormController from '@/components/common/FormController';

interface QuotationTemplateField {
    id: number;
    fieldCode: string;
    fieldName: string;
    dataType: string;
    description: string;
    isEnabled: boolean;
}

interface FormData {
    templateName: string;
    templateType: string;
}

const DetailQuotationForm = () => {
    const methods = useForm<FormData>();

    const [fields, setFields] = useState<QuotationTemplateField[]>([
        {
            id: 1,
            fieldCode: 'STT',
            fieldName: 'STT sản phẩm',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'STT sản phẩm',
            isEnabled: true,
        },
        {
            id: 2,
            fieldCode: 'Option xi hieu',
            fieldName: 'Option xi hieu',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Model-Option sản phẩm',
            isEnabled: true,
        },
        {
            id: 3,
            fieldCode: 'Description',
            fieldName: 'Description',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Mô tả sản phẩm',
            isEnabled: true,
        },
        {
            id: 4,
            fieldCode: 'List price',
            fieldName: 'List price',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Giá của hãng',
            isEnabled: true,
        },
        {
            id: 5,
            fieldCode: 'Quantity',
            fieldName: 'Quantity',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Số lượng sản phẩm',
            isEnabled: true,
        },
        {
            id: 6,
            fieldCode: 'Discount',
            fieldName: 'Discount',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Chiết khấu của hãng',
            isEnabled: true,
        },
        {
            id: 7,
            fieldCode: 'IMPORT Tax (%)',
            fieldName: 'IMPORT Tax (%)',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Thuế theo %',
            isEnabled: true,
        },
        {
            id: 8,
            fieldCode: 'IMPORT Tax',
            fieldName: 'IMPORT Tax',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Thuế theo giá',
            isEnabled: true,
        },
        {
            id: 9,
            fieldCode: 'NET Price',
            fieldName: 'NET Price',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Giá thực nhập',
            isEnabled: true,
        },
        {
            id: 10,
            fieldCode: 'Mark up',
            fieldName: 'Mark up',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Chiết lịch giá bán ra',
            isEnabled: true,
        },
        {
            id: 11,
            fieldCode: 'Unit Price',
            fieldName: 'Unit Price',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Đơn giá của sản phẩm',
            isEnabled: true,
        },
        {
            id: 12,
            fieldCode: 'Total price',
            fieldName: 'Total price',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Tổng giá sản phẩm',
            isEnabled: true,
        },
        {
            id: 13,
            fieldCode: 'Đơn giá (VND)',
            fieldName: 'Đơn giá (VND)',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Đơn giá theo đơn vị VND',
            isEnabled: true,
        },
        {
            id: 14,
            fieldCode: 'Đơn giá theo số (VND)',
            fieldName: 'Đơn giá theo số (VND)',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Đơn giá theo đơn vị VND',
            isEnabled: true,
        },
        {
            id: 15,
            fieldCode: 'Thành tiền',
            fieldName: 'Thành tiền',
            dataType: 'Chọn kiểu dữ liệu',
            description: 'Tổng tiền',
            isEnabled: true,
        },
    ]);

    const toggleField = (id: number) => {
        setFields(
            fields.map((field) =>
                field.id === id
                    ? { ...field, isEnabled: !field.isEnabled }
                    : field,
            ),
        );
    };

    const dataTypeOptions = [
        { value: 'text', label: 'Văn bản' },
        { value: 'number', label: 'Số' },
        { value: 'currency', label: 'Tiền tệ' },
        { value: 'percentage', label: 'Phần trăm' },
        { value: 'date', label: 'Ngày tháng' },
    ];

    const templateTypeOptions = [
        { value: 'classic', label: 'Classic' },
        { value: 'modern', label: 'Modern' },
        { value: 'minimal', label: 'Minimal' },
    ];

    return (
        <Card>
            <CardBody className='p-4'>
                <FormProvider {...methods}>
                    <Row className='mb-4'>
                        <Col md={6}>
                            <Label className='form-label'>
                                Tên mẫu báo giá{' '}
                                <span className='text-danger'>*</span>
                            </Label>
                            <FormController
                                controlType='textInput'
                                name='templateName'
                                placeholder='Mẫu báo giá cho đối tác châu Âu'
                            />
                        </Col>
                        <Col md={6}>
                            <Label className='form-label'>
                                Loại mẫu báo giá{' '}
                                <span className='text-danger'>*</span>
                            </Label>
                            <FormController
                                controlType='select'
                                name='templateType'
                                placeholder='Classic'
                                data={templateTypeOptions}
                            />
                        </Col>
                    </Row>

                    <div className='table-responsive'>
                        <Table className='table table-bordered table-hover mb-0'>
                            <thead className='table-light'>
                                <tr>
                                    <th style={{ width: '60px' }}>Mã trường</th>
                                    <th style={{ width: '200px' }}>
                                        Trường thông tin
                                    </th>
                                    <th style={{ width: '180px' }}>
                                        Kiểu dữ liệu
                                    </th>
                                    <th style={{ width: '150px' }}>Mô tả</th>
                                    <th style={{ width: '100px' }}>Ẩn/Hiện</th>
                                </tr>
                            </thead>
                            <tbody>
                                {fields.map((field) => (
                                    <tr key={field.id}>
                                        <td className='text-center align-middle'>
                                            {field.id}
                                        </td>
                                        <td className='align-middle'>
                                            {field.fieldName}
                                        </td>
                                        <td className='align-middle'>
                                            <select
                                                className='form-select form-select-sm'
                                                defaultValue=''
                                            >
                                                <option value=''>
                                                    Chọn kiểu dữ liệu
                                                </option>
                                                {dataTypeOptions.map(
                                                    (option) => (
                                                        <option
                                                            key={option.value}
                                                            value={option.value}
                                                        >
                                                            {option.label}
                                                        </option>
                                                    ),
                                                )}
                                            </select>
                                        </td>
                                        <td className='align-middle'>
                                            {field.description}
                                        </td>
                                        <td className='text-center align-middle'>
                                            <div className='d-flex align-items-center justify-content-center gap-2'>
                                                <div className='form-check form-switch'>
                                                    <input
                                                        className='form-check-input'
                                                        type='checkbox'
                                                        checked={
                                                            field.isEnabled
                                                        }
                                                        onChange={() =>
                                                            toggleField(
                                                                field.id,
                                                            )
                                                        }
                                                    />
                                                </div>
                                                <Button
                                                    color='link'
                                                    size='sm'
                                                    className='text-info p-0'
                                                    style={{
                                                        textDecoration: 'none',
                                                    }}
                                                >
                                                    <i className='ri-settings-3-line'></i>{' '}
                                                    Thiết lập cộng thức tính
                                                </Button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    </div>
                </FormProvider>
            </CardBody>
        </Card>
    );
};

export default DetailQuotationForm;
