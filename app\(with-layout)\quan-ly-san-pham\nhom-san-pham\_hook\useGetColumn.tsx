import { ResponseSearchProductGroup } from '@/apis/product-group/product-group.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';

import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '../_types/action.type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: ResponseSearchProductGroup | undefined,
    ) => void;
    page: string;
    onRestore?: (data: ResponseSearchProductGroup) => void;
}

const useGetColumn = ({
    onSelectedAction,
    page,
    onRestore,
}: GetColumnProps) => {
    const actions: DropdownAction<ResponseSearchProductGroup>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<ResponseSearchProductGroup>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên nhóm sản phẩm',
                enableHiding: false,
                size: 200,
                minSize: 250,
                maxSize: 200,
                Cell: ({ row }) => {
                    const hasChildren =
                        row.original.childrens &&
                        row.original.childrens.length > 0;
                    const isExpanded = row.getIsExpanded
                        ? row.getIsExpanded()
                        : false;
                    return (
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 4,
                            }}
                        >
                            <span
                                style={{
                                    paddingLeft: `${row.depth * 40}px`,
                                    width: 20,
                                    display: 'inline-flex',
                                    justifyContent: 'center',
                                }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    if (hasChildren && row.toggleExpanded) {
                                        row.toggleExpanded();
                                    }
                                }}
                            >
                                {hasChildren ? (
                                    isExpanded ? (
                                        <i className='ri-arrow-down-s-line' />
                                    ) : (
                                        <i className='ri-arrow-right-s-line' />
                                    )
                                ) : null}
                            </span>
                            <span>{row.original.name}</span>
                        </div>
                    );
                },
            },
            {
                accessorKey: 'description',
                header: 'Mô tả sản phẩm',
                size: 350,
                minSize: 200,
                maxSize: 400,
            },
            {
                // ...(columnDateDefault as MRT_ColumnDef<ResponseSearchProductGroup>),
                accessorKey: 'createdDateTime',
                header: 'Ngày tạo',
                size: 70,
                minSize: 50,
                maxSize: 100,
                Cell: ({ row }) => {
                    return (
                        <FormattedDateTimeWithFormat
                            date={row.original.createdDateTime}
                        />
                    );
                },
            },

            {
                ...(columnActionsDefault as MRT_ColumnDef<ResponseSearchProductGroup>),
                size: 70,
                minSize: 50,
                maxSize: 100,
                enableColumnActions: false,
                Cell: ({ row }) => {
                    if (page === 'list') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                                style={{ marginLeft: '-50px' }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    return;
                },
            },
        ],
        [actions, page, onRestore],
    );

    return columns;
};

export default useGetColumn;
