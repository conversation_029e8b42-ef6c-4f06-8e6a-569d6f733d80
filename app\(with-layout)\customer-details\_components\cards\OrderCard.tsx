import React from 'react';
import { Card, CardBody } from 'reactstrap';
import DataCardHeader from './DataCardHeader';
import HeaderRow from './HeaderRow';
import DataRow from './DataRow';
import ViewAllButton from './ViewAllButton';

const OrderCard: React.FC = () => (
    <div className='mb-4'>
        <Card className='bg-white'>
            <DataCardHeader title='Đơn hàng' count={1} />
            <CardBody>
                <div className='d-flex justify-content-between align-items-center mb-2'>
                    <HeaderRow title='Đơn hàng robot hàn' />
                </div>
                <DataRow label='Giá trị đơn hàng:' value='300,000,000' />
                <DataRow label='Ngày đặt hàng:' value='20/02/2025' />
                <div className='mb-2'>
                    <div className='text-muted'>Trạng thái:</div>
                    <div>
                        <span className='badge bg-success-subtle text-success'>
                            Đang giao hàng
                        </span>
                    </div>
                </div>
                <ViewAllButton />
            </CardBody>
        </Card>
    </div>
);

export default OrderCard;
