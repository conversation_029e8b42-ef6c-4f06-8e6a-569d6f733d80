'use client';
import {
    useDeletePartners,
    useSearchPartners,
} from '@/apis/partners/partners.api';
import {
    IPartnerParams,
    IPartnerResponse,
} from '@/apis/partners/partners.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import ComboboxSelectCumstomerControl from '@/components/common/FormController/ComboboxSelectCumstomerControl';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ImportFileModal from '@/components/common/ImportFile';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { ROUTES } from '@/lib/routes';
import { ACTIONS } from '@/types/actions.type';
import { getOneMonthAgo, getToday } from '@/utils/time';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<IPartnerResponse>,
        })),
    {
        ssr: false,
    },
);
const BusinessPartners = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);

    const methods = useForm<IPartnerParams>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        Name,
        CompanyId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SalePersonId,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'CompanyId',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SalePersonId',
            'SortField',
            'IsDescending',
        ],
    });

    const { mutate: deletePartners } = useDeletePartners({
        onSuccess: () => {
            toast.success('Xóa đối tác thương mại thành công');
            setModal(false);
            setSelectedNames([]);
            setSelectedIds([]);
            refetch();
        },
        onError: () => {
            toast.error('Xóa đối tác thương mại thất bại');
        },
    });

    const handleSelectedAction = (action: ACTIONS, row?: IPartnerResponse) => {
        if (!row) {
            console.error('No partner data provided');
            return;
        }

        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setModal(true);

                break;
            case ACTIONS.EDIT:
                router.push(ROUTES.CRM.PARTNERS.UPDATE.replace(':id', row.id));
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(ROUTES.CRM.PARTNERS.DETAIL.replace(':id', row.id));
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list-partners',
    });

    const { data, refetch, isLoading } = useSearchPartners({
        Name,
        CompanyId,
        SalePersonId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    const { items: listPartners = [], totalItems = 0 } = data ?? {};

    const handleConfimDelete = () => {
        deletePartners({ ids: selectedIds });
    };
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };

    const onCreateNew = () => {
        router.push(ROUTES.CRM.PARTNERS.CREATE);
    };

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={onCreateNew}
                        onImportExcel={() => setIsImportModalOpen(true)}
                    />
                </Col>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex gap-3'>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên đối tác thương mại...'
                                        />

                                        <ComboboxSelectCumstomerControl name='CompanyId' />
                                        <ComboboxSelectUserControl
                                            name='SalePersonId'
                                            placeholder='Nhân viên kinh doanh'
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <>
                                                <Button
                                                    style={{
                                                        backgroundColor: 'red',
                                                        border: 'none',
                                                        color: 'white',
                                                    }}
                                                    onClick={() => {
                                                        setModal(true);
                                                    }}
                                                >
                                                    Xóa ({selectedIds.length})
                                                </Button>
                                            </>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES.CRM.PARTNERS
                                                                .RESTORE,
                                                        )
                                                    }
                                                >
                                                    Khôi phục
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listPartners}
                            totalItems={Number(totalItems)}
                            isLoading={isLoading}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listPartners.findIndex(
                                                    (
                                                        contact: IPartnerResponse,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listPartners.findIndex(
                                                            (
                                                                contact: IPartnerResponse,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listPartners[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>

            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='đối tác thương mại'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};

export default BusinessPartners;
