import { IContactResponse } from '@/apis/contact/contact.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: IContactResponse | undefined,
    ) => void;
    page: string;
    onRestore?: (data: IContactResponse | undefined) => void;
}
const useGetColumn = ({
    onSelectedAction,
    page,
    onRestore,
}: GetColumnProps) => {
    const actions: DropdownAction<IContactResponse>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );
    const columns = useMemo<MRT_ColumnDef<IContactResponse>[]>(
        () => [
            {
                id: 'name',
                accessorKey: 'name',
                header: 'Họ và tên',
                enableSorting: false,
                enablePinning: true,
            },
            {
                accessorKey: 'companyName',
                header: 'Khách hàng',
                grow: true,
            },
            {
                accessorKey: 'departmentName',
                header: 'Phòng ban',
            },
            {
                accessorKey: 'positionName',
                header: 'Chức vụ',
            },
            page === 'restore'
                ? { accessorKey: 'salePerson', header: 'Người thực hiện' }
                : {
                      accessorKey: 'roleName',
                      header: 'Vai trò',
                  },

            page === 'restore'
                ? {
                      accessorKey: 'deletedTime',
                      header: 'Ngày xóa',
                      Cell: ({ row }) => (
                          <FormattedDateTimeWithFormat
                              date={row.original.deletedTime}
                          />
                      ),
                  }
                : {
                      accessorKey: 'createdOn',
                      header: 'Ngày tạo',
                      Cell: ({ row }) => (
                          <FormattedDateTimeWithFormat
                              date={row.original.createdOn}
                          />
                      ),
                  },

            {
                ...(columnActionsDefault as MRT_ColumnDef<IContactResponse>),
                enableColumnActions: false,

                Cell: ({ row }) => {
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{
                                    fontSize: '13px',
                                    color: '#0ab39c',
                                }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    if (page === 'list-contact') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    center={true}
                                />
                            </div>
                        );
                    }
                },
            },
        ],
        [actions, onRestore, page],
    );

    return columns;
};

export default useGetColumn;
