'use client';
import {
    useListRestore,
    useRestoreSupplier,
} from '@/apis/supplier/supplier.api';
import {
    ResponseSupplier,
    SearchSupplier,
} from '@/apis/supplier/supplier.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import ModalRestore from '@/components/common/Modal/ModalRestore';
import { ROUTES } from '@/lib/routes';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    But<PERSON>,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';

import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import useGetColumn from '../_hook/useGetColumn';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ResponseSupplier>,
        })),
    {
        ssr: false,
    },
);
const RestoreSupplier = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const methods = useForm<SearchSupplier>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            IsDescending: true,
        },
    });
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const { control, setValue } = methods;
    const [
        Name,
        UserNameCreated,
        PageNumber,
        PageSize,
        IsDescending,
        FromDate,
        ToDate,
        SortField,
    ] = useWatch({
        control,
        name: [
            'Name',
            'UserNameCreated',
            'PageNumber',
            'PageSize',
            'IsDescending',
            'FromDate',
            'ToDate',
            'SortField',
        ],
    });

    const handleOneRestore = (data: ResponseSupplier) => {
        setSelectedIds([data.id]);
        setSelectedNames([data.name]);
        setModal(true);
    };
    const columns = useGetColumn({
        page: 'restore',
        onRestore: handleOneRestore,
    });
    const { data, refetch } = useListRestore({
        Name,
        UserNameCreated,
        PageNumber,
        PageSize,
        IsDescending,
        FromDate,
        ToDate,
        SortField,
    });
    const { items: listSupplier = [], totalItems } = data ?? {};

    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    const handleCreate = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.CREATE);
    };
    const { mutate: restoreSupplier } = useRestoreSupplier({
        onSuccess: () => {
            toast.success('Khôi phục nhà cung cấp thành công');
            setModal(false);
            setSelectedIds([]);
            setSelectedNames([]);
            refetch();
        },
        onError: () => {
            toast.error('Khôi phục nhà cung cấp thất bại');
        },
    });
    const handleConfimRestore = () => {
        restoreSupplier(selectedIds);
    };
    const handleDelete = () => {
        setModal(true);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col md={12}>
                    <ButtonHeader
                        showExportButton={false}
                        onCreateNew={handleCreate}
                    />
                </Col>
                <Col md={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên nhà cung cấp...'
                                    />
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <Button
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => handleDelete()}
                                            >
                                                Khôi phục ({selectedIds.length})
                                            </Button>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES
                                                                .PRODUCT_MANAGEMENT
                                                                .SUPPLIERS
                                                                .INDEX,
                                                        )
                                                    }
                                                >
                                                    Nhà cung cấp
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listSupplier}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listSupplier.findIndex(
                                                    (
                                                        contact: ResponseSupplier,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listSupplier.findIndex(
                                                            (
                                                                contact: ResponseSupplier,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listSupplier[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalRestore
                onRestore={handleConfimRestore}
                onClose={handleClose}
                isOpen={modal}
                page='nhà cung cấp'
                data={selectedNames}
            />
        </FormProvider>
    );
};
export default RestoreSupplier;
