'use client';

import { useLogin } from '@/apis/auth/auth.api';
import { IAuth } from '@/apis/auth/auth.type';
import FormController from '@/components/common/FormController';
import InputWithFloatingLabelControl from '@/components/common/FormController/InputWithFloatingLabelControl';
import { ROUTES } from '@/lib/routes';
import { changeInfoUserAuth } from '@/slices/auth/reducer';
import { AppDispatch } from '@/store/store';
import { yupResolver } from '@hookform/resolvers/yup';
import { Anchor, Group } from '@mantine/core';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { Button, Card, CardBody, Col, Container, Row } from 'reactstrap';
import * as yup from 'yup';
import { InferType } from 'yup';
import logoAsic from '../../../assets/images/logos/logo-asic.png';

const Login = () => {
    const router = useRouter();
    const dispatch = useDispatch<AppDispatch>();

    const { mutate: login, isPending } = useLogin({
        onSuccess: (data) => {
            dispatch(changeInfoUserAuth(data));
            router.push(ROUTES.DASHBOARD.CRM.INDEX);
        },
    });

    const defaultValues = useMemo(() => {
        return {
            username: 'XUANTHANH61',
            password: 'Abc@123',
        };
    }, []);

    const schema = useMemo(() => {
        return yup.object({
            username: yup
                .string()
                .required('Tên đăng nhập là bắt buộc')
                .min(4, 'Tên đăng nhập phải có ít nhất 4 ký tự')
                .max(20, 'Tên đăng nhập không được quá 20 ký tự'),
            password: yup
                .string()
                .required('Mật khẩu là bắt buộc')
                .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
                .max(20, 'Mật khẩu không được quá 20 ký tự'),
        });
    }, []);

    type FormData = InferType<typeof schema>;

    const methods = useForm<FormData>({
        resolver: yupResolver(schema),
        defaultValues,
    });

    const { handleSubmit } = methods;

    const handleLogin = (data: FormData) => {
        login(data as IAuth);
    };

    return (
        <div className='auth-page-wrapper pt-5'>
            <div
                className='auth-one-bg-position auth-one-bg'
                id='auth-particles'
            >
                <div className='bg-overlay'></div>

                <div className='shape'>
                    <svg
                        xmlns='http://www.w3.org/2000/svg'
                        version='1.1'
                        xmlnsXlink='http://www.w3.org/1999/xlink'
                        viewBox='0 0 1440 120'
                    >
                        <path d='M 0,36 C 144,53.6 432,123.2 720,124 C 1008,124.8 1296,56.8 1440,40L1440 140L0 140z'></path>
                    </svg>
                </div>
                <div
                    className='auth-page-content mt-lg-5'
                    style={{ position: 'absolute', top: '50%', bottom: '50%' }}
                >
                    <Container>
                        <Row className='justify-content-center'>
                            <Col md={8} lg={6} xl={5}>
                                <Card className='mt-4'>
                                    <CardBody className='p-4'>
                                        <div className='mx-auto auth-logo d-flex justify-content-center'>
                                            <Image
                                                src={logoAsic}
                                                alt='Logo Asic'
                                                width={300}
                                                height={100}
                                                style={{ objectFit: 'contain' }}
                                            />
                                        </div>

                                        <FormProvider {...methods}>
                                            <div className='p-2 mt-4 d-flex flex-column gap-3'>
                                                <InputWithFloatingLabelControl
                                                    name='username'
                                                    controlType='textInput'
                                                    label='Tên đăng nhập'
                                                    placeholder='Nhập tên đăng nhập'
                                                />

                                                <InputWithFloatingLabelControl
                                                    name='password'
                                                    controlType='passwordInput'
                                                    label='Mật khẩu'
                                                    placeholder='Nhập mật khẩu đăng nhập'
                                                />

                                                <Group
                                                    justify='space-between'
                                                    align='flex-start'
                                                >
                                                    <FormController
                                                        name='checkRememberMe'
                                                        controlType='checkbox'
                                                        label='Nhớ mật khẩu'
                                                    />

                                                    <Anchor
                                                        href='#'
                                                        onClick={(event) =>
                                                            event.preventDefault()
                                                        }
                                                        fw={500}
                                                        fz='xs'
                                                    >
                                                        Quên mật khẩu
                                                    </Anchor>
                                                </Group>
                                            </div>

                                            <div className='mt-4'>
                                                <Button
                                                    color='success'
                                                    className='btn btn-success w-100'
                                                    loading={!!isPending}
                                                    disabled={isPending}
                                                    onClick={() =>
                                                        void handleSubmit(
                                                            handleLogin,
                                                        )()
                                                    }
                                                >
                                                    Đăng nhập
                                                </Button>
                                            </div>
                                        </FormProvider>
                                    </CardBody>
                                </Card>
                            </Col>
                        </Row>
                    </Container>
                </div>
            </div>
        </div>
    );
};

export default Login;
