import DropdownActionMenu from '@/components/common/DropdownActionMenu';
import FormController from '@/components/common/FormController';
import InputDateRangePickerControl from '@/components/common/FormController/InputDateRangePickerControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { ROUTES } from '@/lib/routes';
import { Option } from '@/types/app.type';
import { useRouter } from 'next/navigation';
import {
    Button,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    UncontrolledDropdown,
} from 'reactstrap';
import { Filter, TYPE_DISPLAY } from '../../types';

interface SearchFilterProps {
    pipelineStagesOptions: Option[];
    activeFilters: Filter[];
    onFilterClick: (filter: string) => void;
    onFilterRemove: (filter: string) => void;
    onFilterDrawerOpen: () => void;
    onFilterModalOpen: () => void;
    activeDisplay: TYPE_DISPLAY;
    onDisplayChange: (display: TYPE_DISPLAY) => void;
}

export const SearchFilter = ({
    pipelineStagesOptions,
    activeFilters,
    onFilterClick,
    onFilterRemove,
    onFilterDrawerOpen,
    onFilterModalOpen,
    activeDisplay,
    onDisplayChange,
}: SearchFilterProps) => {
    const router = useRouter();

    return (
        <div className='d-flex align-items-center gap-2 mb-3 bg-white rounded p-3'>
            <InputSearchNameWithApiControl
                name='Name'
                placeholder='Tìm kiếm theo tên cơ hội hoặc tên khách hàng...'
            />

            <InputDateRangePickerControl
                nameFrom='FromDate'
                nameTo='ToDate'
                allowFutureDates={false}
            />

            <FormController
                controlType='select'
                name='PipelineStageId'
                placeholder='Giai đoạn'
                data={pipelineStagesOptions}
                style={{ width: '200px' }}
            />

            <div className='d-flex gap-2 align-items-center'>
                {activeFilters.map((filter) => (
                    <div
                        key={filter.id}
                        className='d-flex align-items-center px-2 py-1'
                        style={{
                            backgroundColor: '#e6f7f5',
                            borderRadius: '4px',
                            color: '#0ab39c',
                            fontSize: '13px',
                        }}
                    >
                        <i className='ri-filter-3-line me-1'></i>
                        {filter.name}
                        <i
                            className='ri-close-line ms-2'
                            style={{ cursor: 'pointer' }}
                            onClick={() => onFilterRemove(filter.id)}
                        ></i>
                    </div>
                ))}
            </div>

            <UncontrolledDropdown>
                <DropdownToggle
                    style={{
                        backgroundColor: '#daf4f0',
                        color: '#0ab39c',
                        border: 'none',
                        height: '36px',
                    }}
                >
                    <i className='ri-filter-3-line me-1'></i>
                    Bộ lọc nâng cao
                </DropdownToggle>
                <DropdownMenu className='p-2' style={{ minWidth: '200px' }}>
                    <DropdownItem
                        className='d-flex align-items-center px-2 py-1'
                        style={{ color: '#212529' }}
                        onClick={() => onFilterClick('Ngày tạo')}
                    >
                        <i className='ri-calendar-line me-2'></i>
                        Ngày tạo
                    </DropdownItem>
                    <DropdownItem
                        className='d-flex align-items-center px-2 py-1'
                        style={{ color: '#212529' }}
                        onClick={() => onFilterClick('Theo KH')}
                    >
                        <i className='ri-user-line me-2'></i>
                        Theo KH
                    </DropdownItem>
                    <DropdownItem
                        className='d-flex align-items-center px-2 py-1'
                        style={{ color: '#212529' }}
                        onClick={() => onFilterClick('Theo NVKD')}
                    >
                        <i className='ri-team-line me-2'></i>
                        Theo NVKD
                    </DropdownItem>
                    <DropdownItem
                        className='d-flex align-items-center px-2 py-1'
                        style={{ color: '#212529' }}
                        onClick={onFilterDrawerOpen}
                    >
                        <i className='ri-filter-3-line me-2'></i>
                        Xem tất cả bộ lọc
                    </DropdownItem>
                </DropdownMenu>
            </UncontrolledDropdown>

            <div className='d-flex gap-2 ms-auto'>
                <Button
                    style={{
                        backgroundColor: '#f3f6f9',
                        border: 'none',
                        padding: '0',
                        color:
                            activeDisplay === TYPE_DISPLAY.KANBAN
                                ? '#0ab39c'
                                : '#878a99',
                        height: '32px',
                        width: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '4px',
                    }}
                    className='btn-icon'
                    title='Dạng bảng'
                    onClick={() => onDisplayChange(TYPE_DISPLAY.KANBAN)}
                >
                    <i className='ri-layout-grid-fill fs-15'></i>
                </Button>
                <Button
                    style={{
                        backgroundColor: '#f3f6f9',
                        border: 'none',
                        padding: '0',
                        color:
                            activeDisplay === TYPE_DISPLAY.TABLE
                                ? '#0ab39c'
                                : '#878a99',
                        height: '32px',
                        width: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '4px',
                    }}
                    className='btn-icon'
                    title='Dạng danh sách'
                    onClick={() => onDisplayChange(TYPE_DISPLAY.TABLE)}
                >
                    <i className='ri-list-unordered fs-15'></i>
                </Button>
                <Button
                    style={{
                        backgroundColor: '#f3f6f9',
                        border: 'none',
                        padding: '0',
                        color: '#878a99',
                        height: '32px',
                        width: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '4px',
                    }}
                    className='btn-icon'
                    title='Bộ lọc'
                    onClick={onFilterModalOpen}
                >
                    <i className='ri-filter-3-line fs-15'></i>
                </Button>
                <DropdownActionMenu
                    actions={[
                        {
                            icon: 'ri-refresh-line',
                            label: 'Khôi phục cơ hội',
                            onClick: () =>
                                router.push(
                                    ROUTES.CRM.SALES_OPPORTUNITIES.RESTORE,
                                ),
                        },
                        {
                            icon: 'ri-git-branch-line',
                            label: 'Cấu hình giai đoạn',
                            onClick: () =>
                                router.push(
                                    ROUTES.CRM.SALES_OPPORTUNITIES
                                        .CONFIGURATION,
                                ),
                            className: 'px-3 py-2',
                            customRender: (label: string) => (
                                <div className='d-flex align-items-center justify-content-between w-100'>
                                    <div className='d-flex align-items-center'>
                                        <i className='ri-git-branch-line me-2'></i>
                                        <span style={{ fontSize: '13px' }}>
                                            {label}
                                        </span>
                                    </div>
                                    <i className='ri-information-line text-muted'></i>
                                </div>
                            ),
                        },
                    ]}
                    buttonStyle={{
                        backgroundColor: '#f3f6f9',
                        border: 'none',
                        padding: '0',
                        height: '32px',
                        width: '32px',
                        borderRadius: '4px',
                    }}
                    toggleIcon='ri-settings-4-fill fs-15'
                />
            </div>
        </div>
    );
};
