import { IDealDetail } from '@/apis/opportunity/opportunity.type';

interface Tab4Props {
    data?: IDealDetail;
}

const Tab4 = ({ data }: Tab4Props) => {
    const products = data?.products?.products || [];
    const productClassics = data?.products?.productClassics || [];

    return (
        <div>
            {products.length > 0 && (
                <div className='mb-4'>
                    <h5 className='mb-3'>DANH MỤC SẢN PHẨM</h5>
                    <div className='table-responsive'>
                        <table className='table table-bordered'>
                            <thead className='table-light'>
                                <tr>
                                    <th style={{ width: '100px' }}>
                                        <PERSON><PERSON> thứ tự
                                    </th>
                                    <th>Tên sản phẩm</th>
                                    <th style={{ width: '200px' }}>
                                        Kiểu triển khai
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {products.map((product, index) => (
                                    <tr key={product.id}>
                                        <td className='text-center'>
                                            {index + 1}
                                        </td>
                                        <td>{product.name}</td>
                                        <td>{product.installationTypeName}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {productClassics.length > 0 && (
                <div className='mb-4'>
                    <h5 className='mb-3'>SẢN PHẨM CLASSIC</h5>
                    <div className='table-responsive'>
                        <table className='table table-bordered'>
                            <thead className='table-light'>
                                <tr>
                                    <th style={{ width: '100px' }}>
                                        Số thứ tự
                                    </th>
                                    <th style={{ width: '150px' }}>
                                        Mã sản phẩm
                                    </th>
                                    <th>Mô tả</th>
                                    <th style={{ width: '200px' }}>
                                        Tùy chọn sản phẩm
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {productClassics.map((product, index) => (
                                    <tr key={product.id}>
                                        <td className='text-center'>
                                            {index + 1}
                                        </td>
                                        <td>{product.code}</td>
                                        <td>{product.description}</td>
                                        <td>{product.productOption}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {products.length === 0 && productClassics.length === 0 && (
                <div className='text-center py-4'>
                    <p className='text-muted'>Không có dữ liệu sản phẩm</p>
                </div>
            )}

            {(data?.products?.startPoCDate || data?.products?.endPoCDate) && (
                <div className='mt-4'>
                    <h6>Thông tin PoC</h6>
                    <div className='row'>
                        {data.products.startPoCDate && (
                            <div className='col-md-6'>
                                <strong>Ngày bắt đầu PoC:</strong>{' '}
                                {new Date(
                                    data.products.startPoCDate,
                                ).toLocaleDateString('vi-VN')}
                            </div>
                        )}
                        {data.products.endPoCDate && (
                            <div className='col-md-6'>
                                <strong>Ngày kết thúc PoC:</strong>{' '}
                                {new Date(
                                    data.products.endPoCDate,
                                ).toLocaleDateString('vi-VN')}
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default Tab4;
