'use client';

import { useSearchContacts } from '@/apis/contact/contact.api';
import { SearchContact } from '@/apis/contact/contact.type';
import CollapseApp from '@/components/common/CollapseApp';
import { useState } from 'react';
import { Col, Row } from 'reactstrap';
import AddContactModal from '../../AddContactModal';
import ContactsTable from './ContactsTable';

const LinkedInfo = () => {
    const searchContact: SearchContact = {
        PageNumber: 1,
        PageSize: 100,
    };

    const [isAddContactModalOpen, setIsAddContactModalOpen] = useState(false);

    const toggleAddContactModal = () => {
        setIsAddContactModalOpen(!isAddContactModalOpen);
    };

    const { data: contactOptionsData, refetch: refetchContacts } =
        useSearchContacts(searchContact);

    const contactList = contactOptionsData?.items || [];

    return (
        <CollapseApp title='THÔNG TIN LIÊN KẾT'>
            <Row className='g-3 justify-content-around'>
                <Col md='11'>
                    <ContactsTable
                        contactList={contactList}
                        toggleAddContactModal={toggleAddContactModal}
                        title='Phòng mua hàng'
                        buttonText='Tạo mới cá nhân'
                    />
                </Col>
                <AddContactModal
                    isOpen={isAddContactModalOpen}
                    toggle={toggleAddContactModal}
                    onSuccess={() => {
                        refetchContacts();
                    }}
                />
            </Row>
        </CollapseApp>
    );
};

export default LinkedInfo;
