import {
    IQuoteDetailResponse,
    IProductDisplayDeal,
} from '@/apis/quotes/quotes.type';

interface Tab3Props {
    data?: IQuoteDetailResponse;
}

const Tab3 = ({ data }: Tab3Props) => {
    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('en-US').format(value);
    };

    const calculateItemValues = (item: IProductDisplayDeal, index: number) => {
        const listPrice = item.listPrice || 0;
        const quantity = item.quantity || 0;
        const standardDiscount = item.standardDiscount || 0;
        const extendedDiscountRequest = item.extendedDiscountRequest || 0;

        const totalDiscount = standardDiscount + extendedDiscountRequest;

        const netPriceUSD = listPrice * (1 - totalDiscount / 100);

        return {
            stt: index + 1,
            optionCode: item.modelCode || '',
            description: item.description || '',
            listPrice,
            quantity,
            standardDiscount,
            extendedDiscountRequest,
            netPriceUSD: netPriceUSD,
        };
    };

    const calculateTotals = () => {
        if (!data?.quoteItems) {
            return {
                totalNetPrice: 0,
                discountToCustomer: 0,
                finalTotalPrice: 0,
            };
        }

        const totalNetPrice = data.quoteItems.reduce((sum, item) => {
            const listPrice = item.listPrice || 0;
            const quantity = item.quantity || 0;
            const standardDiscount = item.standardDiscount || 0;
            const extendedDiscountRequest = item.extendedDiscountRequest || 0;
            const totalDiscount = standardDiscount + extendedDiscountRequest;
            const netPriceUSD = listPrice * (1 - totalDiscount / 100);
            return sum + netPriceUSD * quantity;
        }, 0);

        const discountToCustomer = data.discountToCustomer || 0;
        const finalTotalPrice = totalNetPrice * (1 - discountToCustomer / 100);

        return {
            totalNetPrice,
            discountToCustomer,
            finalTotalPrice,
        };
    };

    const totals = calculateTotals();

    return (
        <div className='p-3'>
            <div className='table-responsive'>
                <table className='table table-bordered'>
                    <thead style={{ backgroundColor: '#f8f9fa' }}>
                        <tr>
                            <th style={{ width: '60px', textAlign: 'center' }}>
                                STT
                            </th>
                            <th style={{ width: '120px' }}>Option kí hiệu</th>
                            <th style={{ width: '200px' }}>Description</th>
                            <th style={{ width: '100px', textAlign: 'right' }}>
                                List Price
                            </th>
                            <th style={{ width: '80px', textAlign: 'center' }}>
                                Quantity
                            </th>
                            <th style={{ width: '100px', textAlign: 'center' }}>
                                Standard Discount
                            </th>
                            <th style={{ width: '120px', textAlign: 'center' }}>
                                Ex. dist. Request (%)
                            </th>
                            <th style={{ width: '120px', textAlign: 'right' }}>
                                NET price (USD)
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {data?.quoteItems && data.quoteItems.length > 0 ? (
                            data.quoteItems.map((item, index) => {
                                const calculatedItem = calculateItemValues(
                                    item,
                                    index,
                                );
                                return (
                                    <tr key={index}>
                                        <td style={{ textAlign: 'center' }}>
                                            {calculatedItem.stt}
                                        </td>
                                        <td>{calculatedItem.optionCode}</td>
                                        <td>{calculatedItem.description}</td>
                                        <td style={{ textAlign: 'right' }}>
                                            {formatNumber(
                                                calculatedItem.listPrice,
                                            )}
                                        </td>
                                        <td style={{ textAlign: 'center' }}>
                                            {calculatedItem.quantity}
                                        </td>
                                        <td style={{ textAlign: 'center' }}>
                                            {calculatedItem.standardDiscount}
                                        </td>
                                        <td style={{ textAlign: 'center' }}>
                                            {
                                                calculatedItem.extendedDiscountRequest
                                            }
                                        </td>
                                        <td style={{ textAlign: 'right' }}>
                                            {formatNumber(
                                                calculatedItem.netPriceUSD,
                                            )}
                                        </td>
                                    </tr>
                                );
                            })
                        ) : (
                            <tr>
                                <td
                                    colSpan={8}
                                    style={{
                                        textAlign: 'center',
                                        padding: '20px',
                                        color: '#6c757d',
                                    }}
                                >
                                    Chưa có dữ liệu về sản phẩm
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>

            <div className='mt-4'>
                <table
                    className='table table-bordered'
                    style={{ width: '50%', marginLeft: 'auto' }}
                >
                    <tbody>
                        <tr>
                            <td
                                style={{
                                    fontWeight: 'bold',
                                    backgroundColor: '#f8f9fa',
                                    padding: '12px',
                                }}
                            >
                                Total NET price
                            </td>
                            <td style={{ textAlign: 'right', padding: '12px' }}>
                                {formatNumber(totals.totalNetPrice)}
                            </td>
                        </tr>
                        <tr>
                            <td
                                style={{
                                    fontWeight: 'bold',
                                    backgroundColor: '#f8f9fa',
                                    padding: '12px',
                                }}
                            >
                                Discount to customer
                            </td>
                            <td style={{ textAlign: 'right', padding: '12px' }}>
                                {totals.discountToCustomer}%
                            </td>
                        </tr>
                        <tr>
                            <td
                                style={{
                                    fontWeight: 'bold',
                                    backgroundColor: '#f8f9fa',
                                    padding: '12px',
                                }}
                            >
                                Final total CIP price
                            </td>
                            <td
                                style={{
                                    textAlign: 'right',
                                    padding: '12px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {formatNumber(totals.finalTotalPrice)}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default Tab3;
