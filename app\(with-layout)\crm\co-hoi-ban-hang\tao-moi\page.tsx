'use client';

import { useCreateDeal } from '@/apis/opportunity/opportunity.api';
import { IOpportunity } from '@/apis/opportunity/opportunity.type';
import { KEYS_TO_OPPORTUNITY } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import FormOpportunity from '../_components/FormOpportunity';

const CreateOpportunity = () => {
    const router = useRouter();

    const { mutate: createDeal } = useCreateDeal({
        onSuccess: () => {
            toast.success('Tạo mới cơ hội thành công');
            router.push(ROUTES.CRM.SALES_OPPORTUNITIES.INDEX);
        },
        onError: () => {
            toast.error('Tạo mới cơ hội thất bại');
        },
    });

    const handleCreateDeal = (data: IOpportunity) => {
        data.priority = Number(data.priority);
        data.project.watcherUserIds = [data.project.watcherUserIds as string];

        const payload = convertFormValueToPayload(data, KEYS_TO_OPPORTUNITY);

        createDeal(payload as IOpportunity);
    };

    return <FormOpportunity onSubmit={handleCreateDeal} />;
};

export default CreateOpportunity;
