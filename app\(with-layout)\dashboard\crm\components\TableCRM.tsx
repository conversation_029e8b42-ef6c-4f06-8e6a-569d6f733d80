import { Card, Table } from 'reactstrap';

const TableCRM = () => {
    const customers = [
        {
            customerName: 'Tổng Công ty Viễn thông MobiPhone',
            contractName: 'Hợp đồng mua bán hàng hóa',
            status: 'Chưa thanh toán',
            revenue: '200.000.000',
            dueDate: '01/04/2025',
        },
        {
            customerName: 'TMA Solutions',
            contractName: 'Hợp đồng cung cấp dịch vụ',
            status: 'Thanh toán một phần',
            revenue: '150.000.000',
            dueDate: '04/04/2025',
        },
        {
            customerName: 'Công ty Garena',
            contractName: 'Hợp đồng hợp tác kinh doanh',
            status: 'Chưa thanh toán',
            revenue: '130.000.000',
            dueDate: '02/04/2025',
        },
        {
            customerName: 'Ngân hàng TMCP Vietcombank',
            contractName: 'Hợp đồng thương mại',
            status: 'Đã thanh toán',
            revenue: '100.000.000',
            dueDate: '02/04/2025',
        },
        {
            customerName: 'Công ty CP Công nghệ mới',
            contractName: 'Hợp đồng phát triển phần mềm',
            status: 'Đã thanh toán',
            revenue: '97.000.000',
            dueDate: '03/04/2025',
        },
    ];

    return (
        <Card className='table-responsive' style={{ padding: '20px' }}>
            <h5 className='mb-3'>
                Danh sách các khách hàng có doanh thu cao nhất
            </h5>
            <Table
                bordered
                className='table-nowrap align-middle mb-0'
                style={{ fontSize: '11px' }}
            >
                <thead>
                    <tr>
                        <th className='align-middle'>Tên khách hàng</th>
                        <th className='align-middle'>Tên hợp đồng</th>
                        <th className='align-middle'>Trạng thái</th>
                        <th className='align-middle'>Doanh thu</th>
                        <th className='align-middle'>Ngày đặt hàng</th>
                    </tr>
                </thead>
                <tbody>
                    {customers.map((customer, index) => (
                        <tr key={index}>
                            <td>{customer.customerName}</td>
                            <td>{customer.contractName}</td>
                            <td>
                                <span
                                    className={`badge ${
                                        customer.status === 'Đã thanh toán'
                                            ? 'bg-success'
                                            : customer.status ===
                                                'Thanh toán một phần'
                                              ? 'bg-warning'
                                              : 'bg-danger'
                                    }`}
                                >
                                    {customer.status}
                                </span>
                            </td>
                            <td>{customer.revenue}</td>
                            <td>{customer.dueDate}</td>
                        </tr>
                    ))}
                </tbody>
            </Table>
        </Card>
    );
};

export default TableCRM;
