import http, { ApiResponse } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ApiResponseList } from '../../lib/apiBase';
import {
    GetListCustomerGroupsParams,
    ICustomerGroups,
    ICustomerGroupsDetail,
    ICustomerGroupsResponse,
    PayloadDeleteCompanies,
    PayloadRestoresCustomerGroups,
    ResponseSearchRestoresCustomerGroups,
    SearchRestoresCustomerGroups,
} from './customer-groups.type';

const URI = '/api/v1.0/CustomerGroups';

export const customerGroupsKey = {
    GET_LIST_CUSTOMER_GROUPS: 'GET_LIST_CUSTOMER_GROUPS',
    GET_DETAIL_CUSTOMER_GROUPS: 'GET_DETAIL_CUSTOMER_GROUPS',
    DELETE_CUSTOMER_GROUPS: 'DELETE_CUSTOMER_GROUPS',
    SEARCH_RESTORE_CUSTOMER_GROUPS: 'SEARCH_RESTORE_CUSTOMER_GROUPS',
    RESTORE_CUSTOMER_GROUPS: 'RESTORE_CUSTOMER_GROUPS',
};

export const customerGroupsUri = {
    getListCustomerGroups: `${URI}/search`,
    createCustomerGroups: `${URI}`,
    updateCustomerGroups: `${URI}/:id`,
    deleteCustomerGroups: `${URI}/delete-multiple`,
    getDetailCustomerGroups: `${URI}/:id`,
    searchRestoreCustomerGroups: `${URI}/search-restores`,
    restoreCustomerGroups: `${URI}/restore-multiple`,
};

export const customerGroupsApis = {
    getListCustomerGroups(params: GetListCustomerGroupsParams) {
        return http.get<ApiResponseList<ICustomerGroupsResponse[]>>(
            customerGroupsUri.getListCustomerGroups,
            {
                params,
            },
        );
    },
    getDetailCustomerGroups(id: string) {
        return http.get<ICustomerGroupsDetail>(
            customerGroupsUri.getDetailCustomerGroups.replace(':id', id),
        );
    },

    createCustomerGroups(payload: ICustomerGroups) {
        return http.post<ApiResponse<ICustomerGroups>>(
            customerGroupsUri.createCustomerGroups,
            payload,
        );
    },
    deleteCustomerGroups(payload: PayloadDeleteCompanies) {
        return http.post<ApiResponse<GetListCustomerGroupsParams>>(
            customerGroupsUri.deleteCustomerGroups,
            payload,
        );
    },
    searchRestoreCustomerGroups(params: SearchRestoresCustomerGroups) {
        return http.get<
            ApiResponseList<ResponseSearchRestoresCustomerGroups[]>
        >(customerGroupsUri.searchRestoreCustomerGroups, {
            params,
        });
    },
    restoreCustomerGroup(payload: PayloadRestoresCustomerGroups) {
        return http.post<ApiResponse<SearchRestoresCustomerGroups>>(
            customerGroupsUri.restoreCustomerGroups,
            payload,
        );
    },
};

export const useGetListCustomerGroups = (
    params: GetListCustomerGroupsParams,
) => {
    return useQuery({
        queryKey: [customerGroupsKey.GET_LIST_CUSTOMER_GROUPS, params],
        queryFn: () => customerGroupsApis.getListCustomerGroups(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useGetDetailCustomerGroups = (id: string) => {
    return useQuery({
        queryKey: [customerGroupsKey.GET_DETAIL_CUSTOMER_GROUPS, id],
        queryFn: () => customerGroupsApis.getDetailCustomerGroups(id),
        select: (data) => {
            return data;
        },
    });
};

export const useCreateCustomerGroups = (props?: {
    onSuccess?: (data: ICustomerGroups, response: ICustomerGroups) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: ICustomerGroups) =>
            customerGroupsApis.createCustomerGroups(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [customerGroupsKey.GET_LIST_CUSTOMER_GROUPS],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });

    return mutation;
};
export const useDeleteCustomerGroups = (props?: {
    onSuccess?: (
        data: PayloadDeleteCompanies,
        response: ApiResponse<GetListCustomerGroupsParams>,
    ) => void;
    onError?: () => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationKey: [customerGroupsKey.DELETE_CUSTOMER_GROUPS],
        mutationFn: (payload: PayloadDeleteCompanies) =>
            customerGroupsApis.deleteCustomerGroups(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [customerGroupsKey.GET_LIST_CUSTOMER_GROUPS],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });

    return mutation;
};

export const useSearchRestoreCustomerGroups = (
    params: SearchRestoresCustomerGroups,
) => {
    return useQuery({
        queryKey: [customerGroupsKey.SEARCH_RESTORE_CUSTOMER_GROUPS, params],
        queryFn: () => customerGroupsApis.searchRestoreCustomerGroups(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useRestoreCustomerGroups = (props?: {
    onSuccess?: (
        data: PayloadRestoresCustomerGroups,
        response: ApiResponse<SearchRestoresCustomerGroups>,
    ) => void;
    onError?: () => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationKey: [customerGroupsKey.RESTORE_CUSTOMER_GROUPS],
        mutationFn: (payload: PayloadRestoresCustomerGroups) =>
            customerGroupsApis.restoreCustomerGroup(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [customerGroupsKey.SEARCH_RESTORE_CUSTOMER_GROUPS],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
    return mutation;
};
