import { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardHeader,
    Col,
    Label,
    Row,
} from 'reactstrap';

import { ICustomerGroupsDetail } from '@/apis/customer-groups/customer-groups.type';

interface FileInfoProps {
    data: ICustomerGroupsDetail;
}
const FileInfo = ({ data }: FileInfoProps) => {
    const [showAllCustomer, setShowAllCustomer] = useState(false);

    const { companies } = data;

    return (
        <Col lg={4}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <Card className='mb-3'>
                <CardHeader className='d-flex align-items-center'>
                    <h5 className='mb-0 flex-grow-1'>
                        Khách hàng ({companies.length})
                    </h5>
                    <Button
                        color='success'
                        size='sm'
                        className='btn-sm'
                        style={{
                            backgroundColor: '#ffffff',
                            borderColor: '#0ab39c',
                            color: '#0ab39c',
                        }}
                    >
                        <i className='ri-add-line align-middle'></i> Thêm
                    </Button>
                </CardHeader>
                <CardBody>
                    {(showAllCustomer ? companies : companies.slice(0, 2)).map(
                        (item, idx, arr) => (
                            <div key={item?.id || idx}>
                                <h5>{item.name}</h5>
                                <div className='mt-2'>
                                    <Row className='mt-2'>
                                        <Col lg={3}>Loại hình</Col>
                                        <Col lg={9}>
                                            {item.businessTypeName}
                                        </Col>
                                    </Row>
                                    <Row className='mt-2'>
                                        <Col lg={3}>Lĩnh vực</Col>
                                        <Col lg={9}>{item.industryName}</Col>
                                    </Row>
                                    <Row className='mt-2'>
                                        <Col lg={3}>Giai đoạn</Col>
                                        <Col lg={9}>
                                            <span
                                                className='d-inline-block'
                                                style={{
                                                    padding: '2px 8px',
                                                    backgroundColor:
                                                        'rgba(10, 179, 156, 0.18)',
                                                    color: '#0ab39c',
                                                    borderRadius: '4px',
                                                    fontSize: '12px',
                                                    fontWeight: 500,
                                                    marginTop: '4px',
                                                }}
                                            >
                                                {item.lifecycleStageName}
                                            </span>
                                        </Col>
                                    </Row>
                                </div>
                                {idx < arr.length - 1 && (
                                    <hr className='my-3' />
                                )}
                            </div>
                        ),
                    )}
                </CardBody>
                {data.companies.length > 1 && (
                    <CardFooter>
                        <Label
                            style={{ cursor: 'pointer' }}
                            onClick={() => setShowAllCustomer(!showAllCustomer)}
                        >
                            {showAllCustomer ? 'Thu lại' : 'Xem thêm'}
                        </Label>
                    </CardFooter>
                )}
            </Card>
        </Col>
    );
};

export default FileInfo;
