import {
    ClassificationNeedType,
    DealType,
    InstallationType,
    RequestDemo,
} from '@/app/(with-layout)/crm/co-hoi-ban-hang/types';
import { Owner, User } from '@/types/user.type';

export interface IDealParams {
    FieldSearch?: string;
    FromDate?: string;
    ToDate?: string;
    OwnerIds?: string[];
    PipelineId?: string;
    PipelineStageId?: string;
    PageNumber?: string;
    PageSize?: string;
    SortField?: string;
    IsDescending?: boolean;
}
export interface IDealResponse {
    priority: string;
    pipelineStageName: string;
    amount: number;
    title: string;
    // Type kanban
    dealId: string;
    closeDate: string;
    createdDate: string;
    owner: Owner;
    contacts: User[];
    // Type list
    id: string;
    customerName: string;
    phoneNumber: string;
    email: string;
    code: string;
    createdDateTime: string;
}

export interface IPipelineStage {
    id: string;
    name: string;
    sort: number;
    totalDeals: number;
    totalRevenue: number;
}

export interface IPipelineStagesParams {
    OwnerId?: string;
    PageNumber: number;
    PageSize: number;
}
export interface IPipelineStagesResponse {
    id: string;
    pipelineStages: IPipelineStage[];
}

export interface IClassProduct {
    id: string;
    productClassicId: string;
    productClassicOptionId: string;
    description: string;
    code: string;
    productOption: string;
}

export interface ICyberProduct {
    id: string;
    productId: string;
    installationType: InstallationType;
    productCompare: string;
    installationTypeName: string;
    name: string;
}

export interface ICustomerNeed {
    timeRequest: string;
    estimatedPurchase: string;
    classificationNeedType: ClassificationNeedType;
    needAttention: string;
    estimatedBudget: number;
    contentExchange: string;
    requestDemo: RequestDemo;
    category: string;
    classicProducts: IClassProduct[];
    cyberProducts: ICyberProduct[];
    id?: string;
    classificationNeedTypeName?: string;
}

export interface Project {
    name: string;
    managerId: string;
    departmentId: string;
    description: string;
    watcherUserIds: string[] | string;
}

export interface IOpportunity {
    title: string;
    description: string;
    amount: number;
    priority: number;
    probability: number;
    closeDate: string;
    ownerId: string;
    tradePartnerId: string;
    companyId: string;
    pipelineStageId: string;
    segmentType: 1;
    dealType: DealType;
    processesId: string;
    startPoC: string;
    endPoC: string;
    isCreateProject: boolean;
    customerNeed: ICustomerNeed;
    project: Project;
}

export interface IDealsByPipelineStageSortParams {
    Name?: string;
    Sorts?: string[];
    PageNumber?: number;
    PageSize?: number;
    isDescending?: boolean;
}

export interface ParamsId {
    id: string;
}

// Optimize type sau
export interface IDealContact {
    id: string;
    fullName: string;
    departmentName: string;
    positionTitle: string;
    avatarUrl: string;
    roleType: number;
    roleTypeName: string;
}

export interface IProductsInfo {
    startPoCDate: string;
    endPoCDate: string;
    products: ICyberProduct[];
    productClassics: IClassProduct[];
}

export interface ITradePartnerContact {
    id: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    roleName: string;
}

export interface ITradePartner {
    email: string;
    phoneNumber: string;
    contacts: ITradePartnerContact[];
}

export interface IDealShort {
    id: string;
    title: string;
    amount: number;
    probability: number;
}

export interface IContact {
    id: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    roleName: string;
}

export interface IContract {
    id: string;
    name: string;
    createdDate: string;
    creatorName: string;
}

export interface IProcess {
    id: string;
    name: string;
    status: number;
    statusName: string;
}

export interface IQuote {
    id: string;
    name: string;
    closeDate: string;
    amount: number;
}

export interface IProject {
    id: string;
    name: string;
    managerName: string;
    status: number;
    statusName: string;
}

export interface IDealDetail {
    id: string;
    code: string;
    title: string;
    description: string;
    amount: number;
    priority: number;
    priorityName: string;
    probability: number;
    startPoC: string;
    endPoC: string;
    createdDateTime: string;
    dealType: number;
    dealTypeName: string;
    contactsDeal: IDealContact[];
    customerNeed: ICustomerNeed;
    products: IProductsInfo;
    pipelineStageName: string;
    pipelineStageId: string;
    ownerName: string;
    ownerId: string;
    ownerAvatarUrl: string;
    tradePartnerName: string;
    tradePartnerId: string;
    tradePartnerAvatarUrl: string;
    tradePartners: ITradePartner[];
    companyName: string;
    companyId: string;
    companyAvatarUrl: string;
    segmentType: number;
    segmentTypeName: string;
    deals: IDealShort[];
    contacts: IContact[];
    contracts: IContract[];
    process: IProcess;
    quotes: IQuote[];
    project: IProject;
}
