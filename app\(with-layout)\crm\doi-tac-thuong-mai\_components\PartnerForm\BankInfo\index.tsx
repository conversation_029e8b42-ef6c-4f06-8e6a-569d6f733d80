'use client';

import {
    IPartnerBankPayload,
    IPartnerPayload,
} from '@/apis/partners/partners.type';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Button, Col, Row } from 'reactstrap';

interface BankInfoProps {
    index: number;
    key: string;
    name: string;
    bankInfo: IPartnerBankPayload;
    removeBankInfo: (index: number) => void;
}

const RowBankInfo = (props: BankInfoProps) => {
    const { index, key, name, bankInfo, removeBankInfo } = props;

    const [isEdit, setIsEdit] = useState<boolean>(true);

    const handleToggleEdit = () => {
        setIsEdit(!isEdit);
    };

    if (isEdit) {
        return (
            <tr key={key}>
                <td>{index + 1}</td>
                <td>
                    <FormController
                        controlType='textInput'
                        name={`${name}.${index}.bank`}
                        placeholder='Nhập tên ngân hàng'
                    />
                </td>
                <td>
                    <FormController
                        controlType='textInput'
                        name={`${name}.${index}.bankBranch`}
                        placeholder='Nhập chi nhánh ngân hàng'
                    />
                </td>
                <td>
                    <FormController
                        controlType='textInput'
                        name={`${name}.${index}.accountNumber`}
                        placeholder='Nhập số tài khoản'
                    />
                </td>
                <td>
                    <FormController
                        controlType='textInput'
                        name={`${name}.${index}.accountHolderName`}
                        placeholder='Nhập tên chủ tài khoản'
                    />
                </td>
                <td>
                    <FormController
                        controlType='textInput'
                        name={`${name}.${index}.customSwiftCode`}
                        placeholder='Nhập mã Swift'
                    />
                </td>
                <td>
                    <div className='d-flex gap-2 justify-center align-middle'>
                        <button
                            type='button'
                            className='btn btn-sm btn-primary'
                            onClick={handleToggleEdit}
                        >
                            <i className='ri-edit-line'></i>
                        </button>
                        <button
                            type='button'
                            className='btn btn-sm btn-danger'
                            onClick={() => removeBankInfo(index)}
                        >
                            <i className='ri-delete-bin-line'></i>
                        </button>
                    </div>
                </td>
            </tr>
        );
    }

    return (
        <tr key={key}>
            <td>{index + 1}</td>
            <td>{bankInfo.bank}</td>
            <td>{bankInfo.bankBranch}</td>
            <td>{bankInfo.accountNumber}</td>
            <td>{bankInfo.accountHolderName}</td>
            <td>{bankInfo.customSwiftCode}</td>
            <td>
                <div className='d-flex gap-2 justify-center align-middle'>
                    <button
                        type='button'
                        className='btn btn-sm btn-primary'
                        onClick={handleToggleEdit}
                    >
                        <i className='ri-edit-line'></i>
                    </button>
                    <button
                        type='button'
                        className='btn btn-sm btn-danger'
                        onClick={() => removeBankInfo(index)}
                    >
                        <i className='ri-delete-bin-line'></i>
                    </button>
                </div>
            </td>
        </tr>
    );
};

const BankInfo = () => {
    const methods = useFormContext<IPartnerPayload>();
    const { control, getValues } = methods;

    const {
        fields: bankInfos,
        append: appendBankInfo,
        remove: removeBankInfo,
    } = useFieldArray({
        control,
        name: 'addTradePartnerDto.addBankAccountDtos',
    });

    const handleAddBankInfo = () => {
        const newBankInfo = {
            bank: '',
            bankBranch: '',
            accountNumber: '',
            accountHolderName: '',
            customSwiftCode: '',
            companyId: null,
        };

        appendBankInfo(newBankInfo);
    };

    const renderBanksInfo = () => (
        <div>
            <div className='d-flex justify-content-between align-items-center'>
                <h6 className='mb-0'>Tài khoản thanh toán</h6>
                <Button
                    color='light'
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={handleAddBankInfo}
                    style={{
                        border: '1px solid #60cfbf',
                        backgroundColor: '#ffffff',
                        color: '#60cfbf',
                    }}
                >
                    <i className='ri-add-line me-1'></i> Thêm tài khoản
                </Button>
            </div>
            <table
                className='table table-bordered mb-0'
                style={{ tableLayout: 'fixed', width: '100%' }}
            >
                <thead>
                    <tr>
                        <th style={{ width: '100px' }}>Dòng</th>
                        <th style={{ width: 'auto' }}>Ngân hàng</th>
                        <th style={{ width: 'auto' }}>Chi nhánh</th>
                        <th style={{ width: 'auto' }}>Số tài khoản</th>
                        <th style={{ width: 'auto' }}>Tên chủ tài khoản</th>
                        <th style={{ width: 'auto' }}>Mã Swift</th>
                        <th style={{ width: 'auto' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {bankInfos.map((_, index) => (
                        <RowBankInfo
                            index={index}
                            key={`bank-${index}`}
                            name='addTradePartnerDto.addBankAccountDtos'
                            bankInfo={getValues(
                                `addTradePartnerDto.addBankAccountDtos.${index}`,
                            )}
                            removeBankInfo={removeBankInfo}
                        />
                    ))}
                    {bankInfos.length === 0 && (
                        <tr>
                            <td colSpan={6} className='text-center py-3'>
                                Chưa có dữ liệu. Vui lòng thêm tài khoản ngân
                                hàng
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );

    return (
        <CollapseApp title='THÔNG TIN BỔ SUNG'>
            <Row className='g-3 justify-content-around'>
                <Col md='11'>{renderBanksInfo()}</Col>
            </Row>
        </CollapseApp>
    );
};

export default BankInfo;
