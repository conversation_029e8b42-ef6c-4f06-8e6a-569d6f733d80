import React from 'react';
import { Card, CardBody } from 'reactstrap';

export interface AddressCardProps {
    iconClass: string;
    title: string;
    address: string;
}

const AddressCard: React.FC<AddressCardProps> = ({
    iconClass,
    title,
    address,
}) => (
    <div className='col-lg-4 mb-3'>
        <Card className='border'>
            <CardBody>
                <div className='d-flex gap-1 align-items-center mb-1'>
                    <i className={`${iconClass} fs-22`}></i>
                    <h5 className='mb-0 ml-2'>{title}</h5>
                </div>
                <p>{address}</p>
            </CardBody>
        </Card>
    </div>
);

export default AddressCard;
