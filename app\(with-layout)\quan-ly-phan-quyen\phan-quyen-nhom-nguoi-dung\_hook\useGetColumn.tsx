import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';

interface GetColumnProps {
    onSelectedAction: (
        action: ACTIONS,
        data: IUserGroupAuthorization | undefined,
    ) => void;
}
export interface IUserGroupAuthorization {
    name: string;
    MoTaNhomNguoiDung: string;
}
const useGetColumn = ({ onSelectedAction }: GetColumnProps) => {
    const actions: DropdownAction<IUserGroupAuthorization>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) => onSelectedAction(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );

    const columns = useMemo<MRT_ColumnDef<IUserGroupAuthorization>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên nhóm người dùng',
                size: 150,
                Header: () => (
                    <div style={{ paddingLeft: '20px' }}>
                        Tên nhóm người dùng
                    </div>
                ),
                Cell: ({ cell }) => (
                    <div style={{ paddingLeft: '50px' }}>
                        {cell.getValue<string>()}
                    </div>
                ),
            },
            {
                accessorKey: 'MoTaNhomNguoiDung',
                header: 'Mô Tả Nhóm Người Dùng',
                size: 500,

                Cell: ({ cell }) => <div>{cell.getValue<string>()}</div>,
            },
            {
                header: 'Hành động',
                enableResizing: false,
                size: 70,
                Header: () => (
                    <div style={{ textAlign: 'center' }}>Hành động</div>
                ),
                Cell: ({ row }) => (
                    <div
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                        style={{ textAlign: 'center', paddingLeft: '20px' }}
                    >
                        <DropdownActionMenu
                            actions={actions}
                            data={row.original}
                            direction='down'
                            end={false}
                        />
                    </div>
                ),
            },
        ],
        [actions],
    );

    return columns;
};

export default useGetColumn;
