import React from 'react';
import { Badge } from 'reactstrap';

export interface FieldProps {
    label: string; // nhãn cố định
    value: string;
    type?: number;
    badgeText?: string;
}

const Field: React.FC<FieldProps> = ({ label, value, badgeText, type }) => (
    <div className='mb-3'>
        <label className='form-label'>{label}</label>
        <div className='flex gap-2'>
            {value}
            {badgeText && (
                <Badge
                    color='black'
                    className={`ml-2 badge rounded-pill  ${type === 2 ? 'text-success bg-success-subtle' : 'mx-2'}`}
                >
                    {badgeText}
                </Badge>
            )}
        </div>
    </div>
);

export default Field;
