'use client';

import CollapseApp from '@/components/common/CollapseApp';

import { useState } from 'react';

import FormController from '@/components/common/FormController';
import { useFormContext, useWatch } from 'react-hook-form';
import { ICustomer } from '@/apis/customer/customer.type';
import {
    CompanyContactRole,
    FormOfPurchase,
} from '@/constants/sharedData/sharedData.enums';
import { useSearchContacts } from '@/apis/contact/contact.api';
import { SearchContact } from '@/apis/contact/contact.type';

import AddContactModal from '../../Modal/AddContactModal';
import { IPartnerParams } from '@/apis/partners/partners.type';
import { useSearchPartners } from '@/apis/partners/partners.api';
import AddPartnerModal from '../../Modal/AddPartnerModal';
import PartnersTable from './PartnersTable';
import ContactsTable from './ContactsTable';
import { Col, Row } from 'reactstrap';

interface LinkedInfoProps {
    initValue?: ICustomer;
}
const LinkedInfo = ({ initValue }: LinkedInfoProps) => {
    const [isAddContactModalOpen, setIsAddContactModalOpen] = useState(false);
    const [isAddPartnerModalOpen, setIsAddPartnerModalOpen] = useState(false);

    const searchContact: SearchContact = {
        PageNumber: 1,
        PageSize: 50,
    };
    const searchPartner: IPartnerParams = {
        PageNumber: 1,
        PageSize: 50,
    };

    const toggleAddContactModal = () => {
        setIsAddContactModalOpen(!isAddContactModalOpen);
    };

    const toggleAddPartnerModal = () => {
        setIsAddPartnerModalOpen(!isAddPartnerModalOpen);
    };

    const {
        data: contactOptionsData,
        refetch: refetchContacts,
        isLoading: isLoadingContacts,
    } = useSearchContacts(searchContact);

    const contactList = contactOptionsData?.items || [];

    const { data: partnerOptionsData, refetch: refetchPartner } =
        useSearchPartners(searchPartner);
    const partnerList = partnerOptionsData?.items || [];

    const purchaseContacts =
        initValue?.contacts?.filter(
            (c) => c.role === CompanyContactRole.Purchase,
        ) ||
        initValue?.purchaseContactDetailCompanies?.map((c) => ({
            contactId: c.id,
            role: CompanyContactRole.Purchase,
        })) ||
        [];
    const usageContacts =
        initValue?.contacts?.filter(
            (c) => c.role === CompanyContactRole.Usage,
        ) ||
        initValue?.usageContactDetailCompanies?.map((c) => ({
            contactId: c.id,
            role: CompanyContactRole.Usage,
        })) ||
        [];

    const { control } = useFormContext<ICustomer>();
    const formOfPurchase = useWatch({
        control,
        name: 'formOfPurchase',
    });

    return (
        <CollapseApp title='THÔNG TIN LIÊN KẾT'>
            <Row style={{ padding: '20px 40px 20px 40px' }}>
                <Col md='12' className='mb-5'>
                    <ContactsTable
                        contactList={contactList}
                        toggleAddContactModal={toggleAddContactModal}
                        role={CompanyContactRole.Purchase}
                        title='Phòng mua hàng'
                        buttonText='Tạo mới cá nhân'
                        initContacts={purchaseContacts}
                        isLoading={isLoadingContacts}
                    />
                </Col>
                <Col md='12' className='mb-5'>
                    <ContactsTable
                        contactList={contactList}
                        toggleAddContactModal={toggleAddContactModal}
                        role={CompanyContactRole.Usage}
                        title='Phòng sử dụng'
                        buttonText='Tạo mới người sử dụng'
                        initContacts={usageContacts}
                        isLoading={isLoadingContacts}
                    />
                </Col>
                <Col md='6' className='mb-3'>
                    <FormController
                        controlType='select'
                        name='formOfPurchase'
                        label='Hình thức mua hàng'
                        placeholder='Chọn hình thức mua hàng'
                        data={[
                            {
                                label: 'Mua trực tiếp',
                                value: FormOfPurchase.BuyDirectly.toString(),
                            },
                            {
                                label: 'Mua qua đối tác thương mại',
                                value: FormOfPurchase.SI.toString(),
                            },
                        ]}
                        styles={{
                            label: {
                                fontSize: '16px',
                            },
                        }}
                    />
                </Col>

                {Number(formOfPurchase) === 1 && (
                    <Col md='12'>
                        <PartnersTable
                            partnerList={partnerList}
                            toggleAddPartnerModal={toggleAddPartnerModal}
                            initPartners={
                                initValue?.detailTradePartnerCompanies
                            }
                        />
                    </Col>
                )}
            </Row>

            <AddContactModal
                isOpen={isAddContactModalOpen}
                toggle={toggleAddContactModal}
                onSuccess={() => {
                    refetchContacts();
                }}
            />
            <AddPartnerModal
                isOpen={isAddPartnerModalOpen}
                toggle={toggleAddPartnerModal}
                onSuccess={() => {
                    refetchPartner();
                }}
            />
        </CollapseApp>
    );
};

export default LinkedInfo;
