'use client';

import { FormProvider, useForm } from 'react-hook-form';
import { <PERSON><PERSON>, Card, Col, Row } from 'reactstrap';
import ConfigurationRow from '../../../_components/ConfigurationRow';

const UpdateQuotationForm = () => {
    const methods = useForm({
        defaultValues: {},
    });
    return (
        <FormProvider {...methods}>
            <Card
                style={{ padding: '20px 40px 20px 40px' }}
                className='d-flex flex-column gap-3'
            >
                <Row className='gap-2 justify-content-around'>
                    <Col md='11'>
                        <div className='header-quotation-form d-flex justify-content-between'>
                            <p style={{ fontWeight: 700, width: '146px' }}>
                                Mã trường
                            </p>
                            <p style={{ fontWeight: 700, width: '203px' }}>
                                Trường thông tin
                            </p>
                            <p style={{ fontWeight: 700, width: '220px' }}>
                                Kiểu dữ liệu
                            </p>
                            <p style={{ fontWeight: 700, width: '320px' }}>
                                <PERSON><PERSON> tả
                            </p>
                            <p style={{ fontWeight: 700, width: '60px' }}>
                                Ẩn/ Hiện
                            </p>
                            <div
                                style={{
                                    fontWeight: 700,
                                    width: '180px',
                                    cursor: 'pointer',
                                }}
                            ></div>
                            <div
                                style={{
                                    fontWeight: 700,
                                    width: '16px',
                                    cursor: 'pointer',
                                }}
                            ></div>
                        </div>
                    </Col>
                    <ConfigurationRow />
                </Row>

                <Row className='g-3 justify-content-around'>
                    <Col md='11'>
                        <Button color='success' outline size='sm'>
                            <i className='ri-add-line align-bottom me-1'></i>
                            Thêm trường
                        </Button>
                    </Col>
                </Row>

                <Row className='g-3 justify-content-around'>
                    <Col md='11' className='d-flex justify-content-between'>
                        <Button color='danger' outline>
                            Quay lại
                        </Button>
                        <Button color='success'>Tạo mẫu</Button>
                    </Col>
                </Row>
            </Card>
        </FormProvider>
    );
};

export default UpdateQuotationForm;
