import { <PERSON><PERSON>, <PERSON>, Card<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'reactstrap';
import { useState } from 'react';

import { IQuoteDetailResponse } from '@/apis/quotes/quotes.type';

interface Props {
    data?: IQuoteDetailResponse;
}

const FileInfo = ({ data }: Props) => {
    const [showAllContracts, setShowAllContracts] = useState(false);
    const [showAllDeals, setShowAllDeals] = useState(false);
    const [showAllTradePartners, setShowAllTradePartners] = useState(false);

    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {/* File items */}
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            {/* Contracts Card */}
            {data?.contracts && data.contracts.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Hợp đồng ({data.contracts.length})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllContracts
                            ? data.contracts
                            : data.contracts.slice(0, 1)
                        ).map((contract, index) => (
                            <div className='mb-4' key={contract.id || index}>
                                <div className='d-flex align-items-center mb-3'>
                                    <h6 className='mb-0 flex-grow-1 fs-16'>
                                        {contract.name}
                                    </h6>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Ngày tạo:{' '}
                                        <span className='fw-medium text-dark'>
                                            {new Date(
                                                contract.createdDate,
                                            ).toLocaleDateString('vi-VN')}{' '}
                                            {new Date(
                                                contract.createdDate,
                                            ).toLocaleTimeString('vi-VN', {
                                                hour: '2-digit',
                                                minute: '2-digit',
                                            })}
                                        </span>
                                    </span>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Người tạo:{' '}
                                        <span className='fw-medium text-dark'>
                                            {contract.creatorName}
                                        </span>
                                    </span>
                                </div>

                                {index <
                                    (showAllContracts
                                        ? data.contracts
                                        : data.contracts.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.contracts.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                onClick={() =>
                                    setShowAllContracts(!showAllContracts)
                                }
                            >
                                {showAllContracts ? 'Thu gọn' : 'Xem thêm'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}

            {/* Deals Card */}
            {data?.deals && data.deals.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Cơ hội ({data.deals.length})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllDeals
                            ? data.deals
                            : data.deals.slice(0, 1)
                        ).map((deal, index) => (
                            <div className='mb-4' key={index}>
                                <div className='d-flex align-items-center mb-3'>
                                    <h6 className='mb-0 flex-grow-1 fs-16'>
                                        {deal.title}
                                    </h6>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Doanh thu:{' '}
                                        <span className='fw-medium text-success fs-15'>
                                            {deal.amount?.toLocaleString(
                                                'vi-VN',
                                            )}{' '}
                                            VNĐ
                                        </span>
                                    </span>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Xác suất thành:{' '}
                                        <span className='fw-medium text-dark'>
                                            {deal.probability}%
                                        </span>
                                    </span>
                                </div>

                                {index <
                                    (showAllDeals
                                        ? data.deals
                                        : data.deals.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.deals.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                onClick={() => setShowAllDeals(!showAllDeals)}
                            >
                                {showAllDeals ? 'Thu gọn' : 'Xem thêm'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}

            {/* Trade Partners Card */}
            {data?.tradePartners && data.tradePartners.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader>
                        <div className='d-flex align-items-center'>
                            <h5 className='mb-0 flex-grow-1'>
                                Đối tác thương mại ({data.tradePartners.length})
                            </h5>
                            <Button
                                color='success'
                                size='sm'
                                className='btn-sm'
                                style={{
                                    backgroundColor: '#0ab39c',
                                    border: 'none',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>{' '}
                                Thêm
                            </Button>
                        </div>
                    </CardHeader>
                    <CardBody>
                        {(showAllTradePartners
                            ? data.tradePartners
                            : data.tradePartners.slice(0, 1)
                        ).map((partner, index) => (
                            <div className='mb-4' key={index}>
                                <div className='d-flex align-items-center mb-3'>
                                    <div className='flex-grow-1'>
                                        <h6 className='mb-0 fs-16'>
                                            {data.tradePartnerName ||
                                                'Đối tác thương mại'}
                                        </h6>
                                    </div>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                        title='Xem chi tiết'
                                    >
                                        <i className='ri-more-2-line'></i>
                                    </Button>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Email:{' '}
                                        <span className='fw-medium text-dark'>
                                            {partner.email}
                                        </span>
                                    </span>
                                </div>

                                <div className='mb-2'>
                                    <span className='text-muted fs-14'>
                                        Số điện thoại:{' '}
                                        <span className='fw-medium text-dark'>
                                            {partner.phoneNumber}
                                        </span>
                                    </span>
                                </div>

                                {partner.contacts &&
                                    partner.contacts.length > 0 && (
                                        <div className='mb-2'>
                                            <span className='text-muted fs-14'>
                                                Liên hệ:{' '}
                                                <span className='fw-medium text-dark'>
                                                    {
                                                        partner.contacts[0]
                                                            .fullName
                                                    }
                                                    {partner.contacts.length >
                                                        1 &&
                                                        ` (+${partner.contacts.length - 1} khác)`}
                                                </span>
                                            </span>
                                        </div>
                                    )}

                                {index <
                                    (showAllTradePartners
                                        ? data.tradePartners
                                        : data.tradePartners.slice(0, 1)
                                    ).length -
                                        1 && <hr className='my-3' />}
                            </div>
                        ))}

                        {data.tradePartners.length > 1 && (
                            <Button
                                color='info'
                                outline
                                block
                                onClick={() =>
                                    setShowAllTradePartners(
                                        !showAllTradePartners,
                                    )
                                }
                            >
                                {showAllTradePartners ? 'Thu gọn' : 'Xem thêm'}
                            </Button>
                        )}
                    </CardBody>
                </Card>
            )}
        </Col>
    );
};

export default FileInfo;
