import FormController from '@/components/common/FormController';
import { useState } from 'react';
import { Col } from 'reactstrap';
import { fieldTypeOptions } from '../../type';
import ModalSetUpCalculationFormula from '../ModalSetUpCalculationFormula';

const ConfigurationRow = () => {
    const [isOpenModalCalculationFormula, setIsOpenModalCalculationFormula] =
        useState(false);

    const handleToggle = () => {
        setIsOpenModalCalculationFormula(!isOpenModalCalculationFormula);
    };

    return (
        <>
            <Col md='11'>
                <div className='row-quotation-form d-flex justify-content-between'>
                    <div style={{ fontWeight: 700, width: '146px' }}>
                        <FormController
                            controlType='textInput'
                            name='fieldId'
                            placeholder='Nhập mã'
                        />
                    </div>
                    <div style={{ fontWeight: 700, width: '203px' }}>
                        <FormController
                            controlType='textInput'
                            name='fieldInfo'
                            placeholder='Nhập một trường'
                        />
                    </div>
                    <div style={{ fontWeight: 700, width: '220px' }}>
                        <FormController
                            controlType='select'
                            name='fieldType'
                            placeholder='Chọn kiểu dữ liệu'
                            data={fieldTypeOptions}
                        />
                    </div>
                    <div style={{ fontWeight: 700, width: '320px' }}>
                        <FormController
                            controlType='textInput'
                            name='fieldInfo'
                            placeholder='Nhập mô tả'
                        />
                    </div>
                    <div
                        style={{ fontWeight: 700, width: '60px' }}
                        className='d-flex justify-content-center align-items-center'
                    >
                        <i
                            className='ri-eye-line align-bottom me-1'
                            style={{
                                fontSize: '1.25rem',
                                fontWeight: '400',
                                cursor: 'pointer',
                            }}
                        ></i>
                        {/*  ri-eye-off-line */}
                    </div>
                    <div
                        style={{
                            fontWeight: 700,
                            width: '180px',
                            cursor: 'pointer',
                        }}
                        className='d-flex align-items-center'
                        onClick={handleToggle}
                    >
                        <i
                            className='ri-tools-fill align-bottom me-1'
                            style={{
                                fontSize: '1.25rem',
                                fontWeight: '400',

                                color: 'rgba(10, 179, 156, 1)',
                            }}
                        ></i>
                        <p
                            style={{
                                fontWeight: 700,
                                color: 'rgba(10, 179, 156, 1)',
                            }}
                            className='mb-0'
                        >
                            Thiết lập công thức tính
                        </p>
                    </div>
                    <div
                        style={{
                            fontWeight: 700,
                            width: '16px',
                            cursor: 'pointer',
                        }}
                        className='d-flex align-items-center'
                    >
                        <i
                            className='ri-delete-bin-line mb-1'
                            style={{
                                fontSize: '1.25rem',
                                fontWeight: '400',
                                color: 'rgba(236, 66, 67, 1)',
                            }}
                        ></i>
                    </div>
                </div>
            </Col>
            <ModalSetUpCalculationFormula
                isOpen={isOpenModalCalculationFormula}
                onToggle={handleToggle}
            />
        </>
    );
};

export default ConfigurationRow;
