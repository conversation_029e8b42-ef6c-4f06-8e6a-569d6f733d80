'use client';

import { ReactNode, useEffect, useState } from 'react';

interface NoSSRProps {
    children: ReactNode;
    fallback?: ReactNode;
}

export default function NoSSR({ children, fallback = null }: NoSSRProps) {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    if (!isMounted) {
        return <>{fallback}</>;
    }

    return <>{children}</>;
}
