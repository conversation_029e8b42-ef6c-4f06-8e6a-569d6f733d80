import { IQuoteDetailResponse } from '@/apis/quotes/quotes.type';
import { Col, Label, Row } from 'reactstrap';

interface Tab1Props {
    data?: IQuoteDetailResponse;
}

const Tab1 = ({ data }: Tab1Props) => {
    const formatDateTime = (dateTimeString?: string): string => {
        if (!dateTimeString) {
            return '';
        }

        try {
            const date = new Date(dateTimeString);

            if (isNaN(date.getTime())) {
                return dateTimeString;
            }

            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();

            return `${day}/${month}/${year}`;
        } catch {
            return dateTimeString;
        }
    };

    const getAvatarText = (name?: string): string => {
        if (!name) {
            return '';
        }

        const words = name.trim().split(' ').filter(Boolean);

        if (words.length >= 2) {
            return (
                words[0].charAt(0) + words[words.length - 1].charAt(0)
            ).toUpperCase();
        } else if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }

        return '';
    };
    return (
        <div>
            <h3>{data?.name}</h3>
            <Row className='mt-4'>
                <Col md={5}>
                    <Col md={12} className='mb-2'>
                        <Label>MÃ BÁO GIÁ</Label>
                        <p>{data?.code}</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>NGÀY BÁO GIÁ</Label>
                        <p>{formatDateTime(data?.createdDateTime)}</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>KHÁCH HÀNG</Label>
                        <div className='d-flex align-items-center gap-2'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {getAvatarText(data?.company?.name)}
                            </div>
                            <span>{data?.company?.name}</span>
                        </div>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>TÊN CƠ HỘI</Label>
                        <p>{data?.dealName}</p>
                    </Col>
                </Col>
                <Col md={5} className='mt-2'>
                    <Col md={12} className='mb-2'>
                        <Label>TRẠNG THÁI</Label>
                        <p>{data?.statusName}</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>NGÀY KẾT THÚC</Label>
                        <p>{formatDateTime(data?.expectedEndDate)}</p>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>NHÂN VIÊN KINH DOANH</Label>
                        <div className='d-flex align-items-center gap-2'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {getAvatarText(data?.owner?.name)}
                            </div>
                            <span>{data?.owner?.name}</span>
                        </div>
                    </Col>
                </Col>
            </Row>
        </div>
    );
};
export default Tab1;
