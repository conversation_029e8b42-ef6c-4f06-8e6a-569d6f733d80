'use client';
import ButtonHeader from '@/components/common/ButtonHeader';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import MantineTable from '@/components/common/MantineReactTable';
import { ACTIONS } from '@/types/actions.type';
import { FormProvider, useForm } from 'react-hook-form';
import { Card, CardHeader, Col, Container } from 'reactstrap';
import useGetColumn, { IBusinessType } from './_hook/useGetColumn';

const data: IBusinessType[] = [
    {
        name: '<PERSON>ị<PERSON> vụ',
        description: 'Thuộc quản lí của chính phủ',
        createdDate: '10/10/2022',
    },
    {
        name: '<PERSON><PERSON><PERSON> nghiệ<PERSON> thương mại',
        description: 'Thuộc quản lí của tư nhân',
        createdDate: '10/10/2022',
    },
    {
        name: '<PERSON><PERSON><PERSON> nghiệ<PERSON> vốn đầu tư nước ngoài',
        description: 'Vốn sở hữu của nước ngoài',
        createdDate: '10/10/2022',
    },
];
const BusinessSector = () => {
    const handleSelect = (
        action: ACTIONS,
        selectedData: IBusinessType | undefined,
    ) => {
        if (!selectedData) {
            return;
        }

        switch (action) {
            case ACTIONS.EDIT: {
                console.error('Edit business type:', selectedData.name);
                break;
            }
            case ACTIONS.DELETE: {
                console.error('Delete business type:', selectedData.name);
                break;
            }
            case ACTIONS.VIEW_DETAIL: {
                console.error('View business type details:', selectedData.name);
                break;
            }
        }
    };

    const columns = useGetColumn({ onSelectedAction: handleSelect });

    const methods = useForm({
        defaultValues: {
            pageNumber: 1,
            pageSize: 10,
        },
    });

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        showImportButton={false}
                        showExportButton={false}
                    />
                </Col>
                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex justify-content-between align-items-center'>
                                <div style={{ flex: 1 }}>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên loại hình doanh nghiệp...'
                                    />
                                </div>
                            </div>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={data}
                            totalItems={data.length}
                            onPageChange={(page: number) => {
                                console.error(page);
                            }}
                            onPageSizeChange={(size: number) => {
                                console.error(size);
                            }}
                        />
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};

export default BusinessSector;
