'use client';
import {
    GetListCustomerGroupsParams,
    ICustomerGroupsResponse,
} from '@/apis/customer-groups/customer-groups.type';

import { ROUTES } from '@/lib/routes';

import {
    useDeleteCustomerGroups,
    useGetListCustomerGroups,
} from '@/apis/customer-groups/customer-groups.api';
import ButtonHeader from '@/components/common/ButtonHeader';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { ACTIONS } from './_types/action.type';

import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ImportFileModal from '@/components/common/ImportFile';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { getOneMonthAgo, getToday } from '@/utils/time';
import dynamic from 'next/dynamic';
import { toast } from 'react-toastify';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ICustomerGroupsResponse>,
        })),
    {
        ssr: false,
    },
);

const CustomerGroups = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const handleSelectAction = (
        action: ACTIONS,
        row?: ICustomerGroupsResponse,
    ) => {
        if (!row) {
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setModal(true);
                break;
            case ACTIONS.EDIT:
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(
                    ROUTES.CRM.CUSTOMER_GROUPS.DETAIL.replace(':id', row.id),
                );
                break;
            default:
                break;
        }
    };
    const columns = useGetColumn({
        onSelectedAction: handleSelectAction,
    });
    const methods = useForm<GetListCustomerGroupsParams>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        Name,
        SalePersonId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'SalePersonId',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { data, refetch, isLoading } = useGetListCustomerGroups({
        Name,
        SalePersonId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    const { items: listCustomerGroups = [], totalItems = 0 } = data ?? {};

    const { mutate: deleteCustomerGroups } = useDeleteCustomerGroups({
        onSuccess: () => {
            toast.success('Xóa nhóm khách hàng thành công');
            setModal(false);
            setSelectedNames([]);
            setSelectedIds([]);
            refetch();
        },
        onError: () => {
            toast.error('Xóa nhóm khách hàng thất bại');
        },
    });
    const handleConfimDelete = () => {
        deleteCustomerGroups({ ids: selectedIds });
    };

    const handleRedirectPageCreate = () => {
        router.push(ROUTES.CRM.CUSTOMER_GROUPS.CREATE);
    };
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col lg={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={handleRedirectPageCreate}
                    />
                </Col>

                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex flex-wrap align-items-center gap-2'>
                                <InputSearchNameWithApiControl
                                    name='Name'
                                    placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                />

                                <ComboboxSelectUserControl
                                    name='SalePersonId'
                                    placeholder='Nhân viên kinh doanh'
                                />

                                <div className='d-flex gap-2 ms-auto'>
                                    {selectedIds.length > 0 ? (
                                        <Button
                                            style={{
                                                backgroundColor: 'red',
                                                border: 'none',
                                            }}
                                            onClick={() => setModal(true)}
                                        >
                                            Xóa ({selectedIds.length})
                                        </Button>
                                    ) : (
                                        ''
                                    )}
                                    <Button
                                        outline
                                        className='filter-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-filter-line text-primary'></i>
                                    </Button>
                                    <Dropdown
                                        isOpen={dropdownOpen}
                                        toggle={toggleDropdown}
                                        direction='down'
                                    >
                                        <DropdownToggle
                                            outline
                                            className='settings-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-settings-2-line text-info'></i>
                                        </DropdownToggle>
                                        <DropdownMenu>
                                            <DropdownItem
                                                onClick={() =>
                                                    router.push(
                                                        ROUTES.CRM
                                                            .CUSTOMER_GROUPS
                                                            .RESTORE,
                                                    )
                                                }
                                            >
                                                Khôi phục tài khoản
                                            </DropdownItem>
                                        </DropdownMenu>
                                    </Dropdown>
                                </div>
                            </div>
                        </CardHeader>

                        <MantineTable
                            columns={columns}
                            data={listCustomerGroups}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listCustomerGroups.findIndex(
                                                    (
                                                        contact: ICustomerGroupsResponse,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listCustomerGroups.findIndex(
                                                            (
                                                                contact: ICustomerGroupsResponse,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listCustomerGroups[
                                                    parseInt(key)
                                                ];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='nhóm khách hàng'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};

export default CustomerGroups;
