import {
    Button,
    Input,
    Offcanvas,
    OffcanvasBody,
    OffcanvasHeader,
} from 'reactstrap';
import { AdvancedFilter } from '../../types';

interface FilterDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    filters: AdvancedFilter[];
}

export const FilterDrawer: React.FC<FilterDrawerProps> = ({
    isOpen,
    onClose,
    filters,
}) => {
    return (
        <Offcanvas
            isOpen={isOpen}
            toggle={onClose}
            direction='end'
            style={{ width: '500px' }}
        >
            <OffcanvasHeader
                toggle={onClose}
                style={{
                    borderBottom: '1px solid #e9ebec',
                    padding: '1rem 1.5rem',
                }}
            >
                <div className='d-flex justify-content-between align-items-center w-100'>
                    <h5 className='mb-0'>Tất cả bộ lọc nâng cao</h5>
                    <Button
                        color='success'
                        size='sm'
                        style={{
                            backgroundColor: '#0ab39c',
                            border: 'none',
                            padding: '6px 12px',
                        }}
                    >
                        <i className='ri-add-line align-bottom me-1'></i>
                        Thêm bộ lọc
                    </Button>
                </div>
            </OffcanvasHeader>
            <OffcanvasBody>
                {filters.map((filter) => (
                    <div key={filter.id} className='mb-4'>
                        <h6 className='mb-3'>{filter.name}</h6>
                        <div className='d-flex align-items-center gap-2'>
                            <div style={{ flex: 1 }}>
                                <Input
                                    type='text'
                                    value={filter.conditions[0].value}
                                    readOnly
                                    style={{
                                        backgroundColor: 'white',
                                        border: '1px solid #e9ebec',
                                        height: '36px',
                                    }}
                                />
                            </div>
                            <Button
                                color='light'
                                size='sm'
                                className='btn-icon'
                                style={{ padding: '6px' }}
                            >
                                <i className='ri-pencil-line'></i>
                            </Button>
                            <Button
                                color='light'
                                size='sm'
                                className='btn-icon'
                                style={{ padding: '6px' }}
                            >
                                <i className='ri-delete-bin-line'></i>
                            </Button>
                        </div>
                    </div>
                ))}
            </OffcanvasBody>
        </Offcanvas>
    );
};
