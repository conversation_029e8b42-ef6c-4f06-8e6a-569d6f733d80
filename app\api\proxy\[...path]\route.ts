import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_API_URL || 'https://tmivcms.digins.vn';

// Helper function để tạo handler cho mỗi HTTP method
const createHandler =
    (method: string) =>
    async (
        request: NextRequest,
        context: { params: Promise<{ path: string[] }> },
    ) => {
        const { path } = await context.params;
        return handleRequest(request, path, method);
    };

export const GET = createHandler('GET');
export const POST = createHandler('POST');
export const PUT = createHandler('PUT');
export const DELETE = createHandler('DELETE');

async function handleRequest(
    request: NextRequest,
    path: string[],
    method: string,
) {
    try {
        const cookieStore = await cookies();
        const accessToken = cookieStore.get('access_token')?.value;

        // Build URL với query parameters
        const { searchParams } = new URL(request.url);
        const url = `${BACKEND_URL}/${path.join('/')}${searchParams.size ? `?${searchParams}` : ''}`;

        // Prepare headers
        const headers: HeadersInit = { 'Content-Type': 'application/json' };
        if (accessToken) {
            headers.Authorization = `Bearer ${accessToken}`;
        }

        // Prepare request options
        const options: RequestInit = { method, headers };
        if (['POST', 'PUT', 'DELETE'].includes(method)) {
            const body = await request.text();
            if (body) {
                options.body = body;
            }
        }

        // Fetch và return response
        const response = await fetch(url, options);
        const data = await response.text();

        return new NextResponse(data, {
            status: response.status,
            headers: { 'Content-Type': 'application/json' },
        });
    } catch (error) {
        console.error('Proxy error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 },
        );
    }
}
