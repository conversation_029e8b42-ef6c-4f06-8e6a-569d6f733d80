import FormController from '@/components/common/FormController';
import { useFormContext } from 'react-hook-form';
import { Table } from 'reactstrap';

interface Permission {
    all: boolean;
    view: boolean;
    update: boolean;
    delete: boolean;
    other: string;
}

interface FormData {
    permissions: Permission[];
}

const permissionData = [
    { name: '<PERSON>h<PERSON>ch hàng cá nhân' },
    { name: '<PERSON>h<PERSON><PERSON> hàng doanh nghiệp' },
    { name: 'Activity' },
    { name: '<PERSON><PERSON> hội bán hàng' },
    { name: 'Báo giá' },
    { name: '<PERSON><PERSON><PERSON> hàng' },
    { name: '<PERSON><PERSON><PERSON> đơn' },
    { name: '<PERSON>ản phẩm' },
    { name: '<PERSON>h<PERSON><PERSON> sản phẩm' },
    { name: '<PERSON>h<PERSON> cung cấp' },
    { name: 'Phiếu hỗ trợ' },
    { name: '<PERSON>h<PERSON>ch hàng' },
    { name: 'Chấm điểm tiềm năng' },
    { name: '<PERSON><PERSON>o cáo' },
];

const TabPermission = () => {
    const { register } = useFormContext<FormData>();

    return (
        <Table bordered responsive>
            <thead>
                <tr>
                    <th style={{ width: '300px' }}>Tên chức năng</th>
                    <th style={{ width: '100px', textAlign: 'center' }}>
                        Tất cả
                    </th>
                    <th style={{ width: '100px', textAlign: 'center' }}>Xem</th>
                    <th style={{ width: '100px', textAlign: 'center' }}>
                        Cập nhật
                    </th>
                    <th style={{ width: '100px', textAlign: 'center' }}>Xóa</th>
                    <th style={{ width: '150px', textAlign: 'center' }}>
                        Quyền khác
                    </th>
                </tr>
            </thead>
            <tbody>
                {permissionData.map((item, index) => (
                    <tr key={index}>
                        <td>{item.name}</td>
                        <td
                            style={{
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                height: '50px',
                            }}
                        >
                            <input
                                type='checkbox'
                                {...register(`permissions.${index}.all`)}
                                style={{
                                    width: '18px',
                                    height: '18px',
                                    margin: '0',
                                    position: 'relative',
                                    top: '2px',
                                }}
                            />
                        </td>
                        <td
                            style={{
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                height: '50px',
                            }}
                        >
                            <input
                                type='checkbox'
                                {...register(`permissions.${index}.view`)}
                                style={{
                                    width: '18px',
                                    height: '18px',
                                    margin: '0',
                                    position: 'relative',
                                    top: '2px',
                                }}
                            />
                        </td>
                        <td
                            style={{
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                height: '50px',
                            }}
                        >
                            <input
                                type='checkbox'
                                {...register(`permissions.${index}.update`)}
                                style={{
                                    width: '18px',
                                    height: '18px',
                                    margin: '0',
                                    position: 'relative',
                                    top: '2px',
                                }}
                            />
                        </td>
                        <td
                            style={{
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                height: '50px',
                            }}
                        >
                            <input
                                type='checkbox'
                                {...register(`permissions.${index}.delete`)}
                                style={{
                                    width: '18px',
                                    height: '18px',
                                    margin: '0',
                                    position: 'relative',
                                    top: '2px',
                                }}
                            />
                        </td>
                        <td className='text-center'>
                            <FormController
                                controlType='select'
                                name={`permissions.${index}.other`}
                                data={[
                                    { label: 'Quyền 1', value: '1' },
                                    { label: 'Quyền 2', value: '2' },
                                    { label: 'Quyền 3', value: '3' },
                                ]}
                            />
                        </td>
                    </tr>
                ))}
            </tbody>
        </Table>
    );
};

export default TabPermission;
