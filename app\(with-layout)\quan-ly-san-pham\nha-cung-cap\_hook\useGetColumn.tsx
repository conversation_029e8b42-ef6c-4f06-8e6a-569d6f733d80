import { ResponseSupplier } from '@/apis/supplier/supplier.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { ACTIONS } from '../_types/action.type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: ResponseSupplier | undefined,
    ) => void;
    page: string;
    onRestore?: (data: ResponseSupplier) => void;
}

const useGetColumn = ({
    onSelectedAction,
    page,
    onRestore,
}: GetColumnProps) => {
    const actions: DropdownAction<ResponseSupplier>[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(ACTIONS.VIEW_DETAIL, data),
            },
            {
                icon: 'ri-edit-line',
                label: 'Chỉnh sửa',
                onClick: (data) => onSelectedAction?.(ACTIONS.EDIT, data),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa',
                onClick: (data) => onSelectedAction?.(ACTIONS.DELETE, data),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );
    const getLeadStatusLabel = (status: string) => {
        if (status === 'Inactive') {
            return 'Ngừng hoạt động';
        }
        if (status === 'Active') {
            return 'Đang hoạt động';
        }
        return;
    };

    const getStatusStyle = (status: string) => {
        if (status === 'Inactive') {
            return {
                backgroundColor: '#f5f5f5',
                color: '#6c757d',
            };
        }
        if (status === 'Active') {
            return {
                backgroundColor: '#daf4f0',
                color: '#2fbeab',
            };
        }
        return {
            backgroundColor: '#f5f5f5',
            color: '#6c757d',
        };
    };
    const columns = useMemo<MRT_ColumnDef<ResponseSupplier>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên nhà cung cấp',
                enableHiding: false,
                size: 250,
                minSize: 200,
                maxSize: 350,
            },
            {
                accessorKey: 'description',
                header: 'Mô tả chung',
                size: 300,
                minSize: 200,
                maxSize: 320,
            },
            {
                accessorKey: 'commonStatusName',
                header: 'Trạng thái',
                size: 100,
                minSize: 100,
                maxSize: 120,
                enableSorting: false,
                enableColumnActions: false,
                Cell: ({ cell }) => {
                    const status = cell.getValue() as string;
                    const statusStyle = getStatusStyle(status);

                    return (
                        <span
                            className='badge me-1'
                            style={{
                                ...statusStyle,
                                display: 'inline-block',
                                textAlign: 'center',
                                padding: '4px 8px',
                                fontSize: '12px',
                                fontWeight: 500,
                                borderRadius: '4px',
                            }}
                        >
                            {getLeadStatusLabel(status)}
                        </span>
                    );
                },
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<ResponseSupplier>),
                size: 70,
                minSize: 50,
                maxSize: 100,
                enableColumnActions: false,
                Cell: ({ row }) => {
                    if (page === 'list') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                                style={{ marginLeft: '-50px' }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    return;
                },
            },
        ],
        [actions, page, onRestore],
    );

    return columns;
};

export default useGetColumn;
