import React from 'react';
import { Nav, NavItem, NavLink } from 'reactstrap';
import classnames from 'classnames';

export interface NavTabsProps {
    activeTab: string;
    toggleTab: (tab: string) => void;
}

const NavTabs: React.FC<NavTabsProps> = ({ activeTab, toggleTab }) => (
    <Nav tabs className='nav-tabs-custom' style={{ cursor: 'pointer' }}>
        <NavItem>
            <NavLink
                className={classnames({ active: activeTab === '1' })}
                onClick={() => toggleTab('1')}
            >
                Thông tin chung
            </NavLink>
        </NavItem>
        <NavItem>
            <NavLink
                className={classnames({ active: activeTab === '2' })}
                onClick={() => toggleTab('2')}
            >
                Địa chỉ
            </NavLink>
        </NavItem>
        <NavItem>
            <NavLink
                className={classnames({ active: activeTab === '3' })}
                onClick={() => toggleTab('3')}
            >
                <PERSON><PERSON> và mua hàng
            </NavLink>
        </NavItem>
        <NavItem>
            <NavLink
                className={classnames({ active: activeTab === '4' })}
                onClick={() => toggleTab('4')}
            >
                Thanh toán và hóa đơn
            </NavLink>
        </NavItem>
    </Nav>
);

export default NavTabs;
