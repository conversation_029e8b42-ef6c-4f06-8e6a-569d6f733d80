import {
    ClassificationNeedType,
    DealType,
    InstallationType,
    Priority,
    RequestDemo,
} from './types';

// Phân loại nhu cầu
export const classificationNeedOptions = [
    {
        value: ClassificationNeedType.LearnProduct.toString(),
        label: 'Tìm hiểu về sản phẩm',
    },
    {
        value: ClassificationNeedType.ProductSpecific.toString(),
        label: 'Tư vấn cụ thể sản phẩm',
    },
    {
        value: ClassificationNeedType.CompareProducts.toString(),
        label: 'So sánh sản phẩm',
    },
    {
        value: ClassificationNeedType.CustomerCare.toString(),
        label: 'Chăm sóc khách hàng',
    },
];

// Giai đoạn
export const stages = [
    {
        value: 1,
        label: 'Giới thiệu sản phẩm',
    },
    {
        value: 2,
        label: 'Tư vấn sản phẩm',
    },
    {
        value: 3,
        label: 'Tạo và gửi báo giá',
    },
];

// Trạng thái
export const requestDemoOptions = [
    {
        value: RequestDemo.Yes.toString(),
        label: 'Có',
    },
    {
        value: RequestDemo.No.toString(),
        label: 'Không',
    },
    {
        value: RequestDemo.None.toString(),
        label: 'Không có thông tin',
    },
];

// Trạng thái
export const dealTypeOptions = [
    { value: DealType.Classic.toString(), label: 'Classic' },
    { value: DealType.Cyber.toString(), label: 'Cyber Security' },
];

export const InstallationOptions = [
    {
        label: 'On-Preme',
        value: InstallationType.OnPremises.toString(),
    },
    {
        label: 'Cloud',
        value: InstallationType.Cloud.toString(),
    },
];

export const PriorityOptions = [
    {
        label: 'Thấp',
        value: Priority.Low.toString(),
    },
    {
        label: 'Trung bình',
        value: Priority.Medium.toString(),
    },
    {
        label: 'Cao',
        value: Priority.High.toString(),
    },
    {
        label: 'Rất cao',
        value: Priority.Critical.toString(),
    },
];
