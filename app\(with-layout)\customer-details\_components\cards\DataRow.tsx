import React from 'react';
import { Badge } from 'reactstrap';

export interface DataRowProps {
    label: string;
    value: string;
    badgeText?: string;
}

const DataRow: React.FC<DataRowProps> = ({ label, value, badgeText }) => (
    <div className='mb-2'>
        <div className='text-muted'>{label}</div>
        <div>
            {value}
            {badgeText && (
                <Badge color='black' className='ms-1'>
                    {badgeText}
                </Badge>
            )}
        </div>
    </div>
);

export default DataRow;
